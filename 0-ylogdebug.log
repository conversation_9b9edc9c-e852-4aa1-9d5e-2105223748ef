ylogdebug_0  on 08-07 11:35:34


uptime on Thu Aug  7 11:35:34 CST 2025
 11:35:34 up 0 min,  0 users,  load average: 3.18, 0.68, 0.22
run finished on 08-07 11:35:34


logcat -S on Thu Aug  7 11:35:34 CST 2025
size/num main               system             crash              kernel             Total
Total    40527/299          7738/77            0/0                626019/5909        674284/6285
Now      40527/299          7738/77            0/0                626019/5909        674284/6285
Logspan  6.741              6.636                                 6.753              6.753
Overhead 65616              65616                                 200625             336063

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                                19796
  PID/UID   COMMAND LINE                                       "
  499/1000  /apex/com.android.sdkext/bin/derive_classpath   10086
  379/1000 ...<EMAIL> 5385
  291/1000  /system/system_ext/bin/hwservicemanager          3753
  290/1000  /system/bin/servicemanager                        311
0     root                                                  18106
9999  nobody                                                 1465
1036  auditd                                                  376
1017  keystore                                                360
1069  lmkd                                                    273


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
0     root                                                   3492
1017  keystore                                               3360
1000  system                                                  886
  PID/UID   COMMAND LINE                                       "
  515/1000  /product/bin/ylog                                 766
  536/1000  /product/bin/ylogkat                              120

run finished on 08-07 11:35:35


ylogctl q on Thu Aug  7 11:35:35 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 45056 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      0] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[      8] 
[   536]  android      -> Open    -> android.log      [      4]->[      0] 
[   539]  kernel       -> Open    -> kernel.log       [     36]->[      1] 
[   540]  trace        -> Open    -> trace.log        [      0]->[      0] 
[   541]  sgm          -> Open    -> sgm.csv          [      1]->[      0] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [      C]->[      0] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [      F]->[      0] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     14]->[      0] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [      5]->[      0] 

run finished on 08-07 11:35:35


ylogctl space on Thu Aug  7 11:35:35 CST 2025
Root:/data/ylog/ap/   APLogSize:44 APLogMaxSize:46570 DiskFreeSpace:38841  DiskReserved:60
run finished on 08-07 11:35:35


cat /data/ylog/ylog.conf on Thu Aug  7 11:35:35 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 11:35:36


ls -l /data/ylog/ap/ on Thu Aug  7 11:35:36 CST 2025
total 46220
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>   655737 2025-08-07 11:35 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:34 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:34 tcpdump
run finished on 08-07 11:35:36


cat /data/ylog/journal.log on Thu Aug  7 11:35:36 CST 2025
[01-01 08:12:03.662] (     1) [    14]     524     524  

.............................
[01-01 08:12:03.666] (     2) [    14]     524     524  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[01-01 08:12:03.670] (     3) [    14]     524     524  /data/ylog/ylog.conf error,reinit it
[01-01 08:12:03.672] (     4) [    14]     524     524  ylog config is /product/etc/ylog.conf.debug
[01-01 08:12:03.679] (     5) [    14]     524     524  syncLegcyConfig
[01-01 08:12:03.681] (     6) [    14]     524     524  startlogServcie LogEnable : 1
[01-01 08:12:03.682] (     7) [    14]     524     524  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[01-01 08:12:03.686] (     8) [    14]     524     524  setSubLog onOff:0 logType:lastlog
[01-01 08:12:03.686] (     9) [    14]     524     524  setSubLog onOff:0 logType:uboot
[01-01 08:12:03.686] (    10) [    14]     524     524  setSubLog onOff:0 logType:android
[01-01 08:12:03.686] (    11) [    14]     524     524  setSubLog onOff:0 logType:kernel
[01-01 08:12:03.687] (    12) [    14]     524     524  setSubLog onOff:0 logType:trace
[01-01 08:12:03.687] (    13) [    14]     524     524  setSubLog onOff:0 logType:sgm
[01-01 08:12:03.687] (    14) [    14]     524     524  setSubLog onOff:0 logType:sysinfo
[01-01 08:12:03.687] (    15) [    14]     524     524  setSubLog onOff:0 logType:thermal
[01-01 08:12:03.687] (    16) [    14]     524     524  setSubLog onOff:0 logType:ylogdebug
[01-01 08:12:03.687] (    17) [    14]     524     524  setSubLog onOff:0 logType:phoneinfo
[01-01 08:12:03.687] (    18) [    14]     524     524  setSubLog onOff:1 logType:hcidump
[01-01 08:12:03.687] (    19) [    14]     524     524  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[01-01 08:12:03.687] (    20) [    14]     524     524  setSubLog onOff:1 logType:tcpdump
[01-01 08:12:03.687] (    21) [    14]     524     524  setSubLog onOff:0 logType:trustlog
[01-01 08:12:03.690] (    22) [    14]     524     524  syncLegcyConfig
[01-01 08:12:03.690] (    23) [    14]     524     524  SubLog OnOff:0, logType lastlog is 1
[01-01 08:12:03.690] (    24) [    14]     524     524  SubLog OnOff:0, logType uboot is 1
[01-01 08:12:03.690] (    25) [    14]     524     524  SubLog OnOff:0, logType android is 1
[01-01 08:12:03.690] (    26) [    14]     524     524  SubLog OnOff:0, logType kernel is 1
[01-01 08:12:03.690] (    27) [    14]     524     524  SubLog OnOff:0, logType trace is 1
[01-01 08:12:03.690] (    28) [    14]     524     524  SubLog OnOff:0, logType sgm is 1
[01-01 08:12:03.690] (    29) [    14]     524     524  SubLog OnOff:0, logType sysinfo is 1
[01-01 08:12:03.690] (    30) [    14]     524     524  SubLog OnOff:0, logType thermal is 0
[01-01 08:12:03.690] (    31) [    14]     524     524  SubLog OnOff:0, logType ylogdebug is 1
[01-01 08:12:03.690] (    32) [    14]     524     524  SubLog OnOff:0, logType phoneinfo is 1
[01-01 08:12:03.690] (    33) [    14]     524     524  SubLog OnOff:1, logType hcidump is 1
[01-01 08:12:03.690] (    34) [    14]     524     524  SubLog OnOff:1, logType tcpdump is 1
[01-01 08:12:03.690] (    35) [    14]     524     524  SubLog OnOff:0, logType trustlog is 1
[01-01 08:12:03.691] (    36) [    14]     524     524  index:11,logType:tcpdump, logSize:256, totalSize:4096
[01-01 08:12:03.691] (    37) [    14]     524     524  index:10,logType:hcidump, logSize:64, totalSize:1024
[01-01 08:12:03.691] (    38) [    14]     524     524  value:default
[01-01 08:12:03.692] (    39) [    14]     524     524  make dir:/data/ylog/ap/
[01-01 08:12:03.692] (    40) [    14]     524     524  mSubLog:1
[01-01 08:12:03.692] (    41) [    14]     524     524  mSubLog:1
[01-01 08:12:03.693] (    42) [    14]     524     524  logSourceCnt:13 compressLevel:3
[01-01 08:12:03.694] (    43) [    14]     524     536  [lastlog]  configure is [1]
[01-01 08:12:03.699] (    44) [    14]     524     536  [uboot]  configure is [1]
[01-01 08:12:03.702] (    45) [    14]     524     536  [android]  configure is [1]
[01-01 08:12:03.707] (    46) [    14]     524     536  [kernel]  configure is [1]
[01-01 08:12:03.715] (    47) [    14]     524     536  [trace]  configure is [1]
[01-01 08:12:03.725] (     2) [    14]     532     532  aplogfilesize : 256
[01-01 08:12:03.726] (     2) [    14]     532     550  LogReboot:startrebootServcie
[01-01 08:12:03.728] (     3) [    14]     532     532  srootdir : default/
[01-01 08:12:03.728] (     4) [    14]     532     532  aplogmaxsize : 99%
[01-01 08:12:03.728] (     5) [    14]     532     532  aplogrotate : 1
[01-01 08:12:03.729] (     6) [    14]     532     532  prioritypath : 0
[01-01 08:12:03.731] (     7) [    14]     532     550  mkdir /data/log/reliability/dumplog/ success
[01-01 08:12:03.735] (    48) [    14]     524     536  [sgm]  configure is [1]
[01-01 08:12:03.774] (    49) [    14]     524     536  [sysinfo]  configure is [1]
[01-01 08:12:03.792] (    50) [    14]     524     536  [thermal]  configure is [0]
[01-01 08:12:03.792] (    51) [    14]     524     536  [ylogdebug]  configure is [1]
[01-01 08:12:03.812] (    52) [    14]     524     536  [phoneinfo]  configure is [1]
[01-01 08:12:03.824] (    53) [    14]     524     536  [hcidump]  configure is [1]
[01-01 08:12:03.839] (    54) [    14]     524     536  [tcpdump]  configure is [1]
[01-01 08:12:03.877] (    55) [    14]     524     536  [trustlog]  configure is [1]
[01-01 08:12:03.897] (    56) [    14]     524     536  listen to 12 source 
[01-01 08:12:03.949] (    57) [    14]     524     536  index:11 log buffer is null 
[01-01 08:12:03.950] (    58) [    14]     524     536  index:12 log buffer is null 
[01-01 08:12:04.428] (     8) [    15]     532     532  mount changed: [] -> [/data/]
[01-01 08:12:04.431] (     9) [    15]     532     532  set logdir to /data/ylog/ap/, diskfree:46278
[01-01 08:12:04.433] (    10) [    15]     532     532  last ylog file  [] not exsit,backup mmap file
[01-01 08:12:04.434] (    11) [    15]     532     532  last ylog not exit, backupMmapData error.
[01-01 08:12:04.434] (    12) [    15]     532     633  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[01-01 08:12:04.438] (    13) [    15]     532     532  create first file
[01-01 08:12:04.440] (    14) [    15]     532     532  get new  file name(new logfile) : /data/ylog/ap/000-0101_081204_poweron.ylog 
[01-01 08:12:04.440] (    15) [    15]     532     532  open log file:/data/ylog/ap/000-0101_081204_poweron.ylog fd:24 diskfree:46278
[01-01 08:12:04.442] (    16) [    15]     532     633  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
[01-01 08:12:04.444] (    17) [    15]     532     532  update UID file:/data/ylog/loguid=0
[01-01 08:12:04.445] (    59) [    15]     524     529  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[12-31 21:12:06.992] (    60) [    17]     524     535  make dir /data/ylog/ap/tcpdump/
[12-31 21:12:07.037] (    61) [    17]     524     535  open new log:/data/ylog/ap/tcpdump/001_1231_211207_tcpdump.cap, wfd:34, logname:/data/ylog/ap/tcpdump/001_1231_211207_tcpdump.cap
[12-31 21:12:07.037] (    62) [    17]     524     535  logType->totalwriten:0 sublogsize:0
[12-31 21:12:08.733] (    18) [    19]     532     550  currentTime: 19691231211208-37023337
[12-31 21:12:08.736] (    19) [    19]     532     550  SystemBootMode::LINUXKERNEL
[12-31 21:12:08.737] (    20) [    19]     532     550  boot_cause: Pbint triggered
[12-31 21:12:08.737] (    21) [    19]     532     550  boot_reason: normalboot
[12-31 21:12:08.737] (    22) [    19]     532     550  boot_category: normalboot
[12-31 21:12:08.741] (    23) [    19]     532     550  Crash_reason: Normal
[12-31 21:12:08.751] (    24) [    19]     532     950  open /dev/block/by-name/sd_klog failed No such file or directory
[08-06 06:56:19.397] (     1) [    11]     520     520  

.............................
[08-06 06:56:19.399] (     2) [    11]     520     520  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[08-06 06:56:19.407] (     3) [    11]     520     520  syncLegcyConfig
[08-06 06:56:19.413] (     4) [    11]     520     520  startlogServcie LogEnable : 1
[08-06 06:56:19.414] (     5) [    11]     520     520  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[08-06 06:56:19.416] (     6) [    11]     520     520  setSubLog onOff:0 logType:lastlog
[08-06 06:56:19.416] (     7) [    11]     520     520  setSubLog onOff:0 logType:uboot
[08-06 06:56:19.416] (     8) [    11]     520     520  setSubLog onOff:0 logType:android
[08-06 06:56:19.416] (     9) [    11]     520     520  setSubLog onOff:0 logType:kernel
[08-06 06:56:19.416] (    10) [    11]     520     520  setSubLog onOff:0 logType:trace
[08-06 06:56:19.416] (    11) [    11]     520     520  setSubLog onOff:0 logType:sgm
[08-06 06:56:19.416] (    12) [    11]     520     520  setSubLog onOff:0 logType:sysinfo
[08-06 06:56:19.416] (    13) [    11]     520     520  setSubLog onOff:0 logType:thermal
[08-06 06:56:19.416] (    14) [    11]     520     520  setSubLog onOff:0 logType:ylogdebug
[08-06 06:56:19.416] (    15) [    11]     520     520  setSubLog onOff:0 logType:phoneinfo
[08-06 06:56:19.416] (    16) [    11]     520     520  setSubLog onOff:1 logType:hcidump
[08-06 06:56:19.416] (    17) [    11]     520     520  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[08-06 06:56:19.416] (    18) [    11]     520     520  setSubLog onOff:1 logType:tcpdump
[08-06 06:56:19.416] (    19) [    11]     520     520  setSubLog onOff:0 logType:trustlog
[08-06 06:56:19.420] (    20) [    11]     520     520  syncLegcyConfig
[08-06 06:56:19.420] (    21) [    11]     520     520  SubLog OnOff:0, logType lastlog is 1
[08-06 06:56:19.420] (    22) [    11]     520     520  SubLog OnOff:0, logType uboot is 1
[08-06 06:56:19.420] (    23) [    11]     520     520  SubLog OnOff:0, logType android is 1
[08-06 06:56:19.420] (    24) [    11]     520     520  SubLog OnOff:0, logType kernel is 1
[08-06 06:56:19.420] (    25) [    11]     520     520  SubLog OnOff:0, logType trace is 1
[08-06 06:56:19.420] (    26) [    11]     520     520  SubLog OnOff:0, logType sgm is 1
[08-06 06:56:19.420] (    27) [    11]     520     520  SubLog OnOff:0, logType sysinfo is 1
[08-06 06:56:19.420] (    28) [    11]     520     520  SubLog OnOff:0, logType thermal is 0
[08-06 06:56:19.420] (    29) [    11]     520     520  SubLog OnOff:0, logType ylogdebug is 1
[08-06 06:56:19.420] (    30) [    11]     520     520  SubLog OnOff:0, logType phoneinfo is 1
[08-06 06:56:19.420] (    31) [    11]     520     520  SubLog OnOff:1, logType hcidump is 1
[08-06 06:56:19.420] (    32) [    11]     520     520  SubLog OnOff:1, logType tcpdump is 1
[08-06 06:56:19.420] (    33) [    11]     520     520  SubLog OnOff:0, logType trustlog is 1
[08-06 06:56:19.421] (    34) [    11]     520     520  index:11,logType:tcpdump, logSize:256, totalSize:4096
[08-06 06:56:19.421] (    35) [    11]     520     520  index:10,logType:hcidump, logSize:64, totalSize:1024
[08-06 06:56:19.421] (    36) [    11]     520     520  value:default
[08-06 06:56:19.422] (    37) [    11]     520     520  mSubLog:1
[08-06 06:56:19.422] (    38) [    11]     520     520  mSubLog:1
[08-06 06:56:19.422] (    39) [    11]     520     520  logSourceCnt:13 compressLevel:3
[08-06 06:56:19.423] (    40) [    11]     520     533  [lastlog]  configure is [1]
[08-06 06:56:19.428] (    41) [    11]     520     533  [uboot]  configure is [1]
[08-06 06:56:19.432] (    42) [    11]     520     533  [android]  configure is [1]
[08-06 06:56:19.435] (    43) [    11]     520     533  [kernel]  configure is [1]
[08-06 06:56:19.441] (    44) [    11]     520     533  [trace]  configure is [1]
[08-06 06:56:19.451] (    45) [    11]     520     533  [sgm]  configure is [1]
[08-06 06:56:19.453] (     1) [    11]     529     529  aplogfilesize : 256
[08-06 06:56:19.454] (     2) [    11]     529     529  srootdir : default/
[08-06 06:56:19.458] (     3) [    11]     529     529  aplogmaxsize : 99%
[08-06 06:56:19.458] (     4) [    11]     529     529  aplogrotate : 1
[08-06 06:56:19.458] (     5) [    11]     529     529  prioritypath : 0
[08-06 06:56:19.462] (     6) [    11]     529     548  LogReboot:startrebootServcie
[08-06 06:56:19.465] (    46) [    11]     520     533  [sysinfo]  configure is [1]
[08-06 06:56:19.493] (    47) [    11]     520     533  [thermal]  configure is [0]
[08-06 06:56:19.494] (    48) [    11]     520     533  [ylogdebug]  configure is [1]
[08-06 06:56:19.507] (    49) [    11]     520     533  [phoneinfo]  configure is [1]
[08-06 06:56:19.542] (    50) [    11]     520     533  [hcidump]  configure is [1]
[08-06 06:56:19.560] (    51) [    11]     520     533  [tcpdump]  configure is [1]
[08-06 06:56:19.604] (    52) [    11]     520     533  [trustlog]  configure is [1]
[08-06 06:56:19.618] (    53) [    11]     520     533  listen to 12 source 
[08-06 06:56:19.702] (    54) [    11]     520     533  index:11 log buffer is null 
[08-06 06:56:19.704] (    55) [    11]     520     533  index:12 log buffer is null 
[08-06 06:56:20.166] (     7) [    11]     529     529  mount changed: [] -> [/data/]
[08-06 06:56:20.170] (     8) [    11]     529     529  set logdir to /data/ylog/ap/, diskfree:45579
[08-06 06:56:20.178] (     9) [    11]     529     529  last ylog file  [] not exsit,backup mmap file
[08-06 06:56:20.178] (    10) [    11]     529     529  last ylog not exit, backupMmapData error.
[08-06 06:56:20.185] (    11) [    11]     529     529  create first file
[08-06 06:56:20.191] (    12) [    11]     529     529  get new  file name(new logfile) : /data/ylog/ap/001-0806_065620_poweron.ylog 
[08-06 06:56:20.191] (    13) [    11]     529     529  open log file:/data/ylog/ap/001-0806_065620_poweron.ylog fd:23 diskfree:45579
[08-06 06:56:20.201] (    14) [    11]     529     529  update UID file:/data/ylog/loguid=1
[08-06 06:56:20.204] (    56) [    11]     520     526  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[08-06 06:56:21.033] (    15) [    12]     529     622  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[08-06 06:56:21.077] (    16) [    12]     529     622  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
[08-06 06:56:22.792] (    57) [    14]     520     532  open new log:/data/ylog/ap/tcpdump/002_0806_065622_tcpdump.cap, wfd:34, logname:/data/ylog/ap/tcpdump/002_0806_065622_tcpdump.cap
[08-06 06:56:22.793] (    58) [    14]     520     532  logType->totalwriten:0 sublogsize:0
[08-06 06:56:24.471] (    17) [    16]     529     548  currentTime: 20250806065624-10584160
[08-06 06:56:24.484] (    18) [    16]     529     548  SystemBootMode::LINUXKERNEL
[08-06 06:56:24.484] (    19) [    16]     529     548  boot_cause: Pbint triggered
[08-06 06:56:24.485] (    20) [    16]     529     548  boot_reason: normalboot
[08-06 06:56:24.485] (    21) [    16]     529     548  boot_category: normalboot
[08-06 06:56:24.493] (    22) [    16]     529     548  Crash_reason: Normal
[08-06 06:56:24.519] (    23) [    16]     529     939  open /dev/block/by-name/sd_klog failed No such file or directory
[08-06 06:56:50.149] (    59) [    41]     520     532  make dir /data/ylog/ap/hcidump/
[08-06 06:56:50.201] (    60) [    41]     520     532  open new log:/data/ylog/ap/hcidump/001_0806_065650_hcidump.cap, wfd:35, logname:/data/ylog/ap/hcidump/001_0806_065650_hcidump.cap
[08-06 06:56:50.202] (    61) [    41]     520     532  logType->totalwriten:0 sublogsize:0
[08-07 10:30:00.039] (    62) [  3671]     520     530  
 1494, 1152, 1352, 1635, 1191, 2314, 1613, 1749, 1252, 2301, 1823, 1727, 1333, 1573, 1372, 1497, 1215, 1150, 1436, 1155, 1507, 1148, 1455, 1153, 1432, 1456, 1621, 1220, 1159, 1447, 1196,    0, 2292, 2618, 1583, 1739, 1294, 1485, 1089, 1129, 1347, 1094, 1373, 1088, 1379, 1106, 1339, 1077, 1366, 1104, 1307, 1160, 1085, 1382,    0, 1110, 1345, 1063, 1366, 1073,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[08-07 11:31:00.113] (    63) [  7331]     520     530  
 1198, 1475, 1188, 1286, 1483, 1228, 1449, 1146, 1459, 1210, 1448, 1172, 1459, 1163, 1437, 1225, 1164, 1456, 1168, 1454, 1193, 1440, 1170, 1455, 1155, 1492, 1161, 1144, 1472, 1165, 1472, 1195, 1146, 1495, 1154, 1473, 1162, 1442, 1155, 1226, 1444, 1159, 1450, 1169, 1489, 1172, 1467, 1161, 1455, 1155, 1615, 1159, 1233, 1780, 1571, 1605, 1164, 1456, 1168, 1799,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[08-07 11:34:10.466] (     1) [    11]     525     525  

.............................
[08-07 11:34:10.468] (     2) [    11]     525     525  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[08-07 11:34:10.477] (     3) [    11]     525     525  syncLegcyConfig
[08-07 11:34:10.479] (     4) [    11]     525     525  startlogServcie LogEnable : 1
[08-07 11:34:10.479] (     5) [    11]     525     525  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[08-07 11:34:10.484] (     6) [    11]     525     525  setSubLog onOff:0 logType:lastlog
[08-07 11:34:10.484] (     7) [    11]     525     525  setSubLog onOff:0 logType:uboot
[08-07 11:34:10.484] (     8) [    11]     525     525  setSubLog onOff:0 logType:android
[08-07 11:34:10.484] (     9) [    11]     525     525  setSubLog onOff:0 logType:kernel
[08-07 11:34:10.484] (    10) [    11]     525     525  setSubLog onOff:0 logType:trace
[08-07 11:34:10.484] (    11) [    11]     525     525  setSubLog onOff:0 logType:sgm
[08-07 11:34:10.484] (    12) [    11]     525     525  setSubLog onOff:0 logType:sysinfo
[08-07 11:34:10.484] (    13) [    11]     525     525  setSubLog onOff:0 logType:thermal
[08-07 11:34:10.484] (    14) [    11]     525     525  setSubLog onOff:0 logType:ylogdebug
[08-07 11:34:10.484] (    15) [    11]     525     525  setSubLog onOff:0 logType:phoneinfo
[08-07 11:34:10.484] (    16) [    11]     525     525  setSubLog onOff:1 logType:hcidump
[08-07 11:34:10.484] (    17) [    11]     525     525  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[08-07 11:34:10.484] (    18) [    11]     525     525  setSubLog onOff:1 logType:tcpdump
[08-07 11:34:10.484] (    19) [    11]     525     525  setSubLog onOff:0 logType:trustlog
[08-07 11:34:10.487] (    20) [    11]     525     525  syncLegcyConfig
[08-07 11:34:10.488] (    21) [    11]     525     525  SubLog OnOff:0, logType lastlog is 1
[08-07 11:34:10.488] (    22) [    11]     525     525  SubLog OnOff:0, logType uboot is 1
[08-07 11:34:10.488] (    23) [    11]     525     525  SubLog OnOff:0, logType android is 1
[08-07 11:34:10.488] (    24) [    11]     525     525  SubLog OnOff:0, logType kernel is 1
[08-07 11:34:10.488] (    25) [    11]     525     525  SubLog OnOff:0, logType trace is 1
[08-07 11:34:10.488] (    26) [    11]     525     525  SubLog OnOff:0, logType sgm is 1
[08-07 11:34:10.488] (    27) [    11]     525     525  SubLog OnOff:0, logType sysinfo is 1
[08-07 11:34:10.488] (    28) [    11]     525     525  SubLog OnOff:0, logType thermal is 0
[08-07 11:34:10.488] (    29) [    11]     525     525  SubLog OnOff:0, logType ylogdebug is 1
[08-07 11:34:10.488] (    30) [    11]     525     525  SubLog OnOff:0, logType phoneinfo is 1
[08-07 11:34:10.488] (    31) [    11]     525     525  SubLog OnOff:1, logType hcidump is 1
[08-07 11:34:10.488] (    32) [    11]     525     525  SubLog OnOff:1, logType tcpdump is 1
[08-07 11:34:10.488] (    33) [    11]     525     525  SubLog OnOff:0, logType trustlog is 1
[08-07 11:34:10.489] (    34) [    11]     525     525  index:11,logType:tcpdump, logSize:256, totalSize:4096
[08-07 11:34:10.489] (    35) [    11]     525     525  index:10,logType:hcidump, logSize:64, totalSize:1024
[08-07 11:34:10.490] (    36) [    11]     525     525  value:default
[08-07 11:34:10.491] (    37) [    11]     525     525  mSubLog:1
[08-07 11:34:10.491] (    38) [    11]     525     525  mSubLog:1
[08-07 11:34:10.491] (    39) [    11]     525     525  logSourceCnt:13 compressLevel:3
[08-07 11:34:10.493] (    40) [    11]     525     538  [lastlog]  configure is [1]
[08-07 11:34:10.498] (    41) [    11]     525     538  [uboot]  configure is [1]
[08-07 11:34:10.504] (    42) [    11]     525     538  [android]  configure is [1]
[08-07 11:34:10.506] (    43) [    11]     525     538  [kernel]  configure is [1]
[08-07 11:34:10.510] (    44) [    11]     525     538  [trace]  configure is [1]
[08-07 11:34:10.515] (    45) [    11]     525     538  [sgm]  configure is [1]
[08-07 11:34:10.521] (     1) [    11]     534     534  aplogfilesize : 256
[08-07 11:34:10.522] (     2) [    11]     534     534  srootdir : default/
[08-07 11:34:10.522] (     3) [    11]     534     534  aplogmaxsize : 99%
[08-07 11:34:10.522] (     4) [    11]     534     534  aplogrotate : 1
[08-07 11:34:10.522] (     5) [    11]     534     534  prioritypath : 0
[08-07 11:34:10.536] (     6) [    11]     534     552  LogReboot:startrebootServcie
[08-07 11:34:10.545] (    46) [    11]     525     538  [sysinfo]  configure is [1]
[08-07 11:34:10.561] (    47) [    11]     525     538  [thermal]  configure is [0]
[08-07 11:34:10.561] (    48) [    11]     525     538  [ylogdebug]  configure is [1]
[08-07 11:34:10.585] (    49) [    11]     525     538  [phoneinfo]  configure is [1]
[08-07 11:34:10.617] (    50) [    11]     525     538  [hcidump]  configure is [1]
[08-07 11:34:10.649] (    51) [    11]     525     538  [tcpdump]  configure is [1]
[08-07 11:34:10.677] (    52) [    11]     525     538  [trustlog]  configure is [1]
[08-07 11:34:10.689] (    53) [    11]     525     538  listen to 12 source 
[08-07 11:34:10.776] (    54) [    11]     525     538  index:11 log buffer is null 
[08-07 11:34:10.778] (    55) [    11]     525     538  index:12 log buffer is null 
[08-07 11:34:11.230] (     7) [    11]     534     534  mount changed: [] -> [/data/]
[08-07 11:34:11.232] (     8) [    11]     534     534  set logdir to /data/ylog/ap/, diskfree:38852
[08-07 11:34:11.234] (     9) [    11]     534     534  last ylog file  [] not exsit,backup mmap file
[08-07 11:34:11.234] (    10) [    11]     534     534  last ylog not exit, backupMmapData error.
[08-07 11:34:11.250] (    11) [    11]     534     632  copyBlackBox rm exist /data/ylog/ap/blackboxlog
[08-07 11:34:11.256] (    12) [    11]     534     534  create first file
[08-07 11:34:11.259] (    13) [    11]     534     534  get new  file name(new logfile) : /data/ylog/ap/002-0807_113411_poweron.ylog 
[08-07 11:34:11.259] (    14) [    11]     534     534  open log file:/data/ylog/ap/002-0807_113411_poweron.ylog fd:23 diskfree:38856
[08-07 11:34:11.286] (    15) [    11]     534     534  update UID file:/data/ylog/loguid=2
[08-07 11:34:11.287] (    56) [    11]     525     531  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[08-07 11:34:12.013] (    16) [    12]     534     632  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[08-07 11:34:12.013] (    17) [    12]     534     632  copyCache rm exist /data/ylog/ap/cachelog
[08-07 11:34:12.046] (    18) [    12]     534     632  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
[08-07 11:34:13.855] (    57) [    14]     525     537  open new log:/data/ylog/ap/tcpdump/003_0807_113413_tcpdump.cap, wfd:34, logname:/data/ylog/ap/tcpdump/003_0807_113413_tcpdump.cap
[08-07 11:34:13.856] (    58) [    14]     525     537  logType->totalwriten:0 sublogsize:252
[08-07 11:34:15.539] (    19) [    16]     534     552  currentTime: 20250807113415-16793933
[08-07 11:34:15.549] (    20) [    16]     534     552  SystemBootMode::LINUXKERNEL
[08-07 11:34:15.550] (    21) [    16]     534     552  boot_cause: Reboot into normal
[08-07 11:34:15.550] (    22) [    16]     534     552  boot_reason: normalboot
[08-07 11:34:15.550] (    23) [    16]     534     552  boot_category: normalboot
[08-07 11:34:15.555] (    24) [    16]     534     940  open /dev/block/by-name/sd_klog failed No such file or directory
[08-07 11:34:15.556] (    25) [    16]     534     552  Crash_reason: Normal
[08-07 11:35:03.576] (    59) [    64]     525     533  __ERROR  writer pid:534 terminated abnormally, signal 15
 error  [0(Success)] logFileWriterProcessSpawnThread
[08-07 11:35:34.396] (     1) [    11]     515     515  

.............................
[08-07 11:35:34.398] (     2) [    11]     515     515  [1] ylog()  start enable:0  ver:5.0.0   bootmode:normal lite:0 sdk:35 plat:
[08-07 11:35:34.404] (     3) [    11]     515     515  syncLegcyConfig
[08-07 11:35:34.407] (     4) [    11]     515     515  startlogServcie LogEnable : 1
[08-07 11:35:34.407] (     5) [    11]     515     515  __ERROR  write [READER_CMD_REMOVEALL main] error  [9(Bad file descriptor)] sendCmd
[08-07 11:35:34.412] (     6) [    11]     515     515  setSubLog onOff:0 logType:lastlog
[08-07 11:35:34.412] (     7) [    11]     515     515  setSubLog onOff:0 logType:uboot
[08-07 11:35:34.412] (     8) [    11]     515     515  setSubLog onOff:0 logType:android
[08-07 11:35:34.412] (     9) [    11]     515     515  setSubLog onOff:0 logType:kernel
[08-07 11:35:34.412] (    10) [    11]     515     515  setSubLog onOff:0 logType:trace
[08-07 11:35:34.412] (    11) [    11]     515     515  setSubLog onOff:0 logType:sgm
[08-07 11:35:34.412] (    12) [    11]     515     515  setSubLog onOff:0 logType:sysinfo
[08-07 11:35:34.412] (    13) [    11]     515     515  setSubLog onOff:0 logType:thermal
[08-07 11:35:34.412] (    14) [    11]     515     515  setSubLog onOff:0 logType:ylogdebug
[08-07 11:35:34.412] (    15) [    11]     515     515  setSubLog onOff:0 logType:phoneinfo
[08-07 11:35:34.412] (    16) [    11]     515     515  setSubLog onOff:1 logType:hcidump
[08-07 11:35:34.412] (    17) [    11]     515     515  [tcpdump] set bin to [tcpdump -i any -p   -U -w - -s 0 -s 3000]
[08-07 11:35:34.412] (    18) [    11]     515     515  setSubLog onOff:1 logType:tcpdump
[08-07 11:35:34.412] (    19) [    11]     515     515  setSubLog onOff:0 logType:trustlog
[08-07 11:35:34.416] (    20) [    11]     515     515  syncLegcyConfig
[08-07 11:35:34.416] (    21) [    11]     515     515  SubLog OnOff:0, logType lastlog is 1
[08-07 11:35:34.416] (    22) [    11]     515     515  SubLog OnOff:0, logType uboot is 1
[08-07 11:35:34.416] (    23) [    11]     515     515  SubLog OnOff:0, logType android is 1
[08-07 11:35:34.416] (    24) [    11]     515     515  SubLog OnOff:0, logType kernel is 1
[08-07 11:35:34.416] (    25) [    11]     515     515  SubLog OnOff:0, logType trace is 1
[08-07 11:35:34.416] (    26) [    11]     515     515  SubLog OnOff:0, logType sgm is 1
[08-07 11:35:34.416] (    27) [    11]     515     515  SubLog OnOff:0, logType sysinfo is 1
[08-07 11:35:34.416] (    28) [    11]     515     515  SubLog OnOff:0, logType thermal is 0
[08-07 11:35:34.416] (    29) [    11]     515     515  SubLog OnOff:0, logType ylogdebug is 1
[08-07 11:35:34.416] (    30) [    11]     515     515  SubLog OnOff:0, logType phoneinfo is 1
[08-07 11:35:34.416] (    31) [    11]     515     515  SubLog OnOff:1, logType hcidump is 1
[08-07 11:35:34.416] (    32) [    11]     515     515  SubLog OnOff:1, logType tcpdump is 1
[08-07 11:35:34.416] (    33) [    11]     515     515  SubLog OnOff:0, logType trustlog is 1
[08-07 11:35:34.417] (    34) [    11]     515     515  index:11,logType:tcpdump, logSize:256, totalSize:4096
[08-07 11:35:34.417] (    35) [    11]     515     515  index:10,logType:hcidump, logSize:64, totalSize:1024
[08-07 11:35:34.417] (    36) [    11]     515     515  value:default
[08-07 11:35:34.418] (    37) [    11]     515     515  mSubLog:1
[08-07 11:35:34.418] (    38) [    11]     515     515  mSubLog:1
[08-07 11:35:34.418] (    39) [    11]     515     515  logSourceCnt:13 compressLevel:3
[08-07 11:35:34.419] (    40) [    11]     515     528  [lastlog]  configure is [1]
[08-07 11:35:34.425] (    41) [    11]     515     528  [uboot]  configure is [1]
[08-07 11:35:34.433] (    42) [    11]     515     528  [android]  configure is [1]
[08-07 11:35:34.436] (    43) [    11]     515     528  [kernel]  configure is [1]
[08-07 11:35:34.441] (    44) [    11]     515     528  [trace]  configure is [1]
[08-07 11:35:34.447] (    45) [    11]     515     528  [sgm]  configure is [1]
[08-07 11:35:34.452] (     1) [    11]     524     524  aplogfilesize : 256
[08-07 11:35:34.454] (     2) [    11]     524     524  srootdir : default/
[08-07 11:35:34.455] (     3) [    11]     524     524  aplogmaxsize : 99%
[08-07 11:35:34.456] (     4) [    11]     524     524  aplogrotate : 1
[08-07 11:35:34.457] (     5) [    11]     524     524  prioritypath : 0
[08-07 11:35:34.458] (    46) [    11]     515     528  [sysinfo]  configure is [1]
[08-07 11:35:34.475] (     6) [    11]     524     542  LogReboot:startrebootServcie
[08-07 11:35:34.480] (    47) [    11]     515     528  [thermal]  configure is [0]
[08-07 11:35:34.480] (    48) [    11]     515     528  [ylogdebug]  configure is [1]
[08-07 11:35:34.495] (    49) [    11]     515     528  [phoneinfo]  configure is [1]
[08-07 11:35:34.534] (    50) [    11]     515     528  [hcidump]  configure is [1]
[08-07 11:35:34.550] (    51) [    11]     515     528  [tcpdump]  configure is [1]
[08-07 11:35:34.578] (    52) [    11]     515     528  [trustlog]  configure is [1]
[08-07 11:35:34.583] (    53) [    11]     515     528  listen to 12 source 
[08-07 11:35:34.716] (    54) [    11]     515     528  index:11 log buffer is null 
[08-07 11:35:34.718] (    55) [    11]     515     528  index:12 log buffer is null 
[08-07 11:35:35.153] (     7) [    11]     524     524  mount changed: [] -> [/data/]
[08-07 11:35:35.156] (     8) [    11]     524     524  set logdir to /data/ylog/ap/, diskfree:38841
[08-07 11:35:35.158] (     9) [    11]     524     524  last ylog file  [] not exsit,backup mmap file
[08-07 11:35:35.158] (    10) [    11]     524     524  last ylog not exit, backupMmapData error.
[08-07 11:35:35.185] (    11) [    11]     524     624  copyBlackBox rm exist /data/ylog/ap/blackboxlog
[08-07 11:35:35.204] (    12) [    11]     524     524  create first file
[08-07 11:35:35.223] (    13) [    11]     524     524  get new  file name(new logfile) : /data/ylog/ap/003-0807_113535_poweron.ylog 
[08-07 11:35:35.223] (    14) [    11]     524     524  open log file:/data/ylog/ap/003-0807_113535_poweron.ylog fd:23 diskfree:38846
[08-07 11:35:35.227] (    15) [    11]     524     524  update UID file:/data/ylog/loguid=3
[08-07 11:35:35.229] (    56) [    11]     515     521  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
run finished on 08-07 11:35:36
ylogdebug end




ylogdebug_1  on 08-07 11:40:37


uptime on Thu Aug  7 11:40:37 CST 2025
 11:40:37 up 5 min,  0 users,  load average: 15.55, 12.43, 5.82
run finished on 08-07 11:40:37


logcat -S on Thu Aug  7 11:40:37 CST 2025
size/num main               system             crash              kernel             Total
Total    6963196/53132      3049395/18734      0/0                2044873/19345      12057464/91211
Now      817328/5491        1023934/5275       0/0                866383/8367        2707645/19133
Logspan  2:36.282           3:59.694                              4:18.669           4:18.669
Overhead 254650             251126                                253483             776121

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               341585
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  197257
  713/1000  /system/bin/surfaceflinger                      87030
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 14346
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 12425
  933/1000  /vendor/bin/thermald                             8918
  696/1000 ...r/bin/hw/vendor.sprd.hardware.vibrator-service 6329
  643/1000 ...dor/bin/hw/vendor.sprd.hardware.lights-service 4832
10187 com.android.systemui                                  79207
0     root                                                  71280
1041  audioserver                                           56865
10182 com.android.launcher3                                 42340
10160 com.google.android.inputmethod.latin                  39222
10246 com.antutu.ABenchMark                                 32541
10142 com.google.android.apps.messaging                     28754
10145 com.google.android.googlequicksearchbox               28012
10138 com.google.android.gms                                24414
1027  nfc                                                   18270
1001  radio                                                 16270
10070 com.android.calllogbackup                             10811
1013  media                                                  7886
10224 com.google.android.providers.media.module              4201
10141 com.google.android.apps.turbo                          4014
10151 com.android.chrome                                     3954
10175 com.google.android.apps.photos                         3760


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               977526
10187 com.android.systemui                                  37345

run finished on 08-07 11:40:37


ylogctl q on Thu Aug  7 11:40:37 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 4891430 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    BC1]->[    345] 
[   539]  kernel       -> Open    -> kernel.log       [    1A2]->[     FF] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   137C]->[    138] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [    35E]->[     D2] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     37]->[      D] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 11:40:37


ylogctl space on Thu Aug  7 11:40:37 CST 2025
Root:/data/ylog/ap/   APLogSize:49 APLogMaxSize:46570 DiskFreeSpace:38806  DiskReserved:60
run finished on 08-07 11:40:38


cat /data/ylog/ylog.conf on Thu Aug  7 11:40:38 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 11:40:38


ls -l /data/ylog/ap/ on Thu Aug  7 11:40:38 CST 2025
total 50364
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  4897395 2025-08-07 11:40 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 11:40:38


tail -n +339</data/ylog/journal.log on Thu Aug  7 11:40:38 CST 2025
[08-07 11:35:35.227] (    15) [    11]     524     524  update UID file:/data/ylog/loguid=3
[08-07 11:35:35.229] (    56) [    11]     515     521  get notifyNewFileHeader  but mBinHadStarted=1 ,so ignore
[08-07 11:35:37.320] (    16) [    13]     524     624  copy /blackbox/ file: /blackbox/ylog/ copied to /data/ylog/ap/blackboxlog
[08-07 11:35:37.320] (    17) [    13]     524     624  copyCache rm exist /data/ylog/ap/cachelog
[08-07 11:35:37.371] (    18) [    14]     524     624  copy /cache/ file: /cache/ylog/ copied to /data/ylog/ap/cachelog
[08-07 11:35:37.801] (    57) [    14]     515     527  open new log:/data/ylog/ap/tcpdump/004_0807_113537_tcpdump.cap, wfd:34, logname:/data/ylog/ap/tcpdump/004_0807_113537_tcpdump.cap
[08-07 11:35:37.801] (    58) [    14]     515     527  logType->totalwriten:0 sublogsize:252
[08-07 11:35:39.483] (    19) [    16]     524     542  currentTime: 20250807113539-11610468
[08-07 11:35:39.492] (    20) [    16]     524     542  SystemBootMode::LINUXKERNEL
[08-07 11:35:39.492] (    21) [    16]     524     542  boot_cause: Pbint triggered
[08-07 11:35:39.492] (    22) [    16]     524     542  boot_reason: normalboot
[08-07 11:35:39.492] (    23) [    16]     524     542  boot_category: normalboot
[08-07 11:35:39.495] (    24) [    16]     524     542  Crash_reason: Normal
[08-07 11:35:39.497] (    25) [    16]     524     926  open /dev/block/by-name/sd_klog failed No such file or directory
run finished on 08-07 11:40:38
ylogdebug end




ylogdebug_2  on 08-07 11:45:39


uptime on Thu Aug  7 11:45:39 CST 2025
 11:45:39 up 10 min,  0 users,  load average: 13.41, 13.84, 8.39
run finished on 08-07 11:45:39


logcat -S on Thu Aug  7 11:45:39 CST 2025
size/num main               system             crash              kernel             Total
Total    7947143/59853      3480893/21066      0/0                2762534/26362      14190570/107281
Now      820124/5578        998131/4889        0/0                863584/8291        2681839/18758
Logspan  3:56.974           8:19.442                              7:24.162           8:23.783
Overhead 248347             250901                                252883             769697

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               463557
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  315851
  713/1000  /system/bin/surfaceflinger                      75243
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 16717
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 14066
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 11439
  933/1000  /vendor/bin/thermald                             8205
  635/1000 ...dor/bin/hw/android.hardware.usb-service.unisoc 5892
  376/1000  /system/bin/hw/android.system.suspend-service    4910
10187 com.android.systemui                                 111682
1041  audioserver                                           75568
0     root                                                  55042
1013  media                                                 34693
10138 com.google.android.gms                                17806
10224 com.google.android.providers.media.module             16702
10145 com.google.android.googlequicksearchbox               12402
1027  nfc                                                    7451
1046  mediacodec                                             6945
10150 com.google.android.calendar                            4902


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               955351
10187 com.android.systemui                                  38392

run finished on 08-07 11:45:39


ylogctl q on Thu Aug  7 11:45:39 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 6147359 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    D5E]->[    3D1] 
[   539]  kernel       -> Open    -> kernel.log       [    256]->[    173] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   27DE]->[    26E] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [    61D]->[    185] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     5B]->[     1D] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 11:45:39


ylogctl space on Thu Aug  7 11:45:39 CST 2025
Root:/data/ylog/ap/   APLogSize:50 APLogMaxSize:46570 DiskFreeSpace:38791  DiskReserved:60
run finished on 08-07 11:45:40


cat /data/ylog/ylog.conf on Thu Aug  7 11:45:40 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 11:45:40


ls -l /data/ylog/ap/ on Thu Aug  7 11:45:40 CST 2025
total 51584
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  6147359 2025-08-07 11:45 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 11:45:40
ylogdebug end




ylogdebug_3  on 08-07 11:50:40


uptime on Thu Aug  7 11:50:40 CST 2025
 11:50:41 up 15 min,  0 users,  load average: 13.18, 13.38, 9.71
run finished on 08-07 11:50:41


logcat -S on Thu Aug  7 11:50:41 CST 2025
size/num main               system             crash              kernel             Total
Total    7976588/60133      3493941/21169      0/0                3007712/28624      14478241/109926
Now      849569/5858        1011179/4992       0/0                912528/8844        2773276/19694
Logspan  8:58.879           13:19.449                             9:03.228           13:25.362
Overhead 248347             250901                                249942             769468

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               482841
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  324232
  713/1000  /system/bin/surfaceflinger                      75243
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 16717
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 14066
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 11439
  933/1000  /vendor/bin/thermald                             8205
  887/1000  /system_ext/bin/slogmodem                        7996
  376/1000  /system/bin/hw/android.system.suspend-service    7894
  635/1000 ...dor/bin/hw/android.hardware.usb-service.unisoc 5892
10187 com.android.systemui                                 114990
1041  audioserver                                           76751
0     root                                                  59130
1013  media                                                 34693
10138 com.google.android.gms                                18037
10224 com.google.android.providers.media.module             16702
10145 com.google.android.googlequicksearchbox               12987
1027  nfc                                                    7451
1046  mediacodec                                             6945
10150 com.google.android.calendar                            4902


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               967722
10187 com.android.systemui                                  39069

run finished on 08-07 11:50:41


ylogctl q on Thu Aug  7 11:50:41 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 6856700 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    D6F]->[    3E0] 
[   539]  kernel       -> Open    -> kernel.log       [    28F]->[    19F] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   3C02]->[    39D] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [    8C6]->[    230] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     7A]->[     2D] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 11:50:41


ylogctl space on Thu Aug  7 11:50:41 CST 2025
Root:/data/ylog/ap/   APLogSize:50 APLogMaxSize:46570 DiskFreeSpace:38782  DiskReserved:60
run finished on 08-07 11:50:41


cat /data/ylog/ylog.conf on Thu Aug  7 11:50:41 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 11:50:42


ls -l /data/ylog/ap/ on Thu Aug  7 11:50:42 CST 2025
total 52276
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  6856700 2025-08-07 11:50 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 11:50:42
ylogdebug end




ylogdebug_4  on 08-07 11:55:42


uptime on Thu Aug  7 11:55:42 CST 2025
 11:55:42 up 20 min,  0 users,  load average: 13.17, 13.24, 10.68
run finished on 08-07 11:55:42


logcat -S on Thu Aug  7 11:55:42 CST 2025
size/num main               system             crash              kernel             Total
Total    7999085/60342      3499231/21223      0/0                3216633/30511      14714949/112076
Now      872066/6067        1016469/5046       0/0                924997/8728        2813532/19841
Logspan  14:00.589          18:19.456                             13:46.602          18:27.057
Overhead 261084             250901                                255274             789281

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               496115
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  327803
  713/1000  /system/bin/surfaceflinger                      75243
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 16717
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 14066
  887/1000  /system_ext/bin/slogmodem                       12431
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 11439
  376/1000  /system/bin/hw/android.system.suspend-service   10588
  933/1000  /vendor/bin/thermald                             8205
  635/1000 ...dor/bin/hw/android.hardware.usb-service.unisoc 5892
10187 com.android.systemui                                 118650
1041  audioserver                                           77596
0     root                                                  62792
1013  media                                                 34693
10138 com.google.android.gms                                18191
10224 com.google.android.providers.media.module             16702
10145 com.google.android.googlequicksearchbox               13572
1027  nfc                                                    7451
1046  mediacodec                                             6945


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               972497
10187 com.android.systemui                                  39584

run finished on 08-07 11:55:43


ylogctl q on Thu Aug  7 11:55:43 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 7704459 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    D7A]->[    3EB] 
[   539]  kernel       -> Open    -> kernel.log       [    2C4]->[    1CA] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   5011]->[    4D0] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [    C88]->[    319] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     9B]->[     3C] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 11:55:43


ylogctl space on Thu Aug  7 11:55:43 CST 2025
Root:/data/ylog/ap/   APLogSize:51 APLogMaxSize:46570 DiskFreeSpace:38773  DiskReserved:60
run finished on 08-07 11:55:43


cat /data/ylog/ylog.conf on Thu Aug  7 11:55:43 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 11:55:43


ls -l /data/ylog/ap/ on Thu Aug  7 11:55:43 CST 2025
total 53104
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  7704459 2025-08-07 11:55 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 11:55:44
ylogdebug end




ylogdebug_5  on 08-07 12:00:44


uptime on Thu Aug  7 12:00:44 CST 2025
 12:00:44 up 25 min,  0 users,  load average: 13.10, 13.17, 11.37
run finished on 08-07 12:00:44


logcat -S on Thu Aug  7 12:00:44 CST 2025
size/num main               system             crash              kernel             Total
Total    8019089/60531      3502361/21265      0/0                3409642/32229      14931092/114025
Now      892070/6256        1019599/5088       0/0                987012/9103        2898681/20447
Logspan  18:59.206          23:19.463                             18:38.955          23:27.224
Overhead 261084             250901                                260558             796405

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               509361
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  331250
  713/1000  /system/bin/surfaceflinger                      75243
  887/1000  /system_ext/bin/slogmodem                       16866
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 16717
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 14066
  376/1000  /system/bin/hw/android.system.suspend-service   13180
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 11439
  933/1000  /vendor/bin/thermald                             8205
  635/1000 ...dor/bin/hw/android.hardware.usb-service.unisoc 5892
10187 com.android.systemui                                 119547
1041  audioserver                                           78441
0     root                                                  66992
1013  media                                                 34693
10138 com.google.android.gms                                18422
10224 com.google.android.providers.media.module             16702
10145 com.google.android.googlequicksearchbox               14157
1027  nfc                                                    7451
1046  mediacodec                                             6945


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               975524
10187 com.android.systemui                                  39687

run finished on 08-07 12:00:44


ylogctl q on Thu Aug  7 12:00:44 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 8391305 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    D85]->[    3F5] 
[   539]  kernel       -> Open    -> kernel.log       [    2F4]->[    1F6] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   645C]->[    5FF] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [    F2C]->[    3BA] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     BC]->[     4D] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:00:45


ylogctl space on Thu Aug  7 12:00:45 CST 2025
Root:/data/ylog/ap/   APLogSize:52 APLogMaxSize:46570 DiskFreeSpace:38765  DiskReserved:60
run finished on 08-07 12:00:45


cat /data/ylog/ylog.conf on Thu Aug  7 12:00:45 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:00:45


ls -l /data/ylog/ap/ on Thu Aug  7 12:00:45 CST 2025
total 53780
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  8391305 2025-08-07 12:00 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:00:45
ylogdebug end




ylogdebug_6  on 08-07 12:05:46


uptime on Thu Aug  7 12:05:46 CST 2025
 12:05:46 up 30 min,  0 users,  load average: 13.05, 13.14, 11.88
run finished on 08-07 12:05:46


logcat -S on Thu Aug  7 12:05:46 CST 2025
size/num main               system             crash              kernel             Total
Total    8039432/60717      3505698/21307      0/0                3596393/33903      15141523/115927
Now      912413/6442        1022936/5130       0/0                1042773/9416       2978122/20988
Logspan  23:59.372          28:19.47                              23:20.026          28:29.303
Overhead 261084             250901                                251741             790036

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               522560
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  334346
  713/1000  /system/bin/surfaceflinger                      75243
  887/1000  /system_ext/bin/slogmodem                       21301
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 16717
  376/1000  /system/bin/hw/android.system.suspend-service   16076
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 14066
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 11439
  933/1000  /vendor/bin/thermald                             8205
  635/1000 ...dor/bin/hw/android.hardware.usb-service.unisoc 5892
10187 com.android.systemui                                 121485
1041  audioserver                                           79286
0     root                                                  70614
1013  media                                                 34693
10138 com.google.android.gms                                18576
10224 com.google.android.providers.media.module             16702
10145 com.google.android.googlequicksearchbox               14742
1027  nfc                                                    7451
1046  mediacodec                                             6945


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               978655
10187 com.android.systemui                                  39893

run finished on 08-07 12:05:46


ylogctl q on Thu Aug  7 12:05:46 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 9072173 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    D8F]->[    3FF] 
[   539]  kernel       -> Open    -> kernel.log       [    323]->[    21F] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   7884]->[    731] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   11E3]->[    470] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     DD]->[     58] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:05:46


ylogctl space on Thu Aug  7 12:05:46 CST 2025
Root:/data/ylog/ap/   APLogSize:53 APLogMaxSize:46570 DiskFreeSpace:38756  DiskReserved:60
run finished on 08-07 12:05:47


cat /data/ylog/ylog.conf on Thu Aug  7 12:05:47 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:05:47


ls -l /data/ylog/ap/ on Thu Aug  7 12:05:47 CST 2025
total 54444
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  9072173 2025-08-07 12:05 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:05:47
ylogdebug end




ylogdebug_7  on 08-07 12:10:47


uptime on Thu Aug  7 12:10:47 CST 2025
 12:10:47 up 35 min,  0 users,  load average: 14.65, 14.00, 12.61
run finished on 08-07 12:10:47


logcat -S on Thu Aug  7 12:10:48 CST 2025
size/num main               system             crash              kernel             Total
Total    8170697/61553      3556919/21656      0/0                3815613/35884      15543229/119093
Now      912640/6345        1008804/5188       0/0                1000130/9002       2921574/20535
Logspan  28:53.927          33:19.813                             24:26.388          33:19.825
Overhead 254426             256956                                252068             791624

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               493375
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  314503
  713/1000  /system/bin/surfaceflinger                      63361
  887/1000  /system_ext/bin/slogmodem                       25736
  376/1000  /system/bin/hw/android.system.suspend-service   21734
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 14459
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 10988
  622/1000 ...droid.hardware.graphics.allocator@4.0-service 10158
  933/1000  /vendor/bin/thermald                             6792
10187 com.android.systemui                                 113091
0     root                                                  67497
1041  audioserver                                           61423
10246 com.antutu.ABenchMark                                 49691
1013  media                                                 28358
10142 com.google.android.apps.messaging                     27499
10224 com.google.android.providers.media.module             16702
10138 com.google.android.gms                                15509
10145 com.google.android.googlequicksearchbox               12011
1027  nfc                                                    6922
1046  mediacodec                                             5026


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               968022
10187 com.android.systemui                                  36320

run finished on 08-07 12:10:48


ylogctl q on Thu Aug  7 12:10:48 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 10009863 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    DD4]->[    439] 
[   539]  kernel       -> Open    -> kernel.log       [    365]->[    255] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   8D76]->[    86A] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   159D]->[    554] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    102]->[     69] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:10:48


ylogctl space on Thu Aug  7 12:10:48 CST 2025
Root:/data/ylog/ap/   APLogSize:53 APLogMaxSize:46570 DiskFreeSpace:38747  DiskReserved:60
run finished on 08-07 12:10:48


cat /data/ylog/ylog.conf on Thu Aug  7 12:10:48 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:10:48


ls -l /data/ylog/ap/ on Thu Aug  7 12:10:49 CST 2025
total 55360
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 10009863 2025-08-07 12:10 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:10:49
ylogdebug end




ylogdebug_8  on 08-07 12:15:49


uptime on Thu Aug  7 12:15:49 CST 2025
 12:15:49 up 40 min,  0 users,  load average: 14.53, 14.34, 13.15
run finished on 08-07 12:15:49


logcat -S on Thu Aug  7 12:15:49 CST 2025
size/num main               system             crash              kernel             Total
Total    8252945/62022      3568843/21740      0/0                4041117/37927      15862905/121689
Now      929444/6377        1020728/5272       0/0                1029297/9273       2979469/20922
Logspan  33:48.691          38:19.832                             25:01.196          38:21.966
Overhead 252404             256956                                252581             792619

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               484347
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  300741
  713/1000  /system/bin/surfaceflinger                      58619
  887/1000  /system_ext/bin/slogmodem                       29995
  376/1000  /system/bin/hw/android.system.suspend-service   27590
  623/1000 ...ndroid.hardware.graphics.composer@2.4-service 12622
  643/1000 ...or/bin/hw/vendor.sprd.hardware.lights-service 10988
  622/1000 ...ndroid.hardware.graphics.allocator@4.0-service 8925
  933/1000  /vendor/bin/thermald                             6792
10187 com.android.systemui                                 106386
10246 com.antutu.ABenchMark                                 99104
0     root                                                  69212
1041  audioserver                                           50635
10142 com.google.android.apps.messaging                     27499
1013  media                                                 22260
10224 com.google.android.providers.media.module             16702
10138 com.google.android.gms                                14869
10145 com.google.android.googlequicksearchbox               11581
1027  nfc                                                    6922


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               979637
10187 com.android.systemui                                  36629

run finished on 08-07 12:15:50


ylogctl q on Thu Aug  7 12:15:50 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 10754517 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    DFE]->[    460] 
[   539]  kernel       -> Open    -> kernel.log       [    3A6]->[    28D] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   A272]->[    99B] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   1862]->[    5F2] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    121]->[     77] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:15:50


ylogctl space on Thu Aug  7 12:15:50 CST 2025
Root:/data/ylog/ap/   APLogSize:54 APLogMaxSize:46570 DiskFreeSpace:38738  DiskReserved:60
run finished on 08-07 12:15:50


cat /data/ylog/ylog.conf on Thu Aug  7 12:15:50 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:15:50


ls -l /data/ylog/ap/ on Thu Aug  7 12:15:50 CST 2025
total 56088
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 10754517 2025-08-07 12:15 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:15:51
ylogdebug end




ylogdebug_9  on 08-07 12:20:51


uptime on Thu Aug  7 12:20:51 CST 2025
 12:20:51 up 45 min,  0 users,  load average: 16.78, 15.65, 14.02
run finished on 08-07 12:20:51


logcat -S on Thu Aug  7 12:20:51 CST 2025
size/num main               system             crash              kernel             Total
Total    9086854/66753      3858083/23607      0/0                4250374/39767      17195311/130127
Now      1043220/6000       983424/5779        0/0                1042147/9324       3068791/21103
Logspan  14:49.244          42:37.422                             25:21.155          42:41.346
Overhead 260484             255373                                254005             800979

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 222400
1000  system                                               185571
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  146235
  376/1000  /system/bin/hw/android.system.suspend-service   16258
  887/1000  /system_ext/bin/slogmodem                       13129
10246 com.antutu.ABenchMark                                147117
1027  nfc                                                  125274
10142 com.google.android.apps.messaging                     82232
10143 com.android.vending                                   32736
10138 com.google.android.gms                                29074
10224 com.google.android.providers.media.module             23624
0     root                                                  17410
10205 com.google.android.permissioncontroller               12491
10160 com.google.android.inputmethod.latin                  11688
10164 com.google.android.tts                                11617
10156 com.google.android.apps.docs                          11333
10134 com.google.android.dialer                             10426
10091 com.facebook.services                                 10377
10187 com.android.systemui                                   9254
10116 com.facebook.appmanager                                9249
10128 com.google.android.as                                  8384
10082 com.google.android.documentsui                         7740
10145 com.google.android.googlequicksearchbox                7602
10070 com.android.calllogbackup                              6849
10196 com.android.camera2                                    6205
10131 com.google.android.apps.nbu.files                      4724
10175 com.google.android.apps.photos                         4612
10158 com.google.android.contacts                            4251
10215 com.google.android.adservices.api                      4240
10182 com.android.launcher3                                  4206
10151 com.android.chrome                                     4172
1001  radio                                                  3889
10139 com.google.android.as.oss                              3635
1068  secure_element                                         3612
10210 com.google.android.healthconnect.controller            3311


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               962115
10187 com.android.systemui                                  14403

run finished on 08-07 12:20:51


ylogctl q on Thu Aug  7 12:20:51 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 11830658 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [    F47]->[    503] 
[   539]  kernel       -> Open    -> kernel.log       [    3E8]->[    2CA] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   B74F]->[    ACD] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   1B25]->[    68F] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    143]->[     87] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:20:52


ylogctl space on Thu Aug  7 12:20:52 CST 2025
Root:/data/ylog/ap/   APLogSize:55 APLogMaxSize:46570 DiskFreeSpace:38489  DiskReserved:60
run finished on 08-07 12:20:52


cat /data/ylog/ylog.conf on Thu Aug  7 12:20:52 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:20:52


ls -l /data/ylog/ap/ on Thu Aug  7 12:20:52 CST 2025
total 57140
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 11830658 2025-08-07 12:20 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:20:52
ylogdebug end




ylogdebug_10  on 08-07 12:25:53


uptime on Thu Aug  7 12:25:53 CST 2025
 12:25:53 up 50 min,  0 users,  load average: 16.25, 16.16, 14.71
run finished on 08-07 12:25:53


logcat -S on Thu Aug  7 12:25:53 CST 2025
size/num main               system             crash              kernel             Total
Total    9956718/70665      3871845/23702      0/0                4435503/41362      18264066/135729
Now      1192988/5415       997186/5874        0/0                1030860/9175       3221034/20464
Logspan  7:15.11            47:37.438                             25:21.411          47:43.43
Overhead 252589             255373                                254682             793909

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 872173
1000  system                                               189190
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  175088
  887/1000  /system_ext/bin/slogmodem                        6920
  376/1000  /system/bin/hw/android.system.suspend-service    2828
10246 com.antutu.ABenchMark                                 67492
10142 com.google.android.apps.messaging                     25285
10143 com.android.vending                                   11092


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               975362
10187 com.android.systemui                                  14918

run finished on 08-07 12:25:53


ylogctl q on Thu Aug  7 12:25:53 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 12945644 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1041]->[    59C] 
[   539]  kernel       -> Open    -> kernel.log       [    421]->[    303] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   CC5B]->[    C04] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   1EDA]->[    760] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    165]->[     8F] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:25:53


ylogctl space on Thu Aug  7 12:25:53 CST 2025
Root:/data/ylog/ap/   APLogSize:56 APLogMaxSize:46570 DiskFreeSpace:37924  DiskReserved:60
run finished on 08-07 12:25:54


cat /data/ylog/ylog.conf on Thu Aug  7 12:25:54 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:25:54


ls -l /data/ylog/ap/ on Thu Aug  7 12:25:54 CST 2025
total 58276
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 12986363 2025-08-07 12:25 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:25:54
ylogdebug end




ylogdebug_11  on 08-07 12:30:54


uptime on Thu Aug  7 12:30:54 CST 2025
 12:30:55 up 55 min,  0 users,  load average: 14.42, 15.08, 14.65
run finished on 08-07 12:30:55


logcat -S on Thu Aug  7 12:30:55 CST 2025
size/num main               system             crash              kernel             Total
Total    10735565/74902     3940273/24160      0/0                4650923/43295      19326761/142357
Now      1383313/6995       1000211/6033       0/0                984331/8768        3367855/21796
Logspan  8:36.258           52:28.817                             23:41.318          52:34.442
Overhead 258640             254677                                257189             802953

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                1039174
1000  system                                               189642
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  169070
  376/1000  /system/bin/hw/android.system.suspend-service    7882
  887/1000  /system_ext/bin/slogmodem                        7807
10246 com.antutu.ABenchMark                                 90474
10142 com.google.android.apps.messaging                     33850


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               977030
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  965925
  515/1000  /product/bin/ylog                               10215
10187 com.android.systemui                                  14071

run finished on 08-07 12:30:55


ylogctl q on Thu Aug  7 12:30:55 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 13871119 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   113B]->[    5F8] 
[   539]  kernel       -> Open    -> kernel.log       [    463]->[    33E] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   E183]->[    D36] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   2198]->[    802] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    185]->[     9E] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:30:55


ylogctl space on Thu Aug  7 12:30:55 CST 2025
Root:/data/ylog/ap/   APLogSize:57 APLogMaxSize:46570 DiskFreeSpace:38044  DiskReserved:60
run finished on 08-07 12:30:55


cat /data/ylog/ylog.conf on Thu Aug  7 12:30:55 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:30:56


ls -l /data/ylog/ap/ on Thu Aug  7 12:30:56 CST 2025
total 59140
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 13871119 2025-08-07 12:30 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:30:56
ylogdebug end




ylogdebug_12  on 08-07 12:35:56


uptime on Thu Aug  7 12:35:56 CST 2025
 12:35:56 up  1:00,  0 users,  load average: 14.55, 14.76, 14.63
run finished on 08-07 12:35:56


logcat -S on Thu Aug  7 12:35:56 CST 2025
size/num main               system             crash              kernel             Total
Total    10816659/75379     3954420/24259      0/0                4855420/45131      19626499/144769
Now      1399237/7151       1014358/6132       0/0                992285/8820        3405880/22103
Logspan  12:28.857          57:32.711                             24:08.19           57:36.522
Overhead 254215             254677                                255044             797463

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 997475
1000  system                                               205766
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  174515
  376/1000  /system/bin/hw/android.system.suspend-service   13944
  887/1000  /system_ext/bin/slogmodem                       11531
10246 com.antutu.ABenchMark                                124950
10142 com.google.android.apps.messaging                     33850
0     root                                                  10244


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               990662
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  978581
  515/1000  /product/bin/ylog                               11191
10187 com.android.systemui                                  14586

run finished on 08-07 12:35:57


ylogctl q on Thu Aug  7 12:35:57 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 14644272 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1165]->[    621] 
[   539]  kernel       -> Open    -> kernel.log       [    4A0]->[    376] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [   F6AB]->[    E65] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   244B]->[    8B2] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    1A4]->[     AC] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:35:57


ylogctl space on Thu Aug  7 12:35:57 CST 2025
Root:/data/ylog/ap/   APLogSize:58 APLogMaxSize:46570 DiskFreeSpace:38035  DiskReserved:60
run finished on 08-07 12:35:57


cat /data/ylog/ylog.conf on Thu Aug  7 12:35:57 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:35:57


ls -l /data/ylog/ap/ on Thu Aug  7 12:35:57 CST 2025
total 59900
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 14649410 2025-08-07 12:35 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:35:58
ylogdebug end




ylogdebug_13  on 08-07 12:40:58


uptime on Thu Aug  7 12:40:58 CST 2025
 12:40:58 up  1:05,  0 users,  load average: 14.29, 14.58, 14.60
run finished on 08-07 12:40:58


logcat -S on Thu Aug  7 12:40:58 CST 2025
size/num main               system             crash              kernel             Total
Total    10896919/75852     3970797/24364      0/0                5084666/47221      19952382/147437
Now      1413992/7386       1030735/6237       0/0                1025103/9121       3469830/22744
Logspan  17:06.153          1:02:32.432                           24:37.193          1:02:36.284
Overhead 252565             254677                                255223             797288

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 941145
1000  system                                               225848
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  183428
  376/1000  /system/bin/hw/android.system.suspend-service   19598
  887/1000  /system_ext/bin/slogmodem                       15255
10246 com.antutu.ABenchMark                                167088
10142 com.google.android.apps.messaging                     34156
0     root                                                  13259
10187 com.android.systemui                                  11718


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1006302
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  992430
  515/1000  /product/bin/ylog                               12982
10187 com.android.systemui                                  15323

run finished on 08-07 12:40:58


ylogctl q on Thu Aug  7 12:40:58 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 15439152 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1190]->[    649] 
[   539]  kernel       -> Open    -> kernel.log       [    4E5]->[    3B0] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  10BA9]->[    F98] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   26FA]->[    942] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    1C4]->[     BB] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:40:59


ylogctl space on Thu Aug  7 12:40:59 CST 2025
Root:/data/ylog/ap/   APLogSize:59 APLogMaxSize:46570 DiskFreeSpace:38026  DiskReserved:60
run finished on 08-07 12:40:59


cat /data/ylog/ylog.conf on Thu Aug  7 12:40:59 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:40:59


ls -l /data/ylog/ap/ on Thu Aug  7 12:40:59 CST 2025
total 60672
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 15439152 2025-08-07 12:40 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:40:59


tail -n +351</data/ylog/journal.log on Thu Aug  7 12:40:59 CST 2025
[08-07 11:35:39.495] (    24) [    16]     524     542  Crash_reason: Normal
[08-07 11:35:39.497] (    25) [    16]     524     926  open /dev/block/by-name/sd_klog failed No such file or directory
[08-07 12:36:34.482] (    59) [  3671]     515     525  
 1331, 1088, 1329, 1097, 1322, 1068, 1396, 1135, 1366, 1127, 1310, 1245, 1116, 1422, 1122, 1374, 1137, 1412, 1247, 1475, 1150, 1510, 1197, 1150, 1445, 1046, 1710, 1122, 1402, 1147, 1368, 1151, 1417, 1133, 1170, 1363, 1150, 1679, 1790, 1216, 1392, 1151, 1869, 1063, 1327, 1085, 1160, 1291, 1063, 1345, 1075, 1358, 1072, 1336, 1058, 1319, 1124, 1370, 1061, 1078,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
run finished on 08-07 12:41:00
ylogdebug end




ylogdebug_14  on 08-07 12:46:00


uptime on Thu Aug  7 12:46:00 CST 2025
 12:46:00 up  1:10,  0 users,  load average: 14.46, 14.58, 14.61
run finished on 08-07 12:46:00


logcat -S on Thu Aug  7 12:46:00 CST 2025
size/num main               system             crash              kernel             Total
Total    11018548/76615     4024401/24713      0/0                5310879/49309      20353828/150637
Now      1405672/7627       1018917/6237       0/0                989442/8931        3414031/22795
Logspan  21:18.948          1:06:41.193                           23:03.694          1:06:43.265
Overhead 254200             250840                                255301             796580

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 834496
1000  system                                               249583
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  195679
  376/1000  /system/bin/hw/android.system.suspend-service   25356
  887/1000  /system_ext/bin/slogmodem                       19514
10246 com.antutu.ABenchMark                                207773
10142 com.google.android.apps.messaging                     36975
0     root                                                  17311
10187 com.android.systemui                                  14517


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               995440
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  980784
  515/1000  /product/bin/ylog                               13766
10187 com.android.systemui                                  15606

run finished on 08-07 12:46:00


ylogctl q on Thu Aug  7 12:46:00 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 16462898 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   11D0]->[    67C] 
[   539]  kernel       -> Open    -> kernel.log       [    52A]->[    3E9] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  120E5]->[   10CB] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   2ACE]->[    A20] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    1E9]->[     CB] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:46:00


ylogctl space on Thu Aug  7 12:46:01 CST 2025
Root:/data/ylog/ap/   APLogSize:60 APLogMaxSize:46570 DiskFreeSpace:38018  DiskReserved:60
run finished on 08-07 12:46:01


cat /data/ylog/ylog.conf on Thu Aug  7 12:46:01 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:46:01


ls -l /data/ylog/ap/ on Thu Aug  7 12:46:01 CST 2025
total 61676
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 16462898 2025-08-07 12:46 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:46:01
ylogdebug end




ylogdebug_15  on 08-07 12:51:01


uptime on Thu Aug  7 12:51:02 CST 2025
 12:51:02 up  1:15,  0 users,  load average: 14.66, 14.52, 14.56
run finished on 08-07 12:51:02


logcat -S on Thu Aug  7 12:51:02 CST 2025
size/num main               system             crash              kernel             Total
Total    11098406/77076     4036789/24801      0/0                5532409/51330      20667604/153207
Now      1485530/8088       1031305/6325       0/0                1014585/9219       3531420/23632
Logspan  26:24.748          1:11:48.798                           23:21.288          1:11:49.065
Overhead 260698             250840                                255088             805673

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 834496
1000  system                                               274808
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  208120
  376/1000  /system/bin/hw/android.system.suspend-service   31516
  887/1000  /system_ext/bin/slogmodem                       23949
10246 com.antutu.ABenchMark                                254409
10142 com.google.android.apps.messaging                     36975
0     root                                                  20701
10187 com.android.systemui                                  17352


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1007519
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  991887
  515/1000  /product/bin/ylog                               14742
10187 com.android.systemui                                  15915

run finished on 08-07 12:51:02


ylogctl q on Thu Aug  7 12:51:02 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 17270845 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   11FA]->[    6A5] 
[   539]  kernel       -> Open    -> kernel.log       [    56D]->[    422] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  135FB]->[   11FB] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   2D85]->[    AC4] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    209]->[     D6] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:51:02


ylogctl space on Thu Aug  7 12:51:02 CST 2025
Root:/data/ylog/ap/   APLogSize:60 APLogMaxSize:46570 DiskFreeSpace:38009  DiskReserved:60
run finished on 08-07 12:51:02


cat /data/ylog/ylog.conf on Thu Aug  7 12:51:03 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:51:03


ls -l /data/ylog/ap/ on Thu Aug  7 12:51:03 CST 2025
total 62464
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 17270845 2025-08-07 12:51 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:51:03
ylogdebug end




ylogdebug_16  on 08-07 12:56:03


uptime on Thu Aug  7 12:56:03 CST 2025
 12:56:03 up  1:20,  0 users,  load average: 14.34, 14.51, 14.55
run finished on 08-07 12:56:03


logcat -S on Thu Aug  7 12:56:03 CST 2025
size/num main               system             crash              kernel             Total
Total    11195732/77666     4061708/24969      0/0                5758875/53388      21016315/156023
Now      1452981/8093       990730/6093        0/0                1044592/9514       3488303/23700
Logspan  30:27.171          1:14:20.002                           23:35.418          1:14:24.1
Overhead 259429             246601                                255861             802330

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 729761
10246 com.antutu.ABenchMark                                294554
1000  system                                               285977
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  209151
  376/1000  /system/bin/hw/android.system.suspend-service   36866
  887/1000  /system_ext/bin/slogmodem                       27497
10142 com.google.android.apps.messaging                     44475
0     root                                                  24414
10187 com.android.systemui                                  17642
10138 com.google.android.gms                                 7783


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               970213
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  954010
  515/1000  /product/bin/ylog                               15313
10187 com.android.systemui                                  12960

run finished on 08-07 12:56:04


ylogctl q on Thu Aug  7 12:56:04 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 18080721 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   122B]->[    6D0] 
[   539]  kernel       -> Open    -> kernel.log       [    5B2]->[    45E] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  14AF3]->[   132B] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   303B]->[    B80] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    22A]->[     E7] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 12:56:04


ylogctl space on Thu Aug  7 12:56:04 CST 2025
Root:/data/ylog/ap/   APLogSize:61 APLogMaxSize:46570 DiskFreeSpace:38000  DiskReserved:60
run finished on 08-07 12:56:04


cat /data/ylog/ylog.conf on Thu Aug  7 12:56:04 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 12:56:04


ls -l /data/ylog/ap/ on Thu Aug  7 12:56:04 CST 2025
total 63256
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 18080721 2025-08-07 12:56 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 12:56:05
ylogdebug end




ylogdebug_17  on 08-07 13:01:05


uptime on Thu Aug  7 13:01:05 CST 2025
 13:01:05 up  1:25,  0 users,  load average: 14.51, 14.48, 14.53
run finished on 08-07 13:01:05


logcat -S on Thu Aug  7 13:01:05 CST 2025
size/num main               system             crash              kernel             Total
Total    11290464/78255     4084008/25116      0/0                5991003/55501      21365475/158872
Now      1482293/8344       1013030/6240       0/0                1014793/9263       3510116/23847
Logspan  35:23.938          1:19:22.102                           22:28.419          1:19:24.914
Overhead 258091             246601                                257138             803477

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 676321
10246 com.antutu.ABenchMark                                341489
1000  system                                               312132
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  214375
  376/1000  /system/bin/hw/android.system.suspend-service   43332
  887/1000  /system_ext/bin/slogmodem                       31932
 1490/1000  com.android.settings                             8239
10142 com.google.android.apps.messaging                     44475
0     root                                                  28078
10187 com.android.systemui                                  21915
10138 com.google.android.gms                                 7783
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                               991998
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  973103
  515/1000  /product/bin/ylog                               16338
10187 com.android.systemui                                  13475

run finished on 08-07 13:01:05


ylogctl q on Thu Aug  7 13:01:05 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 19085773 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   125C]->[    6FB] 
[   539]  kernel       -> Open    -> kernel.log       [    5F4]->[    496] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  15FD6]->[   145C] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   33E7]->[    C5C] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    24A]->[     F7] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:01:06


ylogctl space on Thu Aug  7 13:01:06 CST 2025
Root:/data/ylog/ap/   APLogSize:62 APLogMaxSize:46570 DiskFreeSpace:37994  DiskReserved:60
run finished on 08-07 13:01:06


cat /data/ylog/ylog.conf on Thu Aug  7 13:01:06 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:01:06


ls -l /data/ylog/ap/ on Thu Aug  7 13:01:06 CST 2025
total 64236
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 19085773 2025-08-07 13:01 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:01:06
ylogdebug end




ylogdebug_18  on 08-07 13:06:07


uptime on Thu Aug  7 13:06:07 CST 2025
 13:06:07 up  1:30,  0 users,  load average: 14.71, 14.54, 14.54
run finished on 08-07 13:06:07


logcat -S on Thu Aug  7 13:06:07 CST 2025
size/num main               system             crash              kernel             Total
Total    11375130/78749     4100290/25224      0/0                6217885/57555      21693305/161528
Now      1501515/8477       1029312/6348       0/0                1045233/9522       3576060/24347
Logspan  40:23.753          1:24:23.773                           23:08.009          1:24:28.335
Overhead 258245             246601                                257232             805349

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 620756
10246 com.antutu.ABenchMark                                390537
1000  system                                               329494
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  219062
  376/1000  /system/bin/hw/android.system.suspend-service   49696
  887/1000  /system_ext/bin/slogmodem                       35656
 1490/1000  com.android.settings                             8239
10142 com.google.android.apps.messaging                     44475
0     root                                                  32137
10187 com.android.systemui                                  24660
10138 com.google.android.gms                                 7783
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168
1041  audioserver                                            6760


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1007267
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  987395
  515/1000  /product/bin/ylog                               17315
10187 com.android.systemui                                  14488

run finished on 08-07 13:06:07


ylogctl q on Thu Aug  7 13:06:07 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 19907326 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   128A]->[    728] 
[   539]  kernel       -> Open    -> kernel.log       [    639]->[    4CF] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  174EF]->[   1591] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   36A4]->[    CF5] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    26C]->[    104] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:06:07


ylogctl space on Thu Aug  7 13:06:07 CST 2025
Root:/data/ylog/ap/   APLogSize:63 APLogMaxSize:46570 DiskFreeSpace:37985  DiskReserved:60
run finished on 08-07 13:06:08


cat /data/ylog/ylog.conf on Thu Aug  7 13:06:08 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:06:08


ls -l /data/ylog/ap/ on Thu Aug  7 13:06:08 CST 2025
total 65040
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 19907326 2025-08-07 13:06 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:06:08
ylogdebug end




ylogdebug_19  on 08-07 13:11:08


uptime on Thu Aug  7 13:11:08 CST 2025
 13:11:09 up  1:35,  0 users,  load average: 14.44, 14.40, 14.47
run finished on 08-07 13:11:09


logcat -S on Thu Aug  7 13:11:09 CST 2025
size/num main               system             crash              kernel             Total
Total    11455012/79195     4110341/25296      0/0                6438175/59543      22003528/164034
Now      1450574/8247       1039363/6420       0/0                1003620/9102       3493557/23769
Logspan  45:14.022          1:29:25.598                           22:31.679          1:29:30.78
Overhead 253982             246601                                256011             801121

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 515431
10246 com.antutu.ABenchMark                                438272
1000  system                                               329864
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  206758
  376/1000  /system/bin/hw/android.system.suspend-service   55348
  887/1000  /system_ext/bin/slogmodem                       40091
 1490/1000  com.android.settings                             8239
10142 com.google.android.apps.messaging                     44475
0     root                                                  35257
10187 com.android.systemui                                  26347
10138 com.google.android.gms                                 7783
1041  audioserver                                            7605
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168
10145 com.google.android.googlequicksearchbox                5631


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1017215
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  996367
  515/1000  /product/bin/ylog                               18291
10187 com.android.systemui                                  14591

run finished on 08-07 13:11:09


ylogctl q on Thu Aug  7 13:11:09 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 20693946 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   12B1]->[    74D] 
[   539]  kernel       -> Open    -> kernel.log       [    67C]->[    506] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  189F7]->[   16C3] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   396B]->[    D9A] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    28B]->[    112] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:11:09


ylogctl space on Thu Aug  7 13:11:09 CST 2025
Root:/data/ylog/ap/   APLogSize:64 APLogMaxSize:46570 DiskFreeSpace:37977  DiskReserved:60
run finished on 08-07 13:11:09


cat /data/ylog/ylog.conf on Thu Aug  7 13:11:09 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:11:10


ls -l /data/ylog/ap/ on Thu Aug  7 13:11:10 CST 2025
total 65816
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 20699742 2025-08-07 13:11 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:11:10
ylogdebug end




ylogdebug_20  on 08-07 13:16:10


uptime on Thu Aug  7 13:16:10 CST 2025
 13:16:10 up  1:40,  0 users,  load average: 14.51, 14.39, 14.44
run finished on 08-07 13:16:10


logcat -S on Thu Aug  7 13:16:10 CST 2025
size/num main               system             crash              kernel             Total
Total    11532357/79626     4120010/25367      0/0                6652674/61483      22305041/166476
Now      1527919/8678       1049032/6491       0/0                1021691/9247       3598642/24416
Logspan  50:14.022          1:34:27.431                           23:02.01           1:34:31.832
Overhead 260137             257594                                256273             820267

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1082  artd                                                 515431
10246 com.antutu.ABenchMark                                486714
1000  system                                               353499
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  217418
  376/1000  /system/bin/hw/android.system.suspend-service   61102
  887/1000  /system_ext/bin/slogmodem                       44526
 1490/1000  com.android.settings                             8239
10142 com.google.android.apps.messaging                     44475
0     root                                                  39095
10187 com.android.systemui                                  26347
1041  audioserver                                            8450
10138 com.google.android.gms                                 7783
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168
10145 com.google.android.googlequicksearchbox                6216


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1026884
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1005060
  515/1000  /product/bin/ylog                               19267
10187 com.android.systemui                                  14591

run finished on 08-07 13:16:11


ylogctl q on Thu Aug  7 13:16:11 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 21689054 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   12DA]->[    775] 
[   539]  kernel       -> Open    -> kernel.log       [    6BB]->[    539] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  19EE9]->[   17F8] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   3D45]->[    E80] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    2AA]->[    120] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:16:11


ylogctl space on Thu Aug  7 13:16:11 CST 2025
Root:/data/ylog/ap/   APLogSize:65 APLogMaxSize:46570 DiskFreeSpace:37968  DiskReserved:60
run finished on 08-07 13:16:11


cat /data/ylog/ylog.conf on Thu Aug  7 13:16:11 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:16:11


ls -l /data/ylog/ap/ on Thu Aug  7 13:16:11 CST 2025
total 66784
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 21689054 2025-08-07 13:16 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:16:12
ylogdebug end




ylogdebug_21  on 08-07 13:21:12


uptime on Thu Aug  7 13:21:12 CST 2025
 13:21:12 up  1:45,  0 users,  load average: 14.21, 14.41, 14.44
run finished on 08-07 13:21:12


logcat -S on Thu Aug  7 13:21:12 CST 2025
size/num main               system             crash              kernel             Total
Total    11617834/80112     4133018/25458      0/0                6873484/63472      22624336/169042
Now      1547913/8838       1062040/6582       0/0                1046042/9437       3655995/24857
Logspan  55:16.671          1:39:33.559                           23:49.368          1:39:33.779
Overhead 260389             257594                                255694             821868

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                537622
1082  artd                                                 460436
1000  system                                               368919
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  219659
  376/1000  /system/bin/hw/android.system.suspend-service   67060
  887/1000  /system_ext/bin/slogmodem                       48961
 1490/1000  com.android.settings                             8239
10142 com.google.android.apps.messaging                     44475
0     root                                                  42575
10187 com.android.systemui                                  29756
1041  audioserver                                            9295
10138 com.google.android.gms                                 7783
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168
10145 com.google.android.googlequicksearchbox                6801


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1039480
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1016680
  515/1000  /product/bin/ylog                               20243
10187 com.android.systemui                                  15003

run finished on 08-07 13:21:12


ylogctl q on Thu Aug  7 13:21:12 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 22476987 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1305]->[    79E] 
[   539]  kernel       -> Open    -> kernel.log       [    6FD]->[    56F] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  1B3BE]->[   1929] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   3FF3]->[    F1B] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    2CA]->[    12D] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:21:13


ylogctl space on Thu Aug  7 13:21:13 CST 2025
Root:/data/ylog/ap/   APLogSize:65 APLogMaxSize:46570 DiskFreeSpace:37960  DiskReserved:60
run finished on 08-07 13:21:13


cat /data/ylog/ylog.conf on Thu Aug  7 13:21:13 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:21:13


ls -l /data/ylog/ap/ on Thu Aug  7 13:21:13 CST 2025
total 67556
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 22482484 2025-08-07 13:21 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:21:13
ylogdebug end




ylogdebug_22  on 08-07 13:26:14


uptime on Thu Aug  7 13:26:14 CST 2025
 13:26:14 up  1:50,  0 users,  load average: 14.22, 14.39, 14.43
run finished on 08-07 13:26:14


logcat -S on Thu Aug  7 13:26:14 CST 2025
size/num main               system             crash              kernel             Total
Total    11701052/80587     4146266/25550      0/0                7066965/65183      22914283/171320
Now      1565599/9083       1075288/6674       0/0                1042993/9369       3683880/25126
Logspan  1:00:17.654        1:44:33.542                           24:32.511          1:44:35.232
Overhead 261442             257594                                253275             822358

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                587609
1082  artd                                                 423116
1000  system                                               365983
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  203442
  376/1000  /system/bin/hw/android.system.suspend-service   73120
  887/1000  /system_ext/bin/slogmodem                       53396
 1490/1000  com.android.settings                             8239
0     root                                                  46070
10142 com.google.android.apps.messaging                     44475
10187 com.android.systemui                                  32626
1041  audioserver                                           10140
10138 com.google.android.gms                                 7783
10145 com.google.android.googlequicksearchbox                7546
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1052213
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1028438
  515/1000  /product/bin/ylog                               21218
10187 com.android.systemui                                  15518

run finished on 08-07 13:26:14


ylogctl q on Thu Aug  7 13:26:14 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 23274689 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1331]->[    7CA] 
[   539]  kernel       -> Open    -> kernel.log       [    739]->[    5A7] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  1C8A1]->[   1A5C] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   42A4]->[    FC1] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    2E9]->[    13E] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:26:14


ylogctl space on Thu Aug  7 13:26:14 CST 2025
Root:/data/ylog/ap/   APLogSize:66 APLogMaxSize:46570 DiskFreeSpace:37951  DiskReserved:60
run finished on 08-07 13:26:15


cat /data/ylog/ylog.conf on Thu Aug  7 13:26:15 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:26:15


ls -l /data/ylog/ap/ on Thu Aug  7 13:26:15 CST 2025
total 68332
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 23274689 2025-08-07 13:26 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:26:15
ylogdebug end




ylogdebug_23  on 08-07 13:31:15


uptime on Thu Aug  7 13:31:15 CST 2025
 13:31:15 up  1:55,  0 users,  load average: 14.35, 14.35, 14.40
run finished on 08-07 13:31:15


logcat -S on Thu Aug  7 13:31:16 CST 2025
size/num main               system             crash              kernel             Total
Total    11783707/81055     4158082/25633      0/0                7267144/66965      23208933/173653
Now      1517318/8803       1087104/6757       0/0                1046669/9387       3651091/24947
Logspan  1:05:16.951        1:49:33.06                            25:06.56           1:49:37.571
Overhead 257887             257594                                250849             818113

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                637396
1000  system                                               385955
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  210712
  376/1000  /system/bin/hw/android.system.suspend-service   78976
  887/1000  /system_ext/bin/slogmodem                       57655
 1490/1000  com.android.settings                             8239
1082  artd                                                 297337
0     root                                                  49370
10142 com.google.android.apps.messaging                     44475
10187 com.android.systemui                                  35461
1041  audioserver                                           10985
10145 com.google.android.googlequicksearchbox                8305
10138 com.google.android.gms                                 7783
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1063720
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1038969
  515/1000  /product/bin/ylog                               22194
10187 com.android.systemui                                  15827

run finished on 08-07 13:31:16


ylogctl q on Thu Aug  7 13:31:16 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 24066905 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   135A]->[    7F0] 
[   539]  kernel       -> Open    -> kernel.log       [    777]->[    5E2] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  1DDAF]->[   1B8E] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   455B]->[   106F] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    308]->[    14E] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:31:16


ylogctl space on Thu Aug  7 13:31:16 CST 2025
Root:/data/ylog/ap/   APLogSize:67 APLogMaxSize:46570 DiskFreeSpace:37942  DiskReserved:60
run finished on 08-07 13:31:16


cat /data/ylog/ylog.conf on Thu Aug  7 13:31:16 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:31:16


ls -l /data/ylog/ap/ on Thu Aug  7 13:31:17 CST 2025
total 69112
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 24075928 2025-08-07 13:31 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:31:17
ylogdebug end




ylogdebug_24  on 08-07 13:36:17


uptime on Thu Aug  7 13:36:17 CST 2025
 13:36:17 up  2:00,  0 users,  load average: 14.52, 14.43, 14.42
run finished on 08-07 13:36:17


logcat -S on Thu Aug  7 13:36:17 CST 2025
size/num main               system             crash              kernel             Total
Total    11876601/81620     4186473/25806      0/0                7491826/68987      23554900/176413
Now      1544720/8972       1050038/6651       0/0                1009566/9051       3604324/24674
Logspan  1:10:20.835        1:54:26.923                           24:11.927          1:54:26.935
Overhead 257431             256029                                251451             818078

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                687370
1000  system                                               418341
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  229207
  376/1000  /system/bin/hw/android.system.suspend-service   85646
  887/1000  /system_ext/bin/slogmodem                       62090
 1490/1000  com.android.settings                             8239
1082  artd                                                 231845
0     root                                                  53636
10142 com.google.android.apps.messaging                     44781
10187 com.android.systemui                                  38820
1041  audioserver                                           11830
10145 com.google.android.googlequicksearchbox                8965
10138 com.google.android.gms                                 7925
10215 com.google.android.adservices.api                      7209
10116 com.facebook.appmanager                                7168


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1026803
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1001185
  515/1000  /product/bin/ylog                               23061
10187 com.android.systemui                                  15678

run finished on 08-07 13:36:17


ylogctl q on Thu Aug  7 13:36:17 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 25098422 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   138F]->[    81F] 
[   539]  kernel       -> Open    -> kernel.log       [    7BB]->[    61D] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  1F28F]->[   1CC0] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   48F0]->[   114F] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    327]->[    159] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:36:18


ylogctl space on Thu Aug  7 13:36:18 CST 2025
Root:/data/ylog/ap/   APLogSize:68 APLogMaxSize:46570 DiskFreeSpace:37933  DiskReserved:60
run finished on 08-07 13:36:18


cat /data/ylog/ylog.conf on Thu Aug  7 13:36:18 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:36:18


ls -l /data/ylog/ap/ on Thu Aug  7 13:36:18 CST 2025
total 70116
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 25098422 2025-08-07 13:36 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:36:18
ylogdebug end




ylogdebug_25  on 08-07 13:41:19


uptime on Thu Aug  7 13:41:19 CST 2025
 13:41:19 up  2:05,  0 users,  load average: 14.38, 14.41, 14.40
run finished on 08-07 13:41:19


logcat -S on Thu Aug  7 13:41:19 CST 2025
size/num main               system             crash              kernel             Total
Total    11967396/82130     4199591/25893      0/0                7711534/70976      23878521/178999
Now      1570131/9225       1063156/6738       0/0                1032859/9246       3666146/25209
Logspan  1:15:20.562        1:59:26.941                           24:43.522          1:59:27.839
Overhead 260351             256029                                252031             823506

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                736732
1000  system                                               433598
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  231387
  376/1000  /system/bin/hw/android.system.suspend-service   91502
  887/1000  /system_ext/bin/slogmodem                       66525
 1490/1000  com.android.settings                             8239
1082  artd                                                 178805
0     root                                                  57027
10142 com.google.android.apps.messaging                     44781
10187 com.android.systemui                                  41655
10145 com.google.android.googlequicksearchbox               13840
1041  audioserver                                           12675
10138 com.google.android.gms                                 7925


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1039612
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1012204
  515/1000  /product/bin/ylog                               24851
10187 com.android.systemui                                  15987

run finished on 08-07 13:41:19


ylogctl q on Thu Aug  7 13:41:19 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 25906015 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   13BD]->[    84A] 
[   539]  kernel       -> Open    -> kernel.log       [    7FF]->[    656] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  20768]->[   1DF1] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   4BA7]->[   11F6] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    346]->[    167] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:41:19


ylogctl space on Thu Aug  7 13:41:19 CST 2025
Root:/data/ylog/ap/   APLogSize:69 APLogMaxSize:46570 DiskFreeSpace:37924  DiskReserved:60
run finished on 08-07 13:41:20


cat /data/ylog/ylog.conf on Thu Aug  7 13:41:20 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:41:20


ls -l /data/ylog/ap/ on Thu Aug  7 13:41:20 CST 2025
total 70904
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 25906015 2025-08-07 13:41 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:41:20


tail -n +354</data/ylog/journal.log on Thu Aug  7 13:41:20 CST 2025
 1331, 1088, 1329, 1097, 1322, 1068, 1396, 1135, 1366, 1127, 1310, 1245, 1116, 1422, 1122, 1374, 1137, 1412, 1247, 1475, 1150, 1510, 1197, 1150, 1445, 1046, 1710, 1122, 1402, 1147, 1368, 1151, 1417, 1133, 1170, 1363, 1150, 1679, 1790, 1216, 1392, 1151, 1869, 1063, 1327, 1085, 1160, 1291, 1063, 1345, 1075, 1358, 1072, 1336, 1058, 1319, 1124, 1370, 1061, 1078,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
[08-07 13:37:34.538] (    60) [  7331]     515     525  
 1120, 1434, 1106, 1382, 1140, 1411, 1159, 1398, 1133, 1392, 1115, 1161, 1405, 1111, 1384, 1124, 1412, 1118, 1364, 1105, 1380, 1157, 1278, 1204, 1131, 1397, 1155, 1380, 1137, 1371, 1100, 1430, 1106, 1377, 1139, 1114, 1403, 1123, 1146, 1371, 1113, 1447, 1148, 1416, 1129, 1413, 1131, 1132, 1421, 1133, 1371, 1169, 1383, 1123, 1385, 1134, 1405, 1110, 1166, 1339,
    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
run finished on 08-07 13:41:20
ylogdebug end




ylogdebug_26  on 08-07 13:46:21


uptime on Thu Aug  7 13:46:21 CST 2025
 13:46:21 up  2:10,  0 users,  load average: 14.47, 14.42, 14.40
run finished on 08-07 13:46:21


logcat -S on Thu Aug  7 13:46:21 CST 2025
size/num main               system             crash              kernel             Total
Total    12049053/82591     4211524/25977      0/0                7925734/72922      24186311/181490
Now      1520839/8974       1075089/6822       0/0                985164/8853        3581092/24649
Logspan  1:20:20.693        2:04:26.962                           23:31.583          2:04:29.917
Overhead 258289             256029                                252109             824802

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                786215
1000  system                                               450936
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  235546
  376/1000  /system/bin/hw/android.system.suspend-service   97460
  887/1000  /system_ext/bin/slogmodem                       70960
 1490/1000  com.android.settings                             8239
0     root                                                  60327
1082  artd                                                  56240
10142 com.google.android.apps.messaging                     44781
10187 com.android.systemui                                  43377
10145 com.google.android.googlequicksearchbox               14425
1041  audioserver                                           13520
10138 com.google.android.gms                                 7925


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1051236
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1022851
  515/1000  /product/bin/ylog                               25828
10187 com.android.systemui                                  16296

run finished on 08-07 13:46:21


ylogctl q on Thu Aug  7 13:46:21 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 26675667 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   13EA]->[    874] 
[   539]  kernel       -> Open    -> kernel.log       [    840]->[    690] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  21C7C]->[   1F21] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   4E59]->[   129D] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    36A]->[    176] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:46:21


ylogctl space on Thu Aug  7 13:46:21 CST 2025
Root:/data/ylog/ap/   APLogSize:69 APLogMaxSize:46570 DiskFreeSpace:37916  DiskReserved:60
run finished on 08-07 13:46:22


cat /data/ylog/ylog.conf on Thu Aug  7 13:46:22 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:46:22


ls -l /data/ylog/ap/ on Thu Aug  7 13:46:22 CST 2025
total 71656
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 26675667 2025-08-07 13:46 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:46:22
ylogdebug end




ylogdebug_27  on 08-07 13:51:22


uptime on Thu Aug  7 13:51:22 CST 2025
 13:51:22 up  2:16,  0 users,  load average: 14.72, 14.69, 14.55
run finished on 08-07 13:51:23


logcat -S on Thu Aug  7 13:51:23 CST 2025
size/num main               system             crash              kernel             Total
Total    12202426/83334     4241348/26173      0/0                8129643/74764      24573417/184271
Now      1543221/8893       1104913/7018       0/0                992531/8952        3640665/24863
Logspan  1:24:09.628        2:09:27.615                           23:20.078          2:09:31.995
Overhead 250581             256029                                253842             823099

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                815763
1000  system                                               503671
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  279035
  376/1000  /system/bin/hw/android.system.suspend-service  101094
  887/1000  /system_ext/bin/slogmodem                       74508
 1490/1000  com.android.settings                             8239
0     root                                                  62109
10187 com.android.systemui                                  44780
10142 com.google.android.apps.messaging                     26461
1082  artd                                                  14938
10145 com.google.android.googlequicksearchbox               14602
1041  audioserver                                           14027
10138 com.google.android.gms                                12614


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1080727
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1051365
  515/1000  /product/bin/ylog                               26805
10187 com.android.systemui                                  16502

run finished on 08-07 13:51:23


ylogctl q on Thu Aug  7 13:51:23 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 27657419 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   142E]->[    8A2] 
[   539]  kernel       -> Open    -> kernel.log       [    87F]->[    6CB] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  23183]->[   2055] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   522B]->[   137B] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    389]->[    187] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:51:23


ylogctl space on Thu Aug  7 13:51:23 CST 2025
Root:/data/ylog/ap/   APLogSize:70 APLogMaxSize:46570 DiskFreeSpace:37906  DiskReserved:60
run finished on 08-07 13:51:23


cat /data/ylog/ylog.conf on Thu Aug  7 13:51:23 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:51:24


ls -l /data/ylog/ap/ on Thu Aug  7 13:51:24 CST 2025
total 72616
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 27657419 2025-08-07 13:51 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:51:24
ylogdebug end




ylogdebug_28  on 08-07 13:56:24


uptime on Thu Aug  7 13:56:24 CST 2025
 13:56:24 up  2:21,  0 users,  load average: 14.86, 14.72, 14.59
run finished on 08-07 13:56:24


logcat -S on Thu Aug  7 13:56:24 CST 2025
size/num main               system             crash              kernel             Total
Total    12286403/83811     4253695/26261      0/0                8337499/76644      24877597/186716
Now      1627198/9370       1052235/6787       0/0                1004174/9088       3683607/25245
Logspan  1:29:11.736        2:14:20.089                           23:38.834          2:14:24.766
Overhead 257314             253841                                253259             828717

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                867226
1000  system                                               529195
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  291477
  376/1000  /system/bin/hw/android.system.suspend-service  107154
  887/1000  /system_ext/bin/slogmodem                       78943
 1490/1000  com.android.settings                             8239
0     root                                                  65947
10187 com.android.systemui                                  46502
10142 com.google.android.apps.messaging                     26461
10145 com.google.android.googlequicksearchbox               15187
1082  artd                                                  14938
1041  audioserver                                           14872
10138 com.google.android.gms                                12614


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1028883
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  998544
  515/1000  /product/bin/ylog                               27782
10187 com.android.systemui                                  15816

run finished on 08-07 13:56:25


ylogctl q on Thu Aug  7 13:56:25 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 28442770 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   145C]->[    8CE] 
[   539]  kernel       -> Open    -> kernel.log       [    8C3]->[    707] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  246A2]->[   2187] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   54D6]->[   1415] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    3A9]->[    195] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 13:56:25


ylogctl space on Thu Aug  7 13:56:25 CST 2025
Root:/data/ylog/ap/   APLogSize:71 APLogMaxSize:46570 DiskFreeSpace:37897  DiskReserved:60
run finished on 08-07 13:56:25


cat /data/ylog/ylog.conf on Thu Aug  7 13:56:25 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 13:56:25


ls -l /data/ylog/ap/ on Thu Aug  7 13:56:26 CST 2025
total 73392
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 28454369 2025-08-07 13:56 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 13:56:26
ylogdebug end




ylogdebug_29  on 08-07 14:01:26


uptime on Thu Aug  7 14:01:26 CST 2025
 14:01:26 up  2:26,  0 users,  load average: 14.18, 14.42, 14.50
run finished on 08-07 14:01:26


logcat -S on Thu Aug  7 14:01:26 CST 2025
size/num main               system             crash              kernel             Total
Total    12396029/84481     4275324/26403      0/0                8554392/78622      25225745/189506
Now      1605926/9260       1073864/6929       0/0                1024594/9289       3704384/25478
Logspan  1:27:11.24         2:19:21.83                            24:19.457          2:19:25.984
Overhead 258604             253841                                251892             829336

Chattiest UIDs in main log buffer:                           Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
10246 com.antutu.ABenchMark                                849229
1000  system                                               537121
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                  284617
  376/1000  /system/bin/hw/android.system.suspend-service  105026
  887/1000  /system_ext/bin/slogmodem                       77169
 1490/1000  com.android.settings                            27126
0     root                                                  64627
10187 com.android.systemui                                  44206
10142 com.google.android.apps.messaging                     17225
10145 com.google.android.googlequicksearchbox               16834
1041  audioserver                                           14703
1082  artd                                                  14567
10138 com.google.android.gms                                13872


Chattiest UIDs in system log buffer:                         Size   +/-  Pruned
UID   PACKAGE                                               BYTES           NUM
1000  system                                              1050203
  PID/UID   COMMAND LINE                                       "
 1227/1000  system_server                                 1018837
  515/1000  /product/bin/ylog                               28809
10187 com.android.systemui                                  16125

run finished on 08-07 14:01:26


ylogctl q on Thu Aug  7 14:01:27 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 29250928 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [   1491]->[    8FB] 
[   539]  kernel       -> Open    -> kernel.log       [    902]->[    743] 
[   540]  trace        -> Open    -> trace.log        [     FD]->[     4A] 
[   541]  sgm          -> Open    -> sgm.csv          [  25B9A]->[   22BF] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [   579B]->[   14B7] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [    3C8]->[    19D] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     82]->[      C] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [     48]->[      E] 

run finished on 08-07 14:01:27


ylogctl space on Thu Aug  7 14:01:27 CST 2025
Root:/data/ylog/ap/   APLogSize:72 APLogMaxSize:46570 DiskFreeSpace:37888  DiskReserved:60
run finished on 08-07 14:01:27


cat /data/ylog/ylog.conf on Thu Aug  7 14:01:27 CST 2025
VERSION,1
status,enable
aplogrotate,enable
prioritypath,internal
sroot,default
aplogfilesize,256
aplogmaxsize,99%
uboot,1
lastlog,1
kernel,1
android,1
hcidump,1
tcpdump,1
sgm,1
sysinfo,1
thermal,0
ylogdebug,1
phoneinfo,1
trace,1
trustlog,1
tcpdump_c,-s 3000
sublog,1


run finished on 08-07 14:01:27


ls -l /data/ylog/ap/ on Thu Aug  7 14:01:27 CST 2025
total 74176
-rw-rw-rw- 1 <USER>   <GROUP>  1191146 2025-08-06 17:29 000-0101_081204_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 42653095 2025-08-07 11:33 001-0806_065620_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>  2742477 2025-08-07 11:35 002-0807_113411_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP> 29250928 2025-08-07 14:01 003-0807_113535_poweron.ylog
-rw-rw-rw- 1 <USER>   <GROUP>    19006 1970-01-01 08:12 analyzer.py
drwxrwxrwx 4 <USER>   <GROUP>     3452 2025-08-07 11:35 blackboxlog
drwxrwxrwx 3 <USER>   <GROUP>     3452 2025-08-07 11:35 cachelog
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-06 17:56 hcidump
drwxrwxrwx 2 <USER> <GROUP>     3452 2025-08-07 11:35 tcpdump
run finished on 08-07 14:01:27
ylogdebug end




