c0  args->params[0] is 0x20. ###
[    1.883358] c0 enter vboot verify
[    1.883361] c0 SECUREBOOTDataVal is not NULL.
[    1.883363] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.883371] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    1.883374] c0 from uboot smc...addr:0x99800000 len:0x600000
[    1.883479] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.883484] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    1.883487] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    1.883491] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    1.883494] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6c12000
[    1.883497] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6c13000
[    1.883500] c0 img_name:l_agdsp, g_avbUserData.img_name:l_agdsp.
[    1.883503] c0 avb check image name:l_agdsp.
[    1.883506] c0 ### enter avb_slot_verify. ###
[    1.883512] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.883517] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe6c0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.883529] c0 [kbc] save vbmeta image for cp verify.
[    1.883539] c0 enter implement read_rollback_index().
[    1.883541] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.883543] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.883547] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.883554] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.883556] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883560] c0 Skip verify chain_partition[boot] while load verify l_agdsp partition... 
[    1.883563] c0 n = 0
[    1.883564] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883567] c0 Skip verify chain_partition[dtbo] while load verify l_agdsp partition... 
[    1.883569] c0 n = 1
[    1.883571] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883574] c0 Skip verify chain_partition[init_boot] while load verify l_agdsp partition... 
[    1.883576] c0 n = 2
[    1.883578] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883581] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_agdsp partition... 
[    1.883583] c0 n = 3
[    1.883584] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883587] c0 Skip verify chain_partition[vbmeta_product] while load verify l_agdsp partition... 
[    1.883590] c0 n = 4
[    1.883591] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883594] c0 Skip verify chain_partition[vbmeta_system] while load verify l_agdsp partition... 
[    1.883597] c0 n = 5
[    1.883598] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883601] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_agdsp partition... 
[    1.883604] c0 n = 6
[    1.883605] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883608] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_agdsp partition... 
[    1.883610] c0 n = 7
[    1.883612] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883615] c0 Skip verify chain_partition[vendor_boot] while load verify l_agdsp partition... 
[    1.883617] c0 n = 8
[    1.883619] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883621] c0 Skip verify chain_partition[l_modem] while load verify l_agdsp partition... 
[    1.883624] c0 n = 9
[    1.883625] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883628] c0 Skip verify chain_partition[l_ldsp] while load verify l_agdsp partition... 
[    1.883631] c0 n = 10
[    1.883632] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883635] c0 Skip verify chain_partition[l_gdsp] while load verify l_agdsp partition... 
[    1.883637] c0 n = 11
[    1.883639] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883642] c0 Skip verify chain_partition[pm_sys] while load verify l_agdsp partition... 
[    1.883644] c0 n = 12
[    1.883646] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.883649] c0 chain partition founded: n = 13
[    1.883652] c0 read partition l_agdsp, offset: 0x5fffc0, bytes: 0x40.
[    1.883655] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x5fffc0 num_bytes:0x40
[    1.883661] c0 Loading vbmeta struct in footer from partition 'read partition l_agdsp, offset: 0x500000, bytes: 0x840.
[    1.883665] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x500000 num_bytes:0x840
[    1.886266] c0 partition: l_agdsp vbmeta_verify_ret is :0. 
[    1.886274] c0 enter implement read_rollback_index().
[    1.886275] c0 read_is_device_unlocked() rollback_index_slot = 15 
[    1.886278] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.886281] c0 Info: g_sprd_vboot_version.img_ver[15]= 0
[    1.886284] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    1.886287] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    1.886293] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    1.886294] c0 check dat cp
[    1.913114] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    1.913121] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    1.913125] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.913128] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.913131] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.913135] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.913137] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.913140] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.913144] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    1.913149] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    1.913160] c0 read_is_device_unlocked() ret = 0 
[    1.913610] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    1.913612] c0 partition: vbmeta  guid_buf_size = 37 
[    1.913615] c0 guid: 1.0 
[    1.913621] c0 in avb_slot_verify, l:1626. 
[    1.913623] c0 in avb_slot_verify, l:1640. 
[    1.913626] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    1.913630] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    1.913631] c0 slot_data[0] is 0xe04d8458.
[    1.913634] c0 slot_data[1] is 0x0.
[    1.913637] c0 vboot_verify_ret is :0
[    1.913642] c0 enter copy just debug... 344.
[    1.913645] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20416 androidboot.vbmeta.digest=7d24cca530456862be1d7961fb247a5ffd4b3975f5e7ffd0ac37
[    1.920300] c0 have got sip smc all from uboot###
[    1.920303] c0  args->params[0] is 0x20. ###
[    1.920307] c0 enter vboot verify
[    1.920309] c0 SECUREBOOTDataVal is not NULL.
[    1.920311] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.920328] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    1.920332] c0 from uboot smc...addr:0x99800000 len:0x800000
[    1.920470] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.920475] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    1.920479] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    1.920483] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    1.920486] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6e12000
[    1.920489] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6e13000
[    1.920492] c0 img_name:dtbo, g_avbUserData.img_name:dtbo.
[    1.920495] c0 avb check image name:dtbo.
[    1.920498] c0 ### enter avb_slot_verify. ###
[    1.920507] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.920512] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe6e0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.920524] c0 [kbc] save vbmeta image for cp verify.
[    1.920534] c0 enter implement read_rollback_index().
[    1.920535] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.920538] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.920542] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.920549] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.920553] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.920558] c0 Skip verify chain_partition[boot] while load verify dtbo partition... 
[    1.920560] c0 n = 0
[    1.920562] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.920565] c0 chain partition founded: n = 1
[    1.920570] c0 read partition dtbo, offset: 0x7fffc0, bytes: 0x40.
[    1.920572] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x7fffc0 num_bytes:0x40
[    1.920579] c0 Loading vbmeta struct in footer from partition 'read partition dtbo, offset: 0x8e000, bytes: 0x840.
[    1.920582] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x8e000 num_bytes:0x840
[    1.923225] c0 partition: dtbo vbmeta_verify_ret is :0. 
[    1.923233] c0 enter implement read_rollback_index().
[    1.923235] c0 read_is_device_unlocked() rollback_index_slot = 6 
[    1.923237] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.923240] c0 Info: g_sprd_vboot_version.img_ver[6]= 0
[    1.923242] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[    1.923245] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    1.923251] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    1.923252] c0 check dat cp
[    1.926299] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    1.926303] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    1.926308] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926312] c0 Skip verify chain_partition[init_boot] while load verify dtbo partition... 
[    1.926314] c0 n = 2
[    1.926316] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926319] c0 Skip verify chain_partition[vbmeta_odm] while load verify dtbo partition... 
[    1.926321] c0 n = 3
[    1.926323] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926326] c0 Skip verify chain_partition[vbmeta_product] while load verify dtbo partition... 
[    1.926328] c0 n = 4
[    1.926330] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926333] c0 Skip verify chain_partition[vbmeta_system] while load verify dtbo partition... 
[    1.926336] c0 n = 5
[    1.926337] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926340] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify dtbo partition... 
[    1.926343] c0 n = 6
[    1.926344] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926347] c0 Skip verify chain_partition[vbmeta_vendor] while load verify dtbo partition... 
[    1.926350] c0 n = 7
[    1.926351] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926355] c0 Skip verify chain_partition[vendor_boot] while load verify dtbo partition... 
[    1.926357] c0 n = 8
[    1.926359] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926362] c0 Skip verify chain_partition[l_modem] while load verify dtbo partition... 
[    1.926365] c0 n = 9
[    1.926366] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926369] c0 Skip verify chain_partition[l_ldsp] while load verify dtbo partition... 
[    1.926371] c0 n = 10
[    1.926376] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926379] c0 Skip verify chain_partition[l_gdsp] while load verify dtbo partition... 
[    1.926381] c0 n = 11
[    1.926383] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926390] c0 Skip verify chain_partition[pm_sys] while load verify dtbo partition... 
[    1.926392] c0 n = 12
[    1.926394] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.926396] c0 Skip verify chain_partition[l_agdsp] while load verify dtbo partition... 
[    1.926399] c0 n = 13
[    1.926400] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.926403] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.926406] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.926409] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.926412] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.926414] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.926418] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    1.926422] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    1.926431] c0 read_is_device_unlocked() ret = 0 
[    1.926868] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    1.926872] c0 partition: vbmeta  guid_buf_size = 37 
[    1.926874] c0 guid: 1.0 
[    1.926881] c0 in avb_slot_verify, l:1626. 
[    1.926884] c0 in avb_slot_verify, l:1640. 
[    1.926887] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    1.926890] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    1.926892] c0 slot_data[0] is 0xe04d8458.
[    1.926895] c0 slot_data[1] is 0x0.
[    1.926897] c0 vboot_verify_ret is :0
[    1.926901] c0 enter copy just debug... 344.
[    1.926904] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20416 androidboot.vbmeta.digest=f8ab3b221a840b8128cd0c31673f6ce804a4d2430a8a07598b58
[    1.973670] c0 have got sip smc all from uboot###
[    1.973672] c0  args->params[0] is 0x20. ###
[    1.973675] c0 enter vboot verify
[    1.973678] c0 SECUREBOOTDataVal is not NULL.
[    1.973679] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.973687] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    1.973690] c0 from uboot smc...addr:0x99800000 len:0x800000
[    1.973826] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.973830] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    1.973833] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    1.973836] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    1.973840] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6e12000
[    1.973843] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6e13000
[    1.973846] c0 img_name:init_boot, g_avbUserData.img_name:init_boot.
[    1.973848] c0 avb check image name:init_boot.
[    1.973850] c0 ### enter avb_slot_verify. ###
[    1.973856] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.973861] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe6e0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.973871] c0 [kbc] save vbmeta image for cp verify.
[    1.973879] c0 enter implement read_rollback_index().
[    1.973881] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.973883] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.973886] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.973893] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.973896] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.973900] c0 Skip verify chain_partition[boot] while load verify init_boot partition... 
[    1.973902] c0 n = 0
[    1.973904] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.973908] c0 Skip verify chain_partition[dtbo] while load verify init_boot partition... 
[    1.973910] c0 n = 1
[    1.973912] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.973915] c0 chain partition founded: n = 2
[    1.973918] c0 read partition init_boot, offset: 0x7fffc0, bytes: 0x40.
[    1.973920] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x7fffc0 num_bytes:0x40
[    1.973926] c0 Loading vbmeta struct in footer from partition 'read partition init_boot, offset: 0x2c3000, bytes: 0x900.
[    1.973929] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x2c3000 num_bytes:0x900
[    1.976482] c0 partition: init_boot vbmeta_verify_ret is :0. 
[    1.976489] c0 enter implement read_rollback_index().
[    1.976491] c0 read_is_device_unlocked() rollback_index_slot = 8 
[    1.976495] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.976498] c0 Info: g_sprd_vboot_version.img_ver[8]= 0
[    1.976501] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:3.
[    1.976504] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    1.976508] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    1.976510] c0 check dat cp
[    1.991379] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    1.991384] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991388] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991392] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    1.991395] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991400] c0 Skip verify chain_partition[vbmeta_odm] while load verify init_boot partition... 
[    1.991403] c0 n = 3
[    1.991404] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991408] c0 Skip verify chain_partition[vbmeta_product] while load verify init_boot partition... 
[    1.991410] c0 n = 4
[    1.991412] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991416] c0 Skip verify chain_partition[vbmeta_system] while load verify init_boot partition... 
[    1.991418] c0 n = 5
[    1.991420] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991424] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify init_boot partition... 
[    1.991426] c0 n = 6
[    1.991429] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991432] c0 Skip verify chain_partition[vbmeta_vendor] while load verify init_boot partition... 
[    1.991434] c0 n = 7
[    1.991436] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991439] c0 Skip verify chain_partition[vendor_boot] while load verify init_boot partition... 
[    1.991442] c0 n = 8
[    1.991444] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991448] c0 Skip verify chain_partition[l_modem] while load verify init_boot partition... 
[    1.991450] c0 n = 9
[    1.991452] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991455] c0 Skip verify chain_partition[l_ldsp] while load verify init_boot partition... 
[    1.991458] c0 n = 10
[    1.991460] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991463] c0 Skip verify chain_partition[l_gdsp] while load verify init_boot partition... 
[    1.991465] c0 n = 11
[    1.991467] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991470] c0 Skip verify chain_partition[pm_sys] while load verify init_boot partition... 
[    1.991473] c0 n = 12
[    1.991474] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.991478] c0 Skip verify chain_partition[l_agdsp] while load verify init_boot partition... 
[    1.991480] c0 n = 13
[    1.991482] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991484] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991487] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991490] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991493] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991495] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    1.991500] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    1.991504] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    1.991515] c0 read_is_device_unlocked() ret = 0 
[    1.991961] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    1.991964] c0 partition: vbmeta  guid_buf_size = 37 
[    1.991967] c0 guid: 1.0 
[    1.991974] c0 in avb_slot_verify, l:1626. 
[    1.991977] c0 in avb_slot_verify, l:1640. 
[    1.991982] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    1.991984] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    1.991986] c0 slot_data[0] is 0xe04d8458.
[    1.991987] c0 slot_data[1] is 0x0.
[    1.991990] c0 vboot_verify_ret is :0
[    1.991994] c0 enter copy just debug... 344.
[    1.991997] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20608 androidboot.vbmeta.digest=24fef36f72dc4ff94eb65e03be2bae85d4dea65a0a361a843127
[    1.998166] c0 have got sip smc all from uboot###
[    1.998168] c0  args->params[0] is 0x20. ###
[    1.998171] c0 enter vboot verify
[    1.998173] c0 SECUREBOOTDataVal is not NULL.
[    1.998175] c0 from uboot smc...addr:0x98100000 len:0x70
[    1.998182] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    1.998186] c0 from uboot smc...addr:0xc6400000 len:0x6400000
[    1.999833] c0 from uboot smc...addr:0x99000000 len:0x5000
[    1.999838] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    1.999842] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    1.999845] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    1.999849] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xeca12000
[    1.999851] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xeca13000
[    1.999855] c0 img_name:vendor_boot, g_avbUserData.img_name:vendor_boot.
[    1.999858] c0 avb check image name:vendor_boot.
[    1.999860] c0 ### enter avb_slot_verify. ###
[    1.999866] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    1.999871] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xeca0b000, offset_from_partition:0x0 num_bytes:0x5000
[    1.999883] c0 [kbc] save vbmeta image for cp verify.
[    1.999893] c0 enter implement read_rollback_index().
[    1.999895] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    1.999897] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    1.999900] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    1.999907] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    1.999911] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999915] c0 Skip verify chain_partition[boot] while load verify vendor_boot partition... 
[    1.999917] c0 n = 0
[    1.999919] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999922] c0 Skip verify chain_partition[dtbo] while load verify vendor_boot partition... 
[    1.999924] c0 n = 1
[    1.999926] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999929] c0 Skip verify chain_partition[init_boot] while load verify vendor_boot partition... 
[    1.999931] c0 n = 2
[    1.999933] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999936] c0 Skip verify chain_partition[vbmeta_odm] while load verify vendor_boot partition... 
[    1.999938] c0 n = 3
[    1.999940] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999943] c0 Skip verify chain_partition[vbmeta_product] while load verify vendor_boot partition... 
[    1.999945] c0 n = 4
[    1.999947] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999949] c0 Skip verify chain_partition[vbmeta_system] while load verify vendor_boot partition... 
[    1.999952] c0 n = 5
[    1.999953] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999956] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vendor_boot partition... 
[    1.999959] c0 n = 6
[    1.999960] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999963] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vendor_boot partition... 
[    1.999966] c0 n = 7
[    1.999967] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    1.999970] c0 chain partition founded: n = 8
[    1.999974] c0 read partition vendor_boot, offset: 0x63fffc0, bytes: 0x40.
[    1.999977] c0 buf:0xe04e3728, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x63fffc0 num_bytes:0x40
[    1.999982] c0 Loading vbmeta struct in footer from partition 'read partition vendor_boot, offset: 0x200d000, bytes: 0x8c0.
[    1.999986] c0 buf:0xe06e9640, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x200d000 num_bytes:0x8c0
[    2.002551] c0 partition: vendor_boot vbmeta_verify_ret is :0. 
[    2.002560] c0 enter implement read_rollback_index().
[    2.002561] c0 read_is_device_unlocked() rollback_index_slot = 18 
[    2.002564] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.002566] c0 Info: g_sprd_vboot_version.img_ver[18]= 0
[    2.002569] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:2.
[    2.002572] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[    2.002579] c0 avb_ops_get_image_buffer: img_addr = 0xe660a000
[    2.002580] c0 check dat cp
[    2.173847] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[    2.173854] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173859] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.173864] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.173868] c0 Skip verify chain_partition[l_modem] while load verify vendor_boot partition... 
[    2.173871] c0 n = 9
[    2.173874] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.173877] c0 Skip verify chain_partition[l_ldsp] while load verify vendor_boot partition... 
[    2.173880] c0 n = 10
[    2.173882] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.173885] c0 Skip verify chain_partition[l_gdsp] while load verify vendor_boot partition... 
[    2.173888] c0 n = 11
[    2.173889] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.173893] c0 Skip verify chain_partition[pm_sys] while load verify vendor_boot partition... 
[    2.173896] c0 n = 12
[    2.173898] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.173901] c0 Skip verify chain_partition[l_agdsp] while load verify vendor_boot partition... 
[    2.173904] c0 n = 13
[    2.173906] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173909] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173913] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173916] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173919] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173921] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.173926] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.173930] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[    2.173941] c0 read_is_device_unlocked() ret = 0 
[    2.174385] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.174388] c0 partition: vbmeta  guid_buf_size = 37 
[    2.174391] c0 guid: 1.0 
[    2.174398] c0 in avb_slot_verify, l:1626. 
[    2.174401] c0 in avb_slot_verify, l:1640. 
[    2.174404] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.174406] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.174409] c0 slot_data[0] is 0xe04d8458.
[    2.174411] c0 slot_data[1] is 0x0.
[    2.174414] c0 vboot_verify_ret is :0
[    2.174418] c0 enter copy just debug... 344.
[    2.174421] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20544 androidboot.vbmeta.digest=a74918061451928706e29cd9ba3193f18e88804727373181ecd9
[    2.180056] c0 have got sip smc all from uboot###
[    2.180058] c0  args->params[0] is 0x20. ###
[    2.180061] c0 enter vboot verify
[    2.180064] c0 SECUREBOOTDataVal is not NULL.
[    2.180066] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.180071] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    2.180075] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.180078] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.180082] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    2.180085] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    2.180089] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    2.180092] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.180095] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.180098] c0 img_name:vbmeta_system, g_avbUserData.img_name:vbmeta_system.
[    2.180101] c0 avb check image name:vbmeta_system.
[    2.180104] c0 ### enter avb_slot_verify. ###
[    2.180111] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.180116] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.180128] c0 [kbc] save vbmeta image for cp verify.
[    2.180137] c0 enter implement read_rollback_index().
[    2.180138] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.180141] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.180145] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.180151] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.180154] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.180158] c0 Skip verify chain_partition[boot] while load verify vbmeta_system partition... 
[    2.180161] c0 n = 0
[    2.180162] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.180166] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_system partition... 
[    2.180168] c0 n = 1
[    2.180170] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.180173] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_system partition... 
[    2.180175] c0 n = 2
[    2.180177] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.180180] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_system partition... 
[    2.180182] c0 n = 3
[    2.180184] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.180187] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_system partition... 
[    2.180189] c0 n = 4
[    2.180191] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.180196] c0 chain partition founded: n = 5
[    2.180199] c0 Loading vbmeta struct from partition 'read partition vbmeta_system, offset: 0x0, bytes: 0x1000.
[    2.180202] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.182901] c0 partition: vbmeta_system vbmeta_verify_ret is :0. 
[    2.182909] c0 enter implement read_rollback_index().
[    2.182911] c0 read_is_device_unlocked() rollback_index_slot = 2 
[    2.182916] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.182918] c0 Info: g_sprd_vboot_version.img_ver[2]= 0
[    2.182921] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:8.
[    2.182924] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.182927] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.182930] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.182932] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.182935] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.182937] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.182940] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.182944] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.182947] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.182949] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182952] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_system partition... 
[    2.182955] c0 n = 6
[    2.182957] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182959] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_system partition... 
[    2.182962] c0 n = 7
[    2.182963] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182966] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_system partition... 
[    2.182969] c0 n = 8
[    2.182970] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182973] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_system partition... 
[    2.182976] c0 n = 9
[    2.182977] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182980] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_system partition... 
[    2.182983] c0 n = 10
[    2.182984] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182987] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_system partition... 
[    2.182989] c0 n = 11
[    2.182991] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.182994] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_system partition... 
[    2.182996] c0 n = 12
[    2.182998] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183000] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_system partition... 
[    2.183003] c0 n = 13
[    2.183004] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.183007] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.183010] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.183012] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.183015] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.183017] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.183020] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.183024] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.183031] c0 read_is_device_unlocked() ret = 0 
[    2.183475] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.183481] c0 partition: vbmeta  guid_buf_size = 37 
[    2.183484] c0 guid: 1.0 
[    2.183490] c0 in avb_slot_verify, l:1626. 
[    2.183492] c0 in avb_slot_verify, l:1640. 
[    2.183495] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.183497] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.183499] c0 slot_data[0] is 0xe04d8458.
[    2.183500] c0 slot_data[1] is 0x0.
[    2.183502] c0 vboot_verify_ret is :0
[    2.183506] c0 enter copy just debug... 344.
[    2.183508] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=21312 androidboot.vbmeta.digest=e309cdb387dedfaafd1f639d9943cf440477a1ec360a2b81aea7
[    2.183880] c0 have got sip smc all from uboot###
[    2.183883] c0  args->params[0] is 0x20. ###
[    2.183884] c0 enter vboot verify
[    2.183886] c0 SECUREBOOTDataVal is not NULL.
[    2.183887] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.183892] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    2.183895] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.183899] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.183902] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    2.183905] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    2.183909] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    2.183912] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.183914] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.183916] c0 img_name:vbmeta_vendor, g_avbUserData.img_name:vbmeta_vendor.
[    2.183919] c0 avb check image name:vbmeta_vendor.
[    2.183921] c0 ### enter avb_slot_verify. ###
[    2.183928] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.183932] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.183943] c0 [kbc] save vbmeta image for cp verify.
[    2.183950] c0 enter implement read_rollback_index().
[    2.183952] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.183954] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.183956] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.183960] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.183964] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183967] c0 Skip verify chain_partition[boot] while load verify vbmeta_vendor partition... 
[    2.183969] c0 n = 0
[    2.183971] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183974] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_vendor partition... 
[    2.183976] c0 n = 1
[    2.183978] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183980] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_vendor partition... 
[    2.183983] c0 n = 2
[    2.183987] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183990] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_vendor partition... 
[    2.183992] c0 n = 3
[    2.183994] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.183997] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_vendor partition... 
[    2.183999] c0 n = 4
[    2.184001] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.184004] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_vendor partition... 
[    2.184007] c0 n = 5
[    2.184008] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.184011] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_vendor partition... 
[    2.184014] c0 n = 6
[    2.184015] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.184018] c0 chain partition founded: n = 7
[    2.184021] c0 Loading vbmeta struct from partition 'read partition vbmeta_vendor, offset: 0x0, bytes: 0x1000.
[    2.184026] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.186639] c0 partition: vbmeta_vendor vbmeta_verify_ret is :0. 
[    2.186647] c0 enter implement read_rollback_index().
[    2.186649] c0 read_is_device_unlocked() rollback_index_slot = 4 
[    2.186651] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.186653] c0 Info: g_sprd_vboot_version.img_ver[4]= 0
[    2.186656] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:4.
[    2.186661] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186663] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186666] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186669] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.186672] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.186675] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186677] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_vendor partition... 
[    2.186680] c0 n = 8
[    2.186681] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186684] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_vendor partition... 
[    2.186687] c0 n = 9
[    2.186688] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186691] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_vendor partition... 
[    2.186695] c0 n = 10
[    2.186697] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186700] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_vendor partition... 
[    2.186702] c0 n = 11
[    2.186704] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186706] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_vendor partition... 
[    2.186709] c0 n = 12
[    2.186710] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.186713] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_vendor partition... 
[    2.186716] c0 n = 13
[    2.186717] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186720] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186722] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186725] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186728] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186730] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.186733] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.186736] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.186743] c0 read_is_device_unlocked() ret = 0 
[    2.187172] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.187175] c0 partition: vbmeta  guid_buf_size = 37 
[    2.187177] c0 guid: 1.0 
[    2.187182] c0 in avb_slot_verify, l:1626. 
[    2.187184] c0 in avb_slot_verify, l:1640. 
[    2.187187] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.187190] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.187191] c0 slot_data[0] is 0xe04d8458.
[    2.187193] c0 slot_data[1] is 0x0.
[    2.187195] c0 vboot_verify_ret is :0
[    2.187198] c0 enter copy just debug... 344.
[    2.187201] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20736 androidboot.vbmeta.digest=6f4e9deb9a57a887ceae15f76da783f7309b987bb4f5cf3a717b
[    2.187576] c0 have got sip smc all from uboot###
[    2.187578] c0  args->params[0] is 0x20. ###
[    2.187580] c0 enter vboot verify
[    2.187582] c0 SECUREBOOTDataVal is not NULL.
[    2.187584] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.187588] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    2.187592] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.187595] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.187598] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    2.187603] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    2.187607] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    2.187610] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.187612] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.187615] c0 img_name:vbmeta_system_ext, g_avbUserData.img_name:vbmeta_system_ext.
[    2.187617] c0 avb check image name:vbmeta_system_ext.
[    2.187619] c0 ### enter avb_slot_verify. ###
[    2.187624] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.187627] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.187637] c0 [kbc] save vbmeta image for cp verify.
[    2.187644] c0 enter implement read_rollback_index().
[    2.187646] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.187648] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.187650] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.187654] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.187657] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187660] c0 Skip verify chain_partition[boot] while load verify vbmeta_system_ext partition... 
[    2.187663] c0 n = 0
[    2.187664] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187667] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_system_ext partition... 
[    2.187673] c0 n = 1
[    2.187674] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187677] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_system_ext partition... 
[    2.187680] c0 n = 2
[    2.187681] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187684] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_system_ext partition... 
[    2.187686] c0 n = 3
[    2.187688] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187691] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_system_ext partition... 
[    2.187693] c0 n = 4
[    2.187695] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187698] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_system_ext partition... 
[    2.187700] c0 n = 5
[    2.187702] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.187705] c0 chain partition founded: n = 6
[    2.187710] c0 Loading vbmeta struct from partition 'read partition vbmeta_system_ext, offset: 0x0, bytes: 0x1000.
[    2.187714] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.190329] c0 partition: vbmeta_system_ext vbmeta_verify_ret is :0. 
[    2.190336] c0 enter implement read_rollback_index().
[    2.190338] c0 read_is_device_unlocked() rollback_index_slot = 3 
[    2.190340] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.190342] c0 Info: g_sprd_vboot_version.img_ver[3]= 0
[    2.190345] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:6.
[    2.190348] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190351] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190353] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190356] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190359] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.190362] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.190365] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.190367] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190370] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_system_ext partition... 
[    2.190373] c0 n = 7
[    2.190374] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190377] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_system_ext partition... 
[    2.190380] c0 n = 8
[    2.190381] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190384] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_system_ext partition... 
[    2.190387] c0 n = 9
[    2.190388] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190391] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_system_ext partition... 
[    2.190394] c0 n = 10
[    2.190395] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190398] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_system_ext partition... 
[    2.190401] c0 n = 11
[    2.190402] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190405] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_system_ext partition... 
[    2.190407] c0 n = 12
[    2.190409] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.190412] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_system_ext partition... 
[    2.190417] c0 n = 13
[    2.190418] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190421] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190423] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190426] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190429] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190431] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.190434] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.190437] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.190443] c0 read_is_device_unlocked() ret = 0 
[    2.190881] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.190884] c0 partition: vbmeta  guid_buf_size = 37 
[    2.190886] c0 guid: 1.0 
[    2.190892] c0 in avb_slot_verify, l:1626. 
[    2.190894] c0 in avb_slot_verify, l:1640. 
[    2.190899] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.190901] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.190903] c0 slot_data[0] is 0xe04d8458.
[    2.190905] c0 slot_data[1] is 0x0.
[    2.190907] c0 vboot_verify_ret is :0
[    2.190910] c0 enter copy just debug... 344.
[    2.190912] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=21184 androidboot.vbmeta.digest=3c6ad205c5f5c5cce6e11dcd59709a5519a1f1d34a45123c89b9
[    2.191283] c0 have got sip smc all from uboot###
[    2.191286] c0  args->params[0] is 0x20. ###
[    2.191287] c0 enter vboot verify
[    2.191289] c0 SECUREBOOTDataVal is not NULL.
[    2.191290] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.191295] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    2.191298] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.191302] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.191305] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    2.191308] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    2.191312] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    2.191315] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.191317] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.191319] c0 img_name:vbmeta_product, g_avbUserData.img_name:vbmeta_product.
[    2.191322] c0 avb check image name:vbmeta_product.
[    2.191324] c0 ### enter avb_slot_verify. ###
[    2.191329] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.191332] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.191342] c0 [kbc] save vbmeta image for cp verify.
[    2.191349] c0 enter implement read_rollback_index().
[    2.191351] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.191353] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.191355] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.191359] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.191362] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191365] c0 Skip verify chain_partition[boot] while load verify vbmeta_product partition... 
[    2.191368] c0 n = 0
[    2.191369] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191372] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_product partition... 
[    2.191375] c0 n = 1
[    2.191376] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191379] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_product partition... 
[    2.191382] c0 n = 2
[    2.191383] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191386] c0 Skip verify chain_partition[vbmeta_odm] while load verify vbmeta_product partition... 
[    2.191388] c0 n = 3
[    2.191390] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.191393] c0 chain partition founded: n = 4
[    2.191396] c0 Loading vbmeta struct from partition 'read partition vbmeta_product, offset: 0x0, bytes: 0x1000.
[    2.191399] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.194180] c0 partition: vbmeta_product vbmeta_verify_ret is :0. 
[    2.194187] c0 enter implement read_rollback_index().
[    2.194188] c0 read_is_device_unlocked() rollback_index_slot = 5 
[    2.194190] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.194193] c0 Info: g_sprd_vboot_version.img_ver[5]= 0
[    2.194196] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:4.
[    2.194199] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194201] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194204] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194208] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.194211] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.194214] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194217] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_product partition... 
[    2.194220] c0 n = 5
[    2.194221] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194224] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_product partition... 
[    2.194227] c0 n = 6
[    2.194228] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194231] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_product partition... 
[    2.194234] c0 n = 7
[    2.194235] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194238] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_product partition... 
[    2.194241] c0 n = 8
[    2.194242] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194245] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_product partition... 
[    2.194250] c0 n = 9
[    2.194251] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194254] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_product partition... 
[    2.194257] c0 n = 10
[    2.194258] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194261] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_product partition... 
[    2.194263] c0 n = 11
[    2.194265] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194268] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_product partition... 
[    2.194270] c0 n = 12
[    2.194272] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.194275] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_product partition... 
[    2.194277] c0 n = 13
[    2.194279] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194281] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194284] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194286] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194291] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194294] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.194297] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.194300] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.194306] c0 read_is_device_unlocked() ret = 0 
[    2.194735] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.194738] c0 partition: vbmeta  guid_buf_size = 37 
[    2.194740] c0 guid: 1.0 
[    2.194745] c0 in avb_slot_verify, l:1626. 
[    2.194748] c0 in avb_slot_verify, l:1640. 
[    2.194750] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.194752] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.194753] c0 slot_data[0] is 0xe04d8458.
[    2.194755] c0 slot_data[1] is 0x0.
[    2.194757] c0 vboot_verify_ret is :0
[    2.194760] c0 enter copy just debug... 344.
[    2.194762] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20736 androidboot.vbmeta.digest=faa442ab6cd35437eb867734bd76b618bec262e7cfe9cc30bdb6
[    2.195134] c0 have got sip smc all from uboot###
[    2.195136] c0  args->params[0] is 0x20. ###
[    2.195138] c0 enter vboot verify
[    2.195139] c0 SECUREBOOTDataVal is not NULL.
[    2.195140] c0 from uboot smc...addr:0x98100000 len:0x70
[    2.195145] c0 from uboot smc...addr:0x9f4a5000 len:0x24
[    2.195148] c0 from uboot smc...addr:0x99800000 len:0x1000
[    2.195152] c0 from uboot smc...addr:0x99000000 len:0x5000
[    2.195155] c0 from uboot smc...addr:0x9f4a8000 len:0x20
[    2.195160] c0 from uboot smc...addr:0x9f4aa000 len:0x200
[    2.195163] c0 from uboot smc...addr:0x9f4a9000 len:0x10
[    2.195166] c0 vb_cmdline_len:512, vbmeta_pubkey_addr:0xe6613000
[    2.195169] c0 vb_cmdline_len:16, vbmeta_pubkey_addr:0xe6614000
[    2.195171] c0 img_name:vbmeta_odm, g_avbUserData.img_name:vbmeta_odm.
[    2.195174] c0 avb check image name:vbmeta_odm.
[    2.195175] c0 ### enter avb_slot_verify. ###
[    2.195180] c0 Loading vbmeta struct from partition 'read partition vbmeta, offset: 0x0, bytes: 0x5000.
[    2.195183] c0 buf:0xe077b018, g_avbUserData.vbmeta_img_addr:0xe660c000, offset_from_partition:0x0 num_bytes:0x5000
[    2.195193] c0 [kbc] save vbmeta image for cp verify.
[    2.195200] c0 enter implement read_rollback_index().
[    2.195202] c0 read_is_device_unlocked() rollback_index_slot = 0 
[    2.195204] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.195206] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[    2.195210] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[    2.195213] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.195216] c0 Skip verify chain_partition[boot] while load verify vbmeta_odm partition... 
[    2.195219] c0 n = 0
[    2.195220] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.195223] c0 Skip verify chain_partition[dtbo] while load verify vbmeta_odm partition... 
[    2.195226] c0 n = 1
[    2.195227] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.195230] c0 Skip verify chain_partition[init_boot] while load verify vbmeta_odm partition... 
[    2.195232] c0 n = 2
[    2.195234] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.195237] c0 chain partition founded: n = 3
[    2.195240] c0 Loading vbmeta struct from partition 'read partition vbmeta_odm, offset: 0x0, bytes: 0x1000.
[    2.195243] c0 buf:0xe070e048, g_avbUserData.img_addr:0xe660a000, offset_from_partition:0x0 num_bytes:0x1000
[    2.197949] c0 partition: vbmeta_odm vbmeta_verify_ret is :0. 
[    2.197956] c0 enter implement read_rollback_index().
[    2.197958] c0 read_is_device_unlocked() rollback_index_slot = 7 
[    2.197960] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[    2.197962] c0 Info: g_sprd_vboot_version.img_ver[7]= 0
[    2.197965] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:3.
[    2.197968] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197970] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.197973] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x1.
[    2.197976] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[    2.197979] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197982] c0 Skip verify chain_partition[vbmeta_product] while load verify vbmeta_odm partition... 
[    2.197984] c0 n = 4
[    2.197986] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197989] c0 Skip verify chain_partition[vbmeta_system] while load verify vbmeta_odm partition... 
[    2.197991] c0 n = 5
[    2.197993] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.197998] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify vbmeta_odm partition... 
[    2.198001] c0 n = 6
[    2.198002] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198005] c0 Skip verify chain_partition[vbmeta_vendor] while load verify vbmeta_odm partition... 
[    2.198008] c0 n = 7
[    2.198009] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198012] c0 Skip verify chain_partition[vendor_boot] while load verify vbmeta_odm partition... 
[    2.198014] c0 n = 8
[    2.198016] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198019] c0 Skip verify chain_partition[l_modem] while load verify vbmeta_odm partition... 
[    2.198021] c0 n = 9
[    2.198023] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198026] c0 Skip verify chain_partition[l_ldsp] while load verify vbmeta_odm partition... 
[    2.198028] c0 n = 10
[    2.198029] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198032] c0 Skip verify chain_partition[l_gdsp] while load verify vbmeta_odm partition... 
[    2.198035] c0 n = 11
[    2.198036] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198039] c0 Skip verify chain_partition[pm_sys] while load verify vbmeta_odm partition... 
[    2.198042] c0 n = 12
[    2.198043] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[    2.198046] c0 Skip verify chain_partition[l_agdsp] while load verify vbmeta_odm partition... 
[    2.198048] c0 n = 13
[    2.198050] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.198052] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.198055] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.198058] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.198060] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.198063] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[    2.198066] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[    2.198069] c0 in avb_slot_verify, l:1531, vbmeta, 2, 0
[    2.198075] c0 read_is_device_unlocked() ret = 0 
[    2.198503] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[    2.198505] c0 partition: vbmeta  guid_buf_size = 37 
[    2.198508] c0 guid: 1.0 
[    2.198513] c0 in avb_slot_verify, l:1626. 
[    2.198515] c0 in avb_slot_verify, l:1640. 
[    2.198517] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is 0.
[    2.198519] c0 >>>>>>>>>>>>>>>>  avb_slot_verify result is OK.
[    2.198521] c0 slot_data[0] is 0xe04d8458.
[    2.198523] c0 slot_data[1] is 0x0.
[    2.198525] c0 vboot_verify_ret is :0
[    2.198528] c0 enter copy just debug... 344.
[    2.198530] c0 avb_slot_data[0]->cmdline is androidboot.vbmeta.device=PARTUUID=1.0 androidboot.vbmeta.avb_version=1.1 androidboot.vbmeta.device_state=locked androidboot.vbmeta.hash_alg=sha256 androidboot.vbmeta.size=20672 androidboot.vbmeta.digest=2d28420c0a199ce2c0bb7a3e9545c48dc5a221b592297e822e4e
[    2.240495] c1 have got sip smc all from uboot###
[    2.240500] c1  args->params[0] is 0xa. ###
[    2.240615] c1 process_sip_call, case FUNCTYPE_GET_LCS res:0, tmp is 1
[    2.279273] c0 have got sip smc all from uboot###
[    2.279276] c0  args->params[0] is 0x36. ###
[    2.279279] c0 from uboot smc...addr:0x9f1a5000 len:0x400
[    2.279307] c0 xing offset = 25.
[    2.279310] c0 xing offset2 = 64.
[    2.279336] c0 xing offset = 25.
[    2.279338] c0 xing offset2 = 64.
[    2.283412] c0 have got sip smc all from uboot###
[    2.283413] c0  args->params[0] is 0x40. ###
[    2.283416] c0 from uboot smc(set chip uid)...addr:0x9f1a4000 len:0x8
[    2.283421] c0 get cpu id: 0x7439c2 0x401d409
[    2.283424] c0 chip uid from uboot(id=5) len=8:
[    2.283426] c0 c2 39 74 00 09 d4 01 04 
[    2.283433] c0 key[5] data has been saved!
[    2.283464] c0 have got sip smc all from uboot###
[    2.283466] c0  args->params[0] is 0x22. ###
[    2.283471] c0 enter set rpmb 4194304
[    2.285036] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285053] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285106] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285119] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285226] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285236] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
<    2.285315> ss: sec_rpmb_bl_client_handle_msg: use new key
[    2.285338] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285350] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285362] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
<    2.285372> ss-ipc: 185: do_disconnect ev->handle ox3ea
<    2.285380> ss: sec_rpmb_bl_disconnect: handle 0x3ea
[    2.285394] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285410] c0 have got sip smc all from uboot###
[    2.285412] c0  args->params[0] is 0x25. ###
[    2.285414] c0 set rpmb type 205
[    2.285448] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285469] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285523] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285536] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285570] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285579] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285599] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285643] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285664] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285679] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285706] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285715] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285736] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285769] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285792] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285804] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285839] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285848] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285868] c0 sm_decode_ns_memory_attr:89: raw=0xff00009f94b300: pa=0x9f94b000: mair=0xff, sharable=0x3
[    2.285900] c0 have got sip smc all from uboot###
[    2.285902] c0  args->params[0] is 0x18. ###
[    2.285916] c0 process_sip_call, case FUNCTYPE_CHECK_CPU_FLASH_BIND, res is:1
[    2.913915] c3 entering scheduler on cpu 3
[    2.916412] c4 entering scheduler on cpu 4
[    2.918658] c5 entering scheduler on cpu 5
[    2.920942] c6 entering scheduler on cpu 6
[    2.922969] c7 entering scheduler on cpu 7
[   12.167426] c4 tam_load_request:1513: load look up com.android.trusty.gatekeeper
[   12.167440] c4 handle_conn_req:412: failed (-2) to send response
<   13.742158> ss: cal_rpmb_block_size: rpmb write data-size 512 to addr (16378) sucessfull
<   13.742177> ss: cal_rpmb_block_size: set rpmb block size 512
<   13.747465> ss: block_device_tipc_rpmb_init: rpmb device's block size 512 block count 8178
<   13.752451> super_block_valid:344: bad magic, 0x7beacb50d2c449d7
<   13.756918> fs_init_from_super:603: super block: files at 6 free at 5
<   13.764576> fs_init_from_super:634: loaded super block version 1
[   13.764615] c4 tam_port_publish:1501:  other port com.android.trusty.storage.client.tp
<   13.764624> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tp success
[   13.764637] c4 tam_port_publish:1501:  other port com.android.trusty.storage.client.tdea
<   13.764642> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tdea success
<   13.764649> ss: block_device_tipc_rpmb_init: rb device's block size 4040 block count 1048576
<   13.777978> super_block_valid:344: bad magic, 0x7beacb50d2c449d7
<   13.781956> super_block_valid:344: bad magic, 0x7beacb50d2c449d7
<   13.781976> fs_init_from_super:641: clear requested, create empty, version 0
[   13.782026] c1 tam_port_publish:1501:  other port com.android.trusty.storage.client.tr
<   13.782036> ss: block_device_tipc_rpmb_init: create port com.android.trusty.storage.client.tr success
<   13.785467> sprdimgversion: sprdimgverion_create_port: rpmb read image/mode image version blk (16382,16380) successful.
[   13.785507] c0 tam_port_publish:1501:  other port com.spreadtrum.sprdimgversion
<   13.785517> ss: rpmb_proxy_connect: create port com.spreadtrum.sprdimgversion success
<   13.789924> sec_rpmbdata: sec_rpmbdata_create_port: rpmb read sec rpmb data (13, 1) (16365, 16377) successful.
[   13.789963] c0 tam_port_publish:1501:  other port com.spreadtrum.secrpmbdata
<   13.789972> ss: rpmb_proxy_connect: create port com.spreadtrum.secrpmbdata success
<   13.790542> ss: block_device_tipc_ns_init: ns device's block size 4040 block count 1048576
[   13.795976] c5 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   13.796184] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   13.796218] c5 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   13.796222] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   13.796225] c5 ta_manager_verify_img:447: RSA_hash
<   13.796999> block_cache_complete_read: load block 0 failed
<   13.797012> block_cache_load_entry: failed to load block 0
[   13.797427] c5 ta_manager_verify_img:506: RSA_verify
<   13.797482> block_cache_complete_read: load block 1 failed
<   13.797492> block_cache_load_entry: failed to load block 1
<   13.797502> fs_init_from_super:641: clear requested, create empty, version 0
[   13.797597] c5 tam_port_publish:1501:  other port com.android.trusty.storage.client.td
<   13.797605> ss: block_device_tipc_ns_init: create port com.android.trusty.storage.client.td success
[   13.799832] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   13.799844] c0 trusty_app:(32) start 0xffffffffe07de000 size 0x00017000
[   13.799854] c0 trusty_app: whitelist.table 0x0, size: 0
[   13.799858] c0 trusty_app 13 uuid: 0x38ba0cdc 0xdf0e 0x11e4 0x9869 0x233fb6ae4795
[   13.799868] c0 trusty_app 0xffffffffe04defb0: stack_sz=0x1000
[   13.799871] c0 trusty_app 0xffffffffe04defb0: heap_sz=0x2000
[   13.799874] c0 trusty_app 0xffffffffe04defb0: one_shot=0x0
[   13.799877] c0 trusty_app 0xffffffffe04defb0: keep_alive=0x0
[   13.799881] c0 trusty_app 0xffffffffe04defb0: flags=0x1c
[   13.799884] c0 ta_manager_write_ta:985: enter tam anti rollback
[   13.799892] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   13.799896] c0 ta_manager_write_ta:997: tam anti rollback ok
[   13.799899] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   13.799904] c0 trusty_tapp_init:
[   13.799977] c0 trusty_app 13: code: start 0x00008000 end 0x0001aa1c
[   13.799982] c0 trusty_app 13: data: start 0x0001b000 end 0x0001c000
[   13.799987] c0 trusty_app 13: bss:                end 0x0001b420
[   13.799990] c0 trusty_app 13: brk:  start 0x0001c000 end 0x0001e000
[   13.799995] c0 trusty_app 13: entry 0x0000b418
<   13.800175> trusty_gatekeeper_ta: 304: Initializing
<   13.800189> trusty_gatekeeper_ta: 89: ReseedRng
<   13.800380> trusty_gatekeeper_ta: 97: ReseedRng ok
[   13.800403] c0 tam_port_publish:1496: publish port com.android.trusty.gatekeeper
[   13.800417] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.gatekeeper accomplished!
<   13.831358> trusty_kernelbootcp: 75: cmd KERNEL_BOOTCP_UNLOCK_DDR
<   13.831372> trusty_kernelbootcp: 322: TA:kbc_unlock_ddr() 
[   13.831380] c6 enter SEC_KBC_GET_TEECFG_FLAG
[   13.831385] c6 pal:g_wifionly_flag = 0 
<   13.831387> trusty_kernelbootcp: 331: TA:g_ioctl_cunter = 1,g_wifionly_flag = 0
[   13.831393] c6 enter SEC_KBC_STOP_CP
[   13.831396] c6 dump_table() len = 0 maplen = 0 addr = 0 
[   13.831399] c6 dump_table() len = 0 maplen = 0 addr = 0 
[   13.831401] c6 dump_table() len = 0 maplen = 0 addr = 0 
[   13.831403] c6 dump_table() len = 0 maplen = 0 addr = 0 
[   13.831405] c6 dump_table() flag = 31 
[   13.831407] c6 dump_table() is_packed = 0 
[   13.831408] c6 kbc_stop_cp() enter MODEM_IMG
[   13.831410] c6 reg_addr = 0xffffffffe61dc174
[   13.831415] c6 before reg = 0400
[   13.831420] c6 after  reg = 0400
[   13.831422] c6 sleep 50ms start 
[   13.884135] c6 reg_addr = 0xffffffffe61ccb98
[   13.884145] c6 before reg = 4800
[   13.884151] c6 after  reg = 4800
[   13.884153] c6 sleep 50ms start 
[   13.934222] c6 reg_addr = 0xffffffffe61cc818
[   13.934232] c6 before reg = 0006
[   13.934238] c6 after  reg = 0006
[   13.934241] c6 sleep 50ms start 
[   13.984306] c6 reg_addr = 0xffffffffe61cc330
[   13.984317] c6 before reg = 2010101
[   13.984322] c6 after  reg = 2010101
[   13.984324] c6 sleep 50ms start 
[   14.034385] c6 reg_addr = 0xffffffffe61dc08c
[   14.034396] c6 before reg = 0001
[   14.034403] c6 after  reg = 0001
[   14.034405] c6 sleep 50ms start SP_IMG
[   14.084460] c6 sleep end 
[   14.084464] c6 kbc_stop_cp() leave 
[   14.084470] c6 enter SEC_KBC_GET_LOAD_MODEM_FLAG
[   14.084471] c6 pal: g_load_modem_flag = 0 
<   14.084475> trusty_kernelbootcp: 339: TA:g_ioctl_cunter = 2,g_load_modem_flag = 0
<   14.084487> trusty_kernelbootcp: 340: TA:SEC_KBC_STOP_CP() ret = 0
<   14.084493> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   14.084499> footer
<   14.084503> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084508> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084514> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084519> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084524> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   14.084529> footer
<   14.084532> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084537> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084541> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084546> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084551> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   14.084556> footer
<   14.084558> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084563> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084568> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084572> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084577> trusty_kernelbootcp: 281: dump_table() len = 0 maplen = 0 addr = 0 
<   14.084582> footer
<   14.084584> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084589> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084594> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084598> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   14.084603> trusty_kernelbootcp: 287: dump_table() flag = 31 
<   14.084608> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
<   14.084646> trusty_kernelbootcp: 348: TA:SEC_FIREWALL_UNLOCK_CP_DDR() ret = 0
<   14.084657> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
<   15.011827> trusty_kernelbootcp: 67: cmd KERNEL_BOOTCP_VERIFY_ALL
<   15.011846> trusty_kernelbootcp: 177: TA:kbc_verify_all_avb2() 
<   15.011853> trusty_kernelbootcp: 281: dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
<   15.011861> footer
<   15.011865> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   15.011873> 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
<   15.011881> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   15.011888> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.011896> trusty_kernelbootcp: 281: dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
<   15.011903> footer
<   15.011906> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   15.011914> 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
<   15.011921> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   15.011929> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.011936> trusty_kernelbootcp: 281: dump_table() len = 440000 maplen = 440000 addr = 89620000 
<   15.011943> footer
<   15.011946> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   15.011954> 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
<   15.011961> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   15.011968> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.011976> trusty_kernelbootcp: 281: dump_table() len = 200000 maplen = 200000 addr = 88040000 
<   15.011982> footer
<   15.011986> 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
<   15.011993> 0010 00 0b a0 00 00 00 00 00 00 0b a0 00 00 00 00 00
<   15.012001> 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
<   15.012143> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.012153> trusty_kernelbootcp: 287: dump_table() flag = 31 
<   15.012159> trusty_kernelbootcp: 288: dump_table() is_packed = 0 
[   15.012168] c5 enter sec_kbc_check_verify_table
<   15.012184> trusty_kernelbootcp: 167: TA:table->flag = 0x1f:
<   15.012253> trusty_kernelbootcp: 182: TA:SEC_FIREWALL_LOCK_CP_DRR() ret = 0
[   15.012259] c5 enter SEC_KBC_VERIFY_ALL_V2
[   15.012263] c5 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[   15.012268] c5 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[   15.012271] c5 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[   15.012275] c5 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[   15.012278] c5 dump_table() flag = 31 
[   15.012280] c5 dump_table() is_packed = 0 
[   15.012283] c5 [kbc]kbc_image_verify_v2() enter.
[   15.012286] c5 [kbc]call mem map:ns addr = 0x8b000000 map len = 0x1380000 
[   15.012598] c5 [kbc]after map:pAddr = 0xffffffffe662e000
[   15.012602] c5 pAddr:
[   15.012604] c5 0000 4d 45 43 50 56 31 2e 30 00 06 00 00 00 00 00 00
[   15.012619] c5 [kbc]avb_userdata_set() packed offset = 0x0
[   15.012622] c5 [kbc]start verify... 
[   15.012624] c5 [kbc]avb_check_kbc_image() name = l_modem
[   15.012627] c5 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_modem
[   15.012630] c5 ### enter avb_slot_verify. ###
[   15.012640] c5 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   15.012666] c5 [kbc]read_from_partition_kbc leave buf = 0xe07c00c0
[   15.015450] c5 partition: vbmeta vbmeta_verify_ret is :0. 
[   15.015456] c5 Enter: implement validate_vbmeta_public_key().
[   15.015459] c5 dump public_key_hash: public_key_hash_length:32. 
[   15.015461] c5 cal_sha256(): enter cal_sha256 
[   15.015470] c5 expected_public_key is matched.
[   15.015472] c5 enter implement read_rollback_index().
[   15.015475] c5 read_is_device_unlocked() rollback_index_slot = 0 
[   15.015478] c5 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.015482] c5 Info: g_sprd_vboot_version.img_ver[0]= 0
[   15.015488] c5 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   15.015492] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015497] c5 Skip verify chain_partition[boot] while load verify l_modem partition... 
[   15.015501] c5 n = 0
[   15.015502] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015506] c5 Skip verify chain_partition[dtbo] while load verify l_modem partition... 
[   15.015509] c5 n = 1
[   15.015511] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015514] c5 Skip verify chain_partition[init_boot] while load verify l_modem partition... 
[   15.015517] c5 n = 2
[   15.015519] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015523] c5 Skip verify chain_partition[vbmeta_odm] while load verify l_modem partition... 
[   15.015526] c5 n = 3
[   15.015527] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015531] c5 Skip verify chain_partition[vbmeta_product] while load verify l_modem partition... 
[   15.015534] c5 n = 4
[   15.015536] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015540] c5 Skip verify chain_partition[vbmeta_system] while load verify l_modem partition... 
[   15.015543] c5 n = 5
[   15.015545] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015548] c5 Skip verify chain_partition[vbmeta_system_ext] while load verify l_modem partition... 
[   15.015552] c5 n = 6
[   15.015553] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015557] c5 Skip verify chain_partition[vbmeta_vendor] while load verify l_modem partition... 
[   15.015560] c5 n = 7
[   15.015562] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015565] c5 Skip verify chain_partition[vendor_boot] while load verify l_modem partition... 
[   15.015569] c5 n = 8
[   15.015570] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.015574] c5 chain partition founded: n = 9
[   15.015579] c5 [kbc]read partition name: l_modem, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   15.015583] c5 [kbc]read footer
[   15.015585] c5 dump footer
[   15.015587] c5 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   15.015602] c5 0010 00 d3 8b 84 00 00 00 00 00 d3 90 00 00 00 00 00
[   15.015616] c5 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   15.015631] c5 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.015646] c5 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   15.015650] c5 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_modem, offset = 0xd39000, num_bytes = 0x840
[   15.015655] c5 [kbc]read certificate: img_addr = 0xe662e000
[   15.015657] c5 [kbc]read certificate: offset = 0xd39000
[   15.015661] c5 dump certificate
[   15.015663] c5 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   15.015678] c5 [kbc]read_from_partition_kbc leave buf = 0xe076c1f8
[   15.019740] c5 partition: l_modem vbmeta_verify_ret is :0. 
[   15.019757] c5 enter implement read_rollback_index().
[   15.019759] c5 read_is_device_unlocked() rollback_index_slot = 11 
[   15.019763] c5 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.019766] c5 Info: g_sprd_vboot_version.img_ver[11]= 0
[   15.019774] c5 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   15.019778] c5 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   15.019786] c5 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   15.019789] c5 check dat cp
[   15.144815] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   15.144826] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   15.144831] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.144837] c0 Skip verify chain_partition[l_ldsp] while load verify l_modem partition... 
[   15.144840] c0 n = 10
[   15.144842] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.144846] c0 Skip verify chain_partition[l_gdsp] while load verify l_modem partition... 
[   15.144850] c0 n = 11
[   15.144851] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.144855] c0 Skip verify chain_partition[pm_sys] while load verify l_modem partition... 
[   15.144858] c0 n = 12
[   15.144860] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.144864] c0 Skip verify chain_partition[l_agdsp] while load verify l_modem partition... 
[   15.144867] c0 n = 13
[   15.144869] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.144872] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.144876] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.144879] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.144883] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.144887] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.144891] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   15.144896] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   15.144908] c0 read_is_device_unlocked() ret = 0 
[   15.145424] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   15.145431] c0 partition: vbmeta  guid_buf_size = 37 
[   15.145434] c0 guid: 1.0 
[   15.145440] c0 in avb_slot_verify, l:1626. 
[   15.145443] c0 in avb_slot_verify, l:1640. 
[   15.145447] c0 [kbc]avb_slot_verify result is 0.
[   15.145450] c0 [kbc]l_modem_avb_slot_verify result is OK.
[   15.145453] c0 [kbc]slot_data[0] is 0xe071e030.
[   15.145455] c0 [kbc]slot_data[1] is 0x0.
[   15.145462] c0 [kbc]ret = 0
[   15.145699] c0 [kbc]call mem map:ns addr = 0x89aa8000 map len = 0xb00000 
[   15.145879] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[   15.145883] c0 pAddr:
[   15.145885] c0 0000 00 5a 5a 5a 08 73 e6 d7 b0 20 00 18 08 db e6 d7
[   15.145900] c0 [kbc]avb_userdata_set() packed offset = 0x0
[   15.145902] c0 [kbc]start verify... 
[   15.145904] c0 [kbc]avb_check_kbc_image() name = l_ldsp
[   15.145907] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_ldsp
[   15.145910] c0 ### enter avb_slot_verify. ###
[   15.145917] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   15.145944] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c00c0
[   15.145948] c0 enter implement read_rollback_index().
[   15.145950] c0 read_is_device_unlocked() rollback_index_slot = 0 
[   15.145953] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.145956] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[   15.145962] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   15.145966] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.145971] c0 Skip verify chain_partition[boot] while load verify l_ldsp partition... 
[   15.145974] c0 n = 0
[   15.145976] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.145979] c0 Skip verify chain_partition[dtbo] while load verify l_ldsp partition... 
[   15.145982] c0 n = 1
[   15.145984] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.145988] c0 Skip verify chain_partition[init_boot] while load verify l_ldsp partition... 
[   15.145991] c0 n = 2
[   15.145993] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.145996] c0 Skip verify chain_partition[vbmeta_odm] while load verify l_ldsp partition... 
[   15.145999] c0 n = 3
[   15.146001] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146004] c0 Skip verify chain_partition[vbmeta_product] while load verify l_ldsp partition... 
[   15.146008] c0 n = 4
[   15.146009] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146013] c0 Skip verify chain_partition[vbmeta_system] while load verify l_ldsp partition... 
[   15.146016] c0 n = 5
[   15.146018] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146022] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify l_ldsp partition... 
[   15.146025] c0 n = 6
[   15.146026] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146030] c0 Skip verify chain_partition[vbmeta_vendor] while load verify l_ldsp partition... 
[   15.146033] c0 n = 7
[   15.146035] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146039] c0 Skip verify chain_partition[vendor_boot] while load verify l_ldsp partition... 
[   15.146042] c0 n = 8
[   15.146043] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146047] c0 Skip verify chain_partition[l_modem] while load verify l_ldsp partition... 
[   15.146050] c0 n = 9
[   15.146052] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.146055] c0 chain partition founded: n = 10
[   15.146059] c0 [kbc]read partition name: l_ldsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   15.146064] c0 [kbc]read footer
[   15.146065] c0 dump footer
[   15.146067] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   15.146082] c0 0010 00 30 00 00 00 00 00 00 00 30 00 00 00 00 00 00
[   15.146097] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   15.146112] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.146127] c0 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   15.146131] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_ldsp, offset = 0x300000, num_bytes = 0x840
[   15.146136] c0 [kbc]read certificate: img_addr = 0xe662e000
[   15.146140] c0 [kbc]read certificate: offset = 0x300000
[   15.146144] c0 dump certificate
[   15.146146] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   15.146161] c0 [kbc]read_from_partition_kbc leave buf = 0xe076c1f8
[   15.155298] c0 partition: l_ldsp vbmeta_verify_ret is :0. 
[   15.155317] c0 enter implement read_rollback_index().
[   15.155319] c0 read_is_device_unlocked() rollback_index_slot = 12 
[   15.155322] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.155326] c0 Info: g_sprd_vboot_version.img_ver[12]= 0
[   15.155333] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   15.155338] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   15.155345] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   15.155348] c0 check dat cp
[   15.193123] c4 tam_load_request:1513: load look up com.android.trusty.identity
[   15.193135] c4 handle_conn_req:412: failed (-2) to send response
[   15.215816] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   15.215827] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   15.215833] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.215840] c0 Skip verify chain_partition[l_gdsp] while load verify l_ldsp partition... 
[   15.215844] c0 n = 11
[   15.215846] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.215849] c0 Skip verify chain_partition[pm_sys] while load verify l_ldsp partition... 
[   15.215853] c0 n = 12
[   15.215855] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.215859] c0 Skip verify chain_partition[l_agdsp] while load verify l_ldsp partition... 
[   15.215862] c0 n = 13
[   15.215863] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.215867] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.215871] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.215874] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.215877] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.215881] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.215886] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   15.215891] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   15.215902] c0 read_is_device_unlocked() ret = 0 
[   15.216530] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   15.216544] c0 partition: vbmeta  guid_buf_size = 37 
[   15.216547] c0 guid: 1.0 
[   15.216565] c0 in avb_slot_verify, l:1626. 
[   15.216656] c4 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   15.216686] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   15.216969] c4 in avb_slot_verify, l:1640. 
[   15.216974] c4 [kbc]avb_slot_verify result is 0.
[   15.216976] c4 [kbc]l_ldsp_avb_slot_verify result is OK.
[   15.216979] c4 [kbc]slot_data[0] is 0xe071e030.
[   15.216982] c4 [kbc]slot_data[1] is 0x0.
[   15.216990] c4 [kbc]ret = 0
[   15.217064] c4 [kbc]call mem map:ns addr = 0x89620000 map len = 0x440000 
[   15.217135] c4 [kbc]after map:pAddr = 0xffffffffe662e000
[   15.217138] c4 pAddr:
[   15.217140] c4 0000 2e 50 53 44 38 38 43 53 5f 53 30 30 00 4d 53 47
[   15.217155] c4 [kbc]avb_userdata_set() packed offset = 0x0
[   15.217157] c4 [kbc]start verify... 
[   15.217160] c4 [kbc]avb_check_kbc_image() name = l_gdsp
[   15.217163] c4 [kbc]avb_check_kbc_image() g_avbUserData.img_name = l_gdsp
[   15.217166] c4 ### enter avb_slot_verify. ###
[   15.217173] c4 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   15.217200] c4 [kbc]read_from_partition_kbc leave buf = 0xe07c00c0
[   15.217205] c4 enter implement read_rollback_index().
[   15.217206] c4 read_is_device_unlocked() rollback_index_slot = 0 
[   15.217209] c4 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.217213] c4 Info: g_sprd_vboot_version.img_ver[0]= 0
[   15.217219] c4 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   15.217223] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217228] c4 Skip verify chain_partition[boot] while load verify l_gdsp partition... 
[   15.217231] c4 n = 0
[   15.217233] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217237] c4 Skip verify chain_partition[dtbo] while load verify l_gdsp partition... 
[   15.217240] c4 n = 1
[   15.217242] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217245] c4 Skip verify chain_partition[init_boot] while load verify l_gdsp partition... 
[   15.217248] c4 n = 2
[   15.217250] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217253] c4 Skip verify chain_partition[vbmeta_odm] while load verify l_gdsp partition... 
[   15.217257] c4 n = 3
[   15.217258] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217262] c4 Skip verify chain_partition[vbmeta_product] while load verify l_gdsp partition... 
[   15.217265] c4 n = 4
[   15.217266] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217270] c4 Skip verify chain_partition[vbmeta_system] while load verify l_gdsp partition... 
[   15.217273] c4 n = 5
[   15.217275] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217279] c4 Skip verify chain_partition[vbmeta_system_ext] while load verify l_gdsp partition... 
[   15.217282] c4 n = 6
[   15.217283] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217287] c4 Skip verify chain_partition[vbmeta_vendor] while load verify l_gdsp partition... 
[   15.217290] c4 n = 7
[   15.217292] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217295] c4 Skip verify chain_partition[vendor_boot] while load verify l_gdsp partition... 
[   15.217299] c4 n = 8
[   15.217300] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217304] c4 Skip verify chain_partition[l_modem] while load verify l_gdsp partition... 
[   15.217307] c4 n = 9
[   15.217308] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217312] c4 Skip verify chain_partition[l_ldsp] while load verify l_gdsp partition... 
[   15.217315] c4 n = 10
[   15.217317] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.217321] c4 chain partition founded: n = 11
[   15.217326] c4 [kbc]read partition name: l_gdsp, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   15.217331] c4 [kbc]read footer
[   15.217333] c4 dump footer
[   15.217335] c4 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   15.217349] c4 0010 00 24 e0 00 00 00 00 00 00 24 e0 00 00 00 00 00
[   15.217364] c4 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   15.217378] c4 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.217394] c4 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   15.217398] c4 Loading vbmeta struct in footer from partition '[kbc]read partition name: l_gdsp, offset = 0x24e000, num_bytes = 0x840
[   15.217402] c4 [kbc]read certificate: img_addr = 0xe662e000
[   15.217405] c4 [kbc]read certificate: offset = 0x24e000
[   15.217409] c4 dump certificate
[   15.217411] c4 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   15.217425] c4 [kbc]read_from_partition_kbc leave buf = 0xe076c1f8
[   15.219854] c4 partition: l_gdsp vbmeta_verify_ret is :0. 
[   15.219864] c4 enter implement read_rollback_index().
[   15.219866] c4 read_is_device_unlocked() rollback_index_slot = 13 
[   15.219868] c4 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.219871] c4 Info: g_sprd_vboot_version.img_ver[13]= 0
[   15.219875] c4 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   15.219879] c4 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   15.219885] c4 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   15.219887] c4 check dat cp
[   15.228880] c4 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   15.228894] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   15.228899] c4 ta_manager_verify_img:447: RSA_hash
[   15.255031] c0 ta_manager_verify_img:506: RSA_verify
[   15.256555] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   15.256566] c0 trusty_app:(32) start 0xffffffffe0819000 size 0x0008b000
[   15.256577] c0 trusty_app: whitelist.table 0x0, size: 0
[   15.256580] c0 trusty_app 14 uuid: 0x3f3010ec 0xfc8 0xc8a2 0x9110 0xc5ef1de1233a
[   15.256589] c0 trusty_app 0xffffffffe071ede0: stack_sz=0x40000
[   15.256592] c0 trusty_app 0xffffffffe071ede0: heap_sz=0x40000
[   15.256596] c0 trusty_app 0xffffffffe071ede0: one_shot=0x0
[   15.256599] c0 trusty_app 0xffffffffe071ede0: keep_alive=0x0
[   15.256602] c0 trusty_app 0xffffffffe071ede0: flags=0x1c
[   15.256606] c0 ta_manager_write_ta:985: enter tam anti rollback
[   15.256613] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   15.256617] c0 ta_manager_write_ta:997: tam anti rollback ok
[   15.256620] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   15.256625] c0 trusty_tapp_init:
[   15.256765] c0 trusty_app 14: code: start 0x00008000 end 0x0008e3c4
[   15.256771] c0 trusty_app 14: data: start 0x0008f000 end 0x00090000
[   15.256775] c0 trusty_app 14: bss:                end 0x0008fa14
[   15.256779] c0 trusty_app 14: brk:  start 0x00090000 end 0x000d0000
[   15.256783] c0 trusty_app 14: entry 0x00011130
[   15.256825] c0 tam_port_publish:1501:  other port com.android.trusty.identity.secure
[   15.256842] c0 tam_port_publish:1496: publish port com.android.trusty.identity
[   15.256877] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.identity accomplished!
[   15.266889] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   15.266901] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   15.266906] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.266913] c0 Skip verify chain_partition[pm_sys] while load verify l_gdsp partition... 
[   15.266916] c0 n = 12
[   15.266918] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.266922] c0 Skip verify chain_partition[l_agdsp] while load verify l_gdsp partition... 
[   15.266925] c0 n = 13
[   15.266927] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.266931] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.267028] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.267032] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.267036] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.267039] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.267045] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   15.267050] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   15.267061] c0 read_is_device_unlocked() ret = 0 
[   15.267477] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   15.267480] c0 partition: vbmeta  guid_buf_size = 37 
[   15.267483] c0 guid: 1.0 
[   15.267490] c0 in avb_slot_verify, l:1626. 
[   15.267494] c0 in avb_slot_verify, l:1640. 
[   15.267497] c0 [kbc]avb_slot_verify result is 0.
[   15.267499] c0 [kbc]l_gdsp_avb_slot_verify result is OK.
[   15.267503] c0 [kbc]slot_data[0] is 0xe071e030.
[   15.267506] c0 [kbc]slot_data[1] is 0x0.
[   15.267511] c0 [kbc]ret = 0
[   15.267560] c0 [kbc]call mem map:ns addr = 0x88040000 map len = 0x200000 
[   15.267602] c0 [kbc]after map:pAddr = 0xffffffffe662e000
[   15.267605] c0 pAddr:
[   15.267607] c0 0000 df f8 94 d0 25 49 26 4a 26 4b 91 42 06 d0 9a 42
[   15.267622] c0 [kbc]avb_userdata_set() packed offset = 0x0
[   15.267625] c0 [kbc]start verify... 
[   15.267627] c0 [kbc]avb_check_kbc_image() name = pm_sys
[   15.267629] c0 [kbc]avb_check_kbc_image() g_avbUserData.img_name = pm_sys
[   15.267632] c0 ### enter avb_slot_verify. ###
[   15.267638] c0 Loading vbmeta struct from partition '[kbc]read partition name: vbmeta, offset = 0x0, num_bytes = 0x10000
[   15.267663] c0 [kbc]read_from_partition_kbc leave buf = 0xe07c00c0
[   15.267667] c0 enter implement read_rollback_index().
[   15.267669] c0 read_is_device_unlocked() rollback_index_slot = 0 
[   15.267672] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.267675] c0 Info: g_sprd_vboot_version.img_ver[0]= 0
[   15.267681] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:20.
[   15.267685] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267690] c0 Skip verify chain_partition[boot] while load verify pm_sys partition... 
[   15.267693] c0 n = 0
[   15.267695] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267698] c0 Skip verify chain_partition[dtbo] while load verify pm_sys partition... 
[   15.267701] c0 n = 1
[   15.267703] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267706] c0 Skip verify chain_partition[init_boot] while load verify pm_sys partition... 
[   15.267710] c0 n = 2
[   15.267711] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267715] c0 Skip verify chain_partition[vbmeta_odm] while load verify pm_sys partition... 
[   15.267718] c0 n = 3
[   15.267720] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267723] c0 Skip verify chain_partition[vbmeta_product] while load verify pm_sys partition... 
[   15.267727] c0 n = 4
[   15.267728] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267732] c0 Skip verify chain_partition[vbmeta_system] while load verify pm_sys partition... 
[   15.267736] c0 n = 5
[   15.267737] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267741] c0 Skip verify chain_partition[vbmeta_system_ext] while load verify pm_sys partition... 
[   15.267744] c0 n = 6
[   15.267746] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267749] c0 Skip verify chain_partition[vbmeta_vendor] while load verify pm_sys partition... 
[   15.267753] c0 n = 7
[   15.267754] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267758] c0 Skip verify chain_partition[vendor_boot] while load verify pm_sys partition... 
[   15.267761] c0 n = 8
[   15.267763] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267767] c0 Skip verify chain_partition[l_modem] while load verify pm_sys partition... 
[   15.267770] c0 n = 9
[   15.267772] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267775] c0 Skip verify chain_partition[l_ldsp] while load verify pm_sys partition... 
[   15.267778] c0 n = 10
[   15.267780] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267783] c0 Skip verify chain_partition[l_gdsp] while load verify pm_sys partition... 
[   15.267787] c0 n = 11
[   15.267789] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.267792] c0 chain partition founded: n = 12
[   15.267796] c0 [kbc]read partition name: pm_sys, offset = 0xffffffffffffffc0, num_bytes = 0x40
[   15.267800] c0 [kbc]read footer
[   15.267801] c0 dump footer
[   15.267804] c0 0000 41 56 42 66 00 00 00 01 00 00 00 00 00 00 00 00
[   15.267818] c0 0010 00 0b a0 00 00 00 00 00 00 0b a0 00 00 00 00 00
[   15.267833] c0 0020 00 00 08 40 00 00 00 00 00 00 00 00 00 00 00 00
[   15.267849] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.267865] c0 [kbc]read_from_partition_kbc leave buf = 0xe069c3e8
[   15.267869] c0 Loading vbmeta struct in footer from partition '[kbc]read partition name: pm_sys, offset = 0xba000, num_bytes = 0x840
[   15.267874] c0 [kbc]read certificate: img_addr = 0xe662e000
[   15.267878] c0 [kbc]read certificate: offset = 0xba000
[   15.267882] c0 dump certificate
[   15.267884] c0 0000 41 56 42 30 00 00 00 01 00 00 00 00 00 00 00 00
[   15.267899] c0 [kbc]read_from_partition_kbc leave buf = 0xe076c1f8
[   15.270678] c0 partition: pm_sys vbmeta_verify_ret is :0. 
[   15.270693] c0 enter implement read_rollback_index().
[   15.270695] c0 read_is_device_unlocked() rollback_index_slot = 14 
[   15.270699] c0 Info: vbmeta_header.rollback_index:0, >= stored_rollback_index:0.
[   15.270702] c0 Info: g_sprd_vboot_version.img_ver[14]= 0
[   15.270708] c0 in load_and_verify_vbmeta, l:1000. num_descriptors:1.
[   15.270712] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x2.
[   15.270720] c0 avb_ops_get_image_buffer: img_addr = 0xe662e000
[   15.270722] c0 check dat cp
[   15.279305] c0 in load_and_verify_vbmeta, l:1019.sub_ret:0.
[   15.279316] c0 in load_and_verify_vbmeta, l:1098. sub_ret:0. 
[   15.279320] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x4.
[   15.279326] c0 Skip verify chain_partition[l_agdsp] while load verify pm_sys partition... 
[   15.279330] c0 n = 13
[   15.279332] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.279336] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.279339] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.279343] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.279347] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.279350] c0 in load_and_verify_vbmeta, l:1009. desc.tag:0x0.
[   15.279355] c0 in avb_slot_verify, l:1527. load_and_verify_vbmeta ret is 0, algorithm_type: 2. 
[   15.279360] c0 in avb_slot_verify, l:1531, vbmeta, 2, 1
[   15.279372] c0 read_is_device_unlocked() ret = 0 
[   15.279786] c0 enter get_unique_guid_for_partition(). partition = vbmeta size = 37 
[   15.279790] c0 partition: vbmeta  guid_buf_size = 37 
[   15.279793] c0 guid: 1.0 
[   15.279799] c0 in avb_slot_verify, l:1626. 
[   15.279802] c0 in avb_slot_verify, l:1640. 
[   15.279805] c0 [kbc]avb_slot_verify result is 0.
[   15.279808] c0 [kbc]pm_sys_avb_slot_verify result is OK.
[   15.279811] c0 [kbc]slot_data[0] is 0xe071e030.
[   15.279813] c0 [kbc]slot_data[1] is 0x0.
[   15.279819] c0 [kbc]ret = 0
[   15.279848] c0 [kbc]verify success. 
[   15.279851] c0 version:
[   15.279853] c0 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279868] c0 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279883] c0 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279898] c0 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279913] c0 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279927] c0 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279942] c0 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279957] c0 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
[   15.279972] c0 0080 00 00 00 00 00 00 00 00
[   15.279982] c0 kbc_v2_dump_version 
[   15.279983] c0 imgver[0] = 0x0 
[   15.279986] c0 imgver[1] = 0x0 
[   15.279988] c0 imgver[2] = 0x0 
[   15.279991] c0 imgver[3] = 0x0 
[   15.279993] c0 imgver[4] = 0x0 
[   15.279995] c0 imgver[5] = 0x0 
[   15.279997] c0 imgver[6] = 0x0 
[   15.279999] c0 imgver[7] = 0x0 
[   15.280002] c0 imgver[8] = 0x0 
[   15.280004] c0 imgver[9] = 0x0 
[   15.280159] c0 imgver[10] = 0x0 
[   15.280164] c0 imgver[11] = 0x0 
[   15.280166] c0 imgver[12] = 0x0 
[   15.280168] c0 imgver[13] = 0x0 
[   15.280171] c0 imgver[14] = 0x0 
[   15.280173] c0 imgver[15] = 0x0 
[   15.280175] c0 imgver[16] = 0x0 
[   15.280178] c0 imgver[17] = 0x0 
[   15.280180] c0 imgver[18] = 0x0 
[   15.280182] c0 imgver[19] = 0x0 
[   15.280184] c0 imgver[20] = 0x0 
[   15.280187] c0 imgver[21] = 0x0 
[   15.280189] c0 imgver[22] = 0x0 
[   15.280191] c0 imgver[23] = 0x0 
[   15.280193] c0 imgver[24] = 0x0 
[   15.280195] c0 imgver[25] = 0x0 
[   15.280197] c0 imgver[26] = 0x0 
[   15.280200] c0 imgver[27] = 0x0 
[   15.280202] c0 imgver[28] = 0x0 
[   15.280204] c0 imgver[29] = 0x0 
[   15.280206] c0 imgver[30] = 0x0 
[   15.280208] c0 imgver[31] = 0x0 
<   15.280217> trusty_kernelbootcp: 185: TA:kbc_verify_all_avb2() ret = 0
[   15.280232] c0 enter SEC_KBC_GET_VERSION
[   15.280234] c0 reset update version flag
<   15.280237> version
<   15.280242> 0000 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280250> 0010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280258> 0020 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280265> 0030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280273> 0040 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280281> 0050 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280288> 0060 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280296> 0070 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
<   15.280304> 0080 00 00 00 00 00 00 00 00
<   15.280310> trusty_kernelbootcp: 193: TA:update version flag = 0
[   15.280317] c0 enter SEC_KBC_START_CP
[   15.280322] c0 dump_table() len = 1380000 maplen = 1380000 addr = 8b000000 
[   15.280326] c0 dump_table() len = b00000 maplen = b00000 addr = 89aa8000 
[   15.280330] c0 dump_table() len = 440000 maplen = 440000 addr = 89620000 
[   15.280333] c0 dump_table() len = 200000 maplen = 200000 addr = 88040000 
[   15.280337] c0 dump_table() flag = 31 
[   15.280339] c0 dump_table() is_packed = 0 
[   15.280341] c0 kbc_start_cp() enter MODEM_IMG
[   15.280343] c0 reg_addr = 0xffffffffe61cc330
[   15.280350] c0 before reg = 2010101
[   15.280356] c0 after  reg = 10101
[   15.280358] c0 sleep 50ms start 
[   15.289230] c2 tam_load_request:1513: load look up com.android.trusty.focalfp
[   15.289243] c2 handle_conn_req:412: failed (-2) to send response
[   15.330504] c0 sleep end 
[   15.330512] c0 reg_addr = 0xffffffffe61cc818
[   15.330520] c0 before reg = 0006
[   15.330526] c0 after  reg = 0004
[   15.330529] c0 sleep 50ms start 
[   15.332322] c4 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   15.332371] c4 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   15.365817] c5 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   15.365829] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   15.365833] c5 ta_manager_verify_img:447: RSA_hash
[   15.380989] c0 sleep end 
[   15.380998] c0 reg_addr = 0xffffffffe61ccb98
[   15.381004] c0 before reg = 4800
[   15.381010] c0 after  reg = 4000
[   15.381012] c0 sleep 50ms start 
[   15.384208] c0 ta_manager_verify_img:506: RSA_verify
[   15.386083] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   15.386097] c0 trusty_app:(32) start 0xffffffffe094a000 size 0x002ae000
[   15.386142] c0 trusty_app: whitelist.table 0x0, size: 0
[   15.386145] c0 trusty_app 15 uuid: 0x4c2cd891 0x219d 0x43d8 0xbe2d 0x39fa2080a26f
[   15.386154] c0 trusty_app 0xffffffffe071e4c0: stack_sz=0x100000
[   15.386157] c0 trusty_app 0xffffffffe071e4c0: heap_sz=0xe00000
[   15.386161] c0 trusty_app 0xffffffffe071e4c0: one_shot=0x0
[   15.386164] c0 trusty_app 0xffffffffe071e4c0: keep_alive=0x0
[   15.386167] c0 trusty_app 0xffffffffe071e4c0: flags=0x1c
[   15.386170] c0 ta_manager_write_ta:985: enter tam anti rollback
[   15.386177] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   15.386181] c0 ta_manager_write_ta:997: tam anti rollback ok
[   15.386184] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   15.386189] c0 trusty_tapp_init:
[   15.389662] c0 trusty_app 15: code: start 0x00008000 end 0x001f0908
[   15.389673] c0 trusty_app 15: data: start 0x001f1000 end 0x002b3000
[   15.389678] c0 trusty_app 15: bss:                end 0x002b2dcc
[   15.389681] c0 trusty_app 15: brk:  start 0x002b3000 end 0x010b3000
[   15.389685] c0 trusty_app 15: entry 0x001b4ef8
<   15.389715> *** Libc Version: 2.2, Built: 02:35:24 Jun  3 2022 ***
<   15.389731> I focaltech:ta:entry: TEE-Sprd TA is born.
[   15.389763] c0 tam_port_publish:1496: publish port com.android.trusty.focalfp
[   15.389779] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.focalfp accomplished!
<   15.390336> D focaltech:ta:base: v2.5.5.0-d60319b-250310@1631
<   15.390352> I focaltech:ta:base: TEE environment checking...
<   15.390363> I focaltech:ta:base: testing timing...0(0)ms.
<   15.440156> I focaltech:ta:base: testing msleep(50)...0ms.
<   15.440173> I focaltech:ta:base: TA is successfully created.
<   15.441125> I focaltech:ta:config: configurations is synced to TA.
<   15.441143> W focaltech:ta:config: no key named 'device.enroll_type', use default value.
<   15.441163> W focaltech:ta:config: no key named 'common.max_authentication_failed_attemps', use default value.
[   15.441877] c0 sleep end 
[   15.441884] c0 reg_addr = 0xffffffffe61dc174
[   15.441892] c0 before reg = 0400
[   15.441898] c0 after  reg = 0000
[   15.441900] c0 sleep 50ms start 
<   15.491414> I focaltech:ta:device: registering chips...
[   15.491937] c0 sleep end 
[   15.491944] c0 reg_addr = 0xffffffffe61dc08c
[   15.491951] c0 before reg = 0001
[   15.491957] c0 after  reg = 0000
[   15.491960] c0 sleep 50ms start SP_IMG
<   15.517777> E focaltech:ta:device: error at ff_device_probe[device.c:102]: failed to probe focaltech fingerprint device.
<   15.517798> E focaltech:ta:device: error at ff_device_probe[device.c:103]: 'Device not found'.
[   15.546692] c0 sleep end 
[   15.546700] c0 kbc_start_cp() leave 
<   15.546708> trusty_kernelbootcp: 199: TA:SEC_KBC_START_CP() ret = 0
<   15.546724> trusty_kernelbootcp: 202: TA:SEC_PREPARE_FIREWALL_DATA() ret = 0
<   15.546740> trusty_kernelbootcp: 107: kbc_send_response rc = 4 
<   15.727386> E focaltech:ta:device: error at ff_device_probe[device.c:102]: failed to probe focaltech fingerprint device.
<   15.727405> E focaltech:ta:device: error at ff_device_probe[device.c:103]: 'Device not found'.
[   15.728127] c1 tam_load_request:1513: load look up com.android.trusty.faceid
[   15.728139] c1 handle_conn_req:412: failed (-2) to send response
[   15.742497] c5 ta_manager_write_ta:857: ta_manager_write_ta: new ta!
[   15.742572] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   15.742599] c5 ta_manager_write_ta:948: ta_manager_write_ta, verify ta
[   15.742603] c5 ta_manager_aes_decrypt:362: ta_encrypted_compat:ta just signed!!!
[   15.742607] c5 ta_manager_verify_img:447: RSA_hash
[   15.745245] c0 ta_manager_verify_img:506: RSA_verify
[   15.747151] c0 ta_manager_write_ta:979: ta_manager_write_ta, load ta
[   15.747164] c0 trusty_app:(32) start 0xffffffffe1b48000 size 0x00015000
[   15.747174] c0 trusty_app: whitelist.table 0x0, size: 0
[   15.747179] c0 trusty_app 16 uuid: 0xf4bc36e6 0x8ec2 0x46e2 0xa82e 0xf7cb6cdc6f72
[   15.747187] c0 trusty_app 0xffffffffe0713448: stack_sz=0x2000
[   15.747190] c0 trusty_app 0xffffffffe0713448: heap_sz=0x6000
[   15.747194] c0 trusty_app 0xffffffffe0713448: one_shot=0x0
[   15.747198] c0 trusty_app 0xffffffffe0713448: keep_alive=0x0
[   15.747201] c0 trusty_app 0xffffffffe0713448: flags=0x1c
[   15.747204] c0 ta_manager_write_ta:985: enter tam anti rollback
[   15.747211] c0 tam_anti_rollback:259: ta anti_rollback not enable!!!
[   15.747216] c0 ta_manager_write_ta:997: tam anti rollback ok
[   15.747219] c0 ta_manager_write_ta:1009: current ta not define whilelist. not do loading authority check.
[   15.747224] c0 trusty_tapp_init:
[   15.747370] c0 trusty_app 16: code: start 0x00008000 end 0x000189cc
[   15.747376] c0 trusty_app 16: data: start 0x00019000 end 0x0001a000
[   15.747381] c0 trusty_app 16: bss:                end 0x0001941c
[   15.747384] c0 trusty_app 16: brk:  start 0x0001a000 end 0x00020000
[   15.747389] c0 trusty_app 16: entry 0x0000b430
[   15.747430] c0 tam_port_publish:1496: publish port com.android.trusty.faceid
[   15.747444] c0 ta_manager_write_ta:1052: ta_manager_write_ta, load com.android.trusty.faceid accomplished!
<   15.930391> E focaltech:ta:device: error at ff_device_probe[device.c:102]: failed to probe focaltech fingerprint device.
<   15.930409> E focaltech:ta:device: error at ff_device_probe[device.c:103]: 'Device not found'.
[   15.942645] c5 tam_disc_notify:1554: port =  com.android.trusty.focalfp
[   15.942673] c5 ipc_port_kill:1034: kill (com.android.trusty.focalfp) 's TA 
<   15.942738> E focaltech:ta:entry: error at focalfp_chan_handler[trustlet_entry.c:353]: closed by peer.
<   15.942758> E focaltech:ta:entry: error at main[trustlet_entry.c:532]: wait_any(..) = -1.
[   15.942887] c5 exit called, thread 0xffffffffe0722448, name trusty_tapp_2_4c2cd891-219d-43d
[   15.942896] c5 tam_thread_exit_notify:1422: tam_thread_exit_notify, id:15
[   15.942902] c5 tam_unload_request:1373:  com.android.trusty.focalfp, id: 15
[   15.942913] c5 ta_manager_unload_thread_fun:1351: app id 15 exit,tam going to unloader app ......
[   15.942919] c5 ta_manager_unload_thread_fun:1358: will free trusty_app path com.android.trusty.focalfp
<   16.750669> ss: block_device_tipc_prodnv_init: prodnv device's block size 4040 block count 1048576
<   16.755002> fs_init_from_super:603: super block: files at 4 free at 5
<   16.757608> fs_init_from_super:634: loaded super block version 1
<   16.757637> ss: block_device_tipc_prodnv_init: create port com.android.trusty.storage.client.tn success
<   30.661217> storage_client: 316: storage_open_file: invalid  rc = -2 
<   30.661238> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<   30.661450> ss-ipc: 185: do_disconnect ev->handle ox3f3
<   30.661463> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 
<   30.669788> storage_client: 316: storage_open_file: invalid  rc = -2 
<   30.669807> trusty_face (err): app/faceid/lite/faceid/trusty_faceid.cpp, Line 88: Faceid template file not exist!
<   30.670015> ss-ipc: 185: do_disconnect ev->handle ox3f3
<   30.670029> ss: disconnect whith f4bc36e68ec246e2a82ef7cb6cdc6f72 

trusty_keymasterapp/keymaster/secure_storage_manager.cpp, Line 127: Finish storage_open_session_async
storage_client: 374: storage_open_file: invalid  rc = 2 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 955: Error: file 'AttestationIds' not exists

trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 115: Before storage_end_transaction
trusty_keymaster (warn): app/keymaster/secure_storage_manager.cpp, Line 117: Finish storage_end_transaction
storage_client: 374: storage_open_file: invalid  rc = -2 
trusty_keymaster (err): app/keymaster/secure_storage_manager.cpp, Line 955: Error: file 'AttestationIds' not exists

