

(null)
1][T1124@C6] [Audio:PIPE] user_msg_out.parameter2=0x0
<6>[   64.007849][T1124@C6] [Audio:PIPE] user_msg_out.parameter3=0x0
<14>[   64.008236][ T1@C6] init: Sending signal 15 to service 'hidl_memory' (pid 601) process group...
<12>[   64.008946][ T1@C6] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_601 after 0 ms
<11>[   64.009036][ T1@C6] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_601: Device or resource busy
<14>[   64.010594][ T1@C3] init: Sending signal 15 to service 'zygote_secondary' (pid 556) process group...
<12>[   64.012631][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_556 after 0 ms
<6>[   64.012649][T1078@C4] [Audio:SMSG] aud_smsg_recv wait interrupted!
<3>[   64.012672][T1078@C4] [Audio:PIPE] aud_pipe_recv_cmd, Failed to recv,ret(-512)
<3>[   64.012691][T1078@C4] [Audio:PIPE] aud_pipe_read aud_pipe_recv_cmd failed
<11>[   64.012824][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_556: Device or resource busy
<6>[   64.012878][T1078@C4] [Audio:PIPE] user_msg_out.channel=0x0
<6>[   64.012898][T1078@C4] [Audio:PIPE] user_msg_out.command=0x0
<6>[   64.012915][T1078@C4] [Audio:PIPE] user_msg_out.parameter0=0x0
<6>[   64.012932][T1078@C4] [Audio:PIPE] user_msg_out.parameter1=0x0
<6>[   64.012948][T1078@C4] [Audio:PIPE] user_msg_out.parameter2=0x0
<6>[   64.012964][T1078@C4] [Audio:PIPE] user_msg_out.parameter3=0x0
<14>[   64.014389][ T1@C4] init: Sending signal 15 to service 'zygote' (pid 553) process group...
<4>[   64.032896][ T1@C4] Signal_Debug: Req_proc is init, pid=1, Tar_proc is system_server, pid=1229
<4>[   64.032953][ T1@C4] Signal_Debug: Tar_proc_group is system_server, tgid=1229, sig=15
<4>[   64.062029][ T1@C4] Signal_Debug: Req_proc is init, pid=1, Tar_proc is system_server, pid=1229
<4>[   64.062066][ T1@C4] Signal_Debug: Tar_proc_group is system_server, tgid=1229, sig=15
<12>[   64.062215][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_553 after 1 ms
<11>[   64.062404][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_553: Device or resource busy
<14>[   64.065449][ T1@C4] init: Sending signal 15 to service 'netd' (pid 549) process group...
<3>[   64.072140][T1084@C3] sprd-apipe apipe-cmd-in: wait interrupted!
<14>[   64.115843][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_549
<14>[   64.118077][ T1@C3] init: Sending signal 15 to service 'statsd' (pid 536) process group...
<12>[   64.119809][ T1@C3] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1066/pid_536 after 0 ms
<6>[   64.120005][T1244@C0] [ASoC:BOARD] hook_general_spk id: 1, gpio: 187, mode: 1, on: 0, audio_sense:0
<6>[   64.120031][T1244@C0] [ASoC:BOARD] hook_general_ctl hook state {0, 0, 0}
<11>[   64.120432][ T1@C3] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1066/pid_536: Device or resource busy
<14>[   64.122052][ T1@C5] init: Sending signal 15 to service 'ylog' (pid 525) process group...
<12>[   64.124742][ T1@C5] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_525 after 0 ms
<11>[   64.124930][ T1@C5] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_525: Device or resource busy
<14>[   64.126498][ T1@C4] init: Sending signal 15 to service 'vendor.sprd.boot-hal-1-2' (pid 380) process group...
<12>[   64.127841][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_0/pid_380 after 0 ms
<11>[   64.128025][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_0/pid_380: Device or resource busy
<14>[   64.130434][ T1@C4] init: Sending signal 15 to service 'vendor.keymint-unisoc-2.0' (pid 379) process group...
<14>[   64.152456][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_379
<14>[   64.154386][ T1@C4] init: Sending signal 15 to service 'keystore2' (pid 378) process group...
<12>[   64.155732][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1017/pid_378 after 0 ms
<11>[   64.155905][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1017/pid_378: Device or resource busy
<14>[   64.157521][ T1@C4] init: Sending signal 15 to service 'system_suspend' (pid 377) process group...
<12>[   64.158884][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1000/pid_377 after 0 ms
<11>[   64.159061][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1000/pid_377: Device or resource busy
<14>[   64.160857][ T1@C4] init: Sending signal 15 to service 'lmkd' (pid 301) process group...
<12>[   64.162607][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1069/pid_301 after 0 ms
<11>[   64.162788][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1069/pid_301: Device or resource busy
<14>[   64.166153][ T1@C4] init: Sending signal 15 to service 'prng_seeder' (pid 282) process group...
<12>[   64.167486][ T1@C4] libprocessgroup: Still waiting on process(es) to exit for cgroup /sys/fs/cgroup/uid_1092/pid_282 after 0 ms
<11>[   64.167671][ T1@C4] libprocessgroup: Unable to remove cgroup /sys/fs/cgroup/uid_1092/pid_282: Device or resource busy
<14>[   64.169617][ T1@C4] init: Service 'vendor.keymint-unisoc-2.0' (pid 379) received signal 15
<14>[   64.169687][ T1@C4] init: Sending signal 9 to service 'vendor.keymint-unisoc-2.0' (pid 379) process group...
<11>[   64.169969][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_379/cgroup.kill: No such file or directory
<11>[   64.170133][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_379/cgroup.procs: No such file or directory
<6>[   64.210266][T837@C1] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[   64.215547][T534@C1] unisoc_userlog: userlog_release
<6>[   64.219596][T131@C7] binder: undelivered TRANSACTION_COMPLETE
<6>[   64.219618][T131@C7] binder: undelivered transaction 86905, process died.
<14>[   64.234621][ T1@C7] init: Service 'system_suspend' (pid 377) received signal 15
<14>[   64.234660][ T1@C7] init: Sending signal 9 to service 'system_suspend' (pid 377) process group...
<14>[   64.235320][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_377
<14>[   64.236853][ T1@C7] init: Service 'vendor.sprd.boot-hal-1-2' (pid 380) received signal 15
<14>[   64.236888][ T1@C7] init: Sending signal 9 to service 'vendor.sprd.boot-hal-1-2' (pid 380) process group...
<14>[   64.237713][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_380
<14>[   64.239216][ T1@C7] init: Service 'prng_seeder' (pid 282) received signal 15
<14>[   64.239249][ T1@C7] init: Sending signal 9 to service 'prng_seeder' (pid 282) process group...
<14>[   64.239846][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1092/pid_282
<14>[   64.241462][ T1@C7] init: Service 'netd' (pid 549) received signal 15
<14>[   64.241496][ T1@C7] init: Sending signal 9 to service 'netd' (pid 549) process group...
<11>[   64.241647][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_549/cgroup.kill: No such file or directory
<11>[   64.241718][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_549/cgroup.procs: No such file or directory
<14>[   64.243754][ T1@C7] init: Service 'hidl_memory' (pid 601) received signal 15
<14>[   64.243792][ T1@C7] init: Sending signal 9 to service 'hidl_memory' (pid 601) process group...
<14>[   64.244413][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_601
<14>[   64.247058][ T1@C4] init: Service 'vendor.camera-provider-2-4' (pid 608) received signal 15
<14>[   64.247125][ T1@C4] init: Sending signal 9 to service 'vendor.camera-provider-2-4' (pid 608) process group...
<14>[   64.248398][ T1@C4] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1047/pid_608
<14>[   64.250570][ T1@C2] init: Service 'vendor.bluetooth-1-1' (pid 607) received signal 15
<14>[   64.250631][ T1@C2] init: Sending signal 9 to service 'vendor.bluetooth-1-1' (pid 607) process group...
<14>[   64.251878][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1002/pid_607
<14>[   64.257170][ T1@C2] init: Service 'keystore2' (pid 378) received signal 15
<14>[   64.257247][ T1@C2] init: Sending signal 9 to service 'keystore2' (pid 378) process group...
<14>[   64.258759][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1017/pid_378
<14>[   64.262919][ T1@C2] init: Service 'vendor.drm-clearkey-service' (pid 609) received signal 15
<14>[   64.262986][ T1@C2] init: Sending signal 9 to service 'vendor.drm-clearkey-service' (pid 609) process group...
<14>[   64.264277][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_609
<14>[   64.268424][ T1@C7] init: Service 'vendor.drm-widevine-hal' (pid 610) received signal 15
<14>[   64.268462][ T1@C7] init: Sending signal 9 to service 'vendor.drm-widevine-hal' (pid 610) process group...
<14>[   64.269123][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1013/pid_610
<14>[   64.271099][ T1@C7] init: Service 'vendor.gatekeeper-1-0' (pid 618) received signal 15
<14>[   64.271135][ T1@C7] init: Sending signal 9 to service 'vendor.gatekeeper-1-0' (pid 618) process group...
<14>[   64.271799][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_618
<14>[   64.273646][ T1@C7] init: Service 'vendor.gralloc-4-0' (pid 620) received signal 15
<14>[   64.273681][ T1@C7] init: Sending signal 9 to service 'vendor.gralloc-4-0' (pid 620) process group...
<14>[   64.274327][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_620
<14>[   64.276196][ T1@C7] init: Service 'vendor.hwcomposer-2-4' (pid 623) received signal 15
<14>[   64.276232][ T1@C7] init: Sending signal 9 to service 'vendor.hwcomposer-2-4' (pid 623) process group...
<14>[   64.276861][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_623
<14>[   64.280959][ T1@C2] init: Service 'vendor.health-default' (pid 629) received signal 15
<14>[   64.281034][ T1@C2] init: Sending signal 9 to service 'vendor.health-default' (pid 629) process group...
<14>[   64.282590][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_629
<14>[   64.286991][ T1@C2] init: Service 'media.unisoc.codec2' (pid 631) received signal 15
<14>[   64.287054][ T1@C2] init: Sending signal 9 to service 'media.unisoc.codec2' (pid 631) process group...
<14>[   64.288367][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1046/pid_631
<14>[   64.293293][ T1@C2] init: Service 'vendor.sensors-hal-multihal' (pid 636) received signal 15
<14>[   64.293449][ T1@C2] init: Sending signal 9 to service 'vendor.sensors-hal-multihal' (pid 636) process group...
<6>[   64.293730][T1244@C6] [ASoC:SC2730] dalr_dc_os_event Dpop sucessed! i=1, ANA_STS1=0x7a80
<14>[   64.294647][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_636
<14>[   64.297476][ T1@C7] init: Service 'vendor.usb_default' (pid 638) received signal 15
<14>[   64.297509][ T1@C7] init: Sending signal 9 to service 'vendor.usb_default' (pid 638) process group...
<14>[   64.298112][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_638
<14>[   64.300827][ T1@C7] init: Service 'vendor.wifi_hal_legacy' (pid 640) received signal 15
<14>[   64.300860][ T1@C7] init: Sending signal 9 to service 'vendor.wifi_hal_legacy' (pid 640) process group...
<14>[   64.301543][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1010/pid_640
<14>[   64.306881][ T1@C5] init: Service 'vendor.aprd-default' (pid 642) received signal 15
<14>[   64.306951][ T1@C5] init: Sending signal 9 to service 'vendor.aprd-default' (pid 642) process group...
<14>[   64.308209][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_642
<14>[   64.311247][ T1@C5] init: Service 'vendor.cplog_svc-default' (pid 645) received signal 15
<14>[   64.311309][ T1@C5] init: Sending signal 9 to service 'vendor.cplog_svc-default' (pid 645) process group...
<14>[   64.312516][ T1@C5] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_645
<14>[   64.314866][ T1@C7] init: Service 'vendor.enhance-default' (pid 646) received signal 15
<14>[   64.314901][ T1@C7] init: Sending signal 9 to service 'vendor.enhance-default' (pid 646) process group...
<14>[   64.315506][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_646
<14>[   64.316804][ T1@C7] init: Service 'vendor.sprd.hardware.memtrack-service' (pid 660) received signal 15
<14>[   64.316839][ T1@C7] init: Sending signal 9 to service 'vendor.sprd.hardware.memtrack-service' (pid 660) process group...
<14>[   64.317517][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_9999/pid_660
<14>[   64.318829][ T1@C7] init: Service 'vendor.network-default' (pid 661) received signal 15
<14>[   64.318859][ T1@C7] init: Sending signal 9 to service 'vendor.network-default' (pid 661) process group...
<14>[   64.319530][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_661
<14>[   64.320956][ T1@C7] init: Service 'vendor.power.stats-default' (pid 668) received signal 15
<14>[   64.320989][ T1@C7] init: Sending signal 9 to service 'vendor.power.stats-default' (pid 668) process group...
<14>[   64.321980][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_668
<14>[   64.323388][ T1@C7] init: Service 'vendor.rebootescrow-default' (pid 673) received signal 15
<14>[   64.323424][ T1@C7] init: Sending signal 9 to service 'vendor.rebootescrow-default' (pid 673) process group...
<14>[   64.324026][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_673
<14>[   64.325324][ T1@C7] init: Service 'vendor.sprd.hardware.trusty-service' (pid 674) received signal 15
<14>[   64.325417][ T1@C7] init: Sending signal 9 to service 'vendor.sprd.hardware.trusty-service' (pid 674) process group...
<14>[   64.326120][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_674
<14>[   64.329721][ T1@C7] init: Service 'vendor.oemlock-default' (pid 675) received signal 15
<14>[   64.329754][ T1@C7] init: Sending signal 9 to service 'vendor.oemlock-default' (pid 675) process group...
<14>[   64.330341][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_675
<14>[   64.331785][ T1@C7] init: Service 'vendor.power-default' (pid 676) received signal 15
<14>[   64.331819][ T1@C7] init: Sending signal 9 to service 'vendor.power-default' (pid 676) process group...
<14>[   64.332468][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_676
<6>[   64.333700][T1244@C2] [ASoC:SC2730] DAC Off
<6>[   64.333731][T1244@C2] [ASoC:SC2730] sprd_codec_sample_rate_setting AD 0 DA 48000 AD1 0
<6>[   64.333771][T1244@C2] [ASoC:SC2730] DAC Off
<6>[   64.333787][T1244@C2] [ASoC:SC2730] sprd_codec_sample_rate_setting AD 0 DA 48000 AD1 0
<14>[   64.334170][ T1@C7] init: Service 'vendor.focaltech.fps_hal' (pid 681) received signal 15
<14>[   64.334196][ T1@C7] init: Sending signal 9 to service 'vendor.focaltech.fps_hal' (pid 681) process group...
<14>[   64.334896][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_681
<6>[   64.336399][T1244@C3] [ASoC:SC2730] sprd_codec_power_disable, line: 1420
<14>[   64.337541][ T1@C7] init: Service 'vendor.sprd.hardware.gnss-service' (pid 683) received signal 15
<14>[   64.337576][ T1@C7] init: Sending signal 9 to service 'vendor.sprd.hardware.gnss-service' (pid 683) process group...
<14>[   64.338210][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_683
<14>[   64.340561][ T1@C7] init: Service 'vendor.sprd.broadcastradio-hal2' (pid 687) received signal 15
<14>[   64.340595][ T1@C7] init: Sending signal 9 to service 'vendor.sprd.broadcastradio-hal2' (pid 687) process group...
<14>[   64.341200][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_687
<14>[   64.344245][ T1@C7] init: Service 'vendor.sprd.hardware.connmgr@1.0-service' (pid 690) received signal 15
<14>[   64.344300][ T1@C7] init: Sending signal 9 to service 'vendor.sprd.hardware.connmgr@1.0-service' (pid 690) process group...
<14>[   64.344927][ T1@C7] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_690
<14>[   64.347284][ T1@C3] init: Service 'miscdata_hal_service' (pid 691) received signal 15
<14>[   64.347358][ T1@C3] init: Sending signal 9 to service 'miscdata_hal_service' (pid 691) process group...
<11>[   64.347651][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_691/cgroup.kill: No such file or directory
<11>[   64.347805][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_691/cgroup.procs: No such file or directory
<14>[   64.351391][ T1@C1] init: Service 'zygote' (pid 553) received signal 15
<14>[   64.351455][ T1@C1] init: Sending signal 9 to service 'zygote' (pid 553) process group...
<4>[   64.351882][ T1@C1] Signal_Debug: Req_proc is init, pid=1, Tar_proc is system_server, pid=1229
<4>[   64.351904][ T1@C1] Signal_Debug: Tar_proc_group is system_server, tgid=1229, sig=9
<14>[   64.353089][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_553
<14>[   64.359094][T306@C3] logd: logdr: UID=1000 GID=1000 PID=3561 b tail=0 logMask=1f pid=0 start=0ns deadline=0ns
<14>[   64.418778][ T1@C7] init: Service 'ylog' (pid 525) received signal 9
<14>[   64.418821][ T1@C7] init: Sending signal 9 to service 'ylog' (pid 525) process group...
<14>[   64.425046][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_525
<12>[   64.425590][T3562@C2] logd: SocketClient: write error (Broken pipe)
<14>[   64.430857][ T1@C3] init: Service 'statsd' (pid 536) received signal 15
<14>[   64.430920][ T1@C3] init: Sending signal 9 to service 'statsd' (pid 536) process group...
<11>[   64.431199][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1066/pid_536/cgroup.kill: No such file or directory
<11>[   64.431335][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1066/pid_536/cgroup.procs: No such file or directory
<14>[   64.437098][ T1@C3] init: Service 'vendor.thermal-hal-2-0' (pid 693) received signal 15
<14>[   64.437168][ T1@C3] init: Sending signal 9 to service 'vendor.thermal-hal-2-0' (pid 693) process group...
<11>[   64.437599][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_693/cgroup.kill: No such file or directory
<11>[   64.437766][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_693/cgroup.procs: No such file or directory
<6>[   64.442216][T2022@C1] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[   64.443269][ T1@C3] init: Service 'vendor.tool-default' (pid 694) received signal 15
<14>[   64.443332][ T1@C3] init: Sending signal 9 to service 'vendor.tool-default' (pid 694) process group...
<11>[   64.443670][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_694/cgroup.kill: No such file or directory
<11>[   64.443824][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_694/cgroup.procs: No such file or directory
<14>[   64.448131][ T1@C3] init: Service 'vendor.sprd.hardware.vibrator-service' (pid 700) received signal 15
<14>[   64.448196][ T1@C3] init: Sending signal 9 to service 'vendor.sprd.hardware.vibrator-service' (pid 700) process group...
<11>[   64.448512][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_700/cgroup.kill: No such file or directory
<11>[   64.448663][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_700/cgroup.procs: No such file or directory
<14>[   64.454180][ T1@C3] init: Service 'audioserver' (pid 703) received signal 15
<14>[   64.454243][ T1@C3] init: Sending signal 9 to service 'audioserver' (pid 703) process group...
<11>[   64.454539][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1041/pid_703/cgroup.kill: No such file or directory
<11>[   64.454688][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1041/pid_703/cgroup.procs: No such file or directory
<14>[   64.459480][ T1@C3] init: Service 'credstore' (pid 705) received signal 15
<14>[   64.459553][ T1@C3] init: Sending signal 9 to service 'credstore' (pid 705) process group...
<11>[   64.459851][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1076/pid_705/cgroup.kill: No such file or directory
<11>[   64.459989][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1076/pid_705/cgroup.procs: No such file or directory
<14>[   64.463272][ T1@C7] init: Service 'gpu' (pid 709) received signal 15
<14>[   64.463308][ T1@C7] init: Sending signal 9 to service 'gpu' (pid 709) process group...
<11>[   64.463471][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1072/pid_709/cgroup.kill: No such file or directory
<11>[   64.463540][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1072/pid_709/cgroup.procs: No such file or directory
<14>[   64.465154][ T1@C7] init: Service 'audio_parameter_parser_service' (pid 722) received signal 15
<14>[   64.465190][ T1@C7] init: Sending signal 9 to service 'audio_parameter_parser_service' (pid 722) process group...
<11>[   64.465412][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1041/pid_722/cgroup.kill: No such file or directory
<11>[   64.465489][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1041/pid_722/cgroup.procs: No such file or directory
<14>[   64.467600][ T1@C7] init: Service 'audio_tunning_service' (pid 724) received signal 15
<14>[   64.467632][ T1@C7] init: Sending signal 9 to service 'audio_tunning_service' (pid 724) process group...
<11>[   64.467795][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1041/pid_724/cgroup.kill: No such file or directory
<11>[   64.467864][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1041/pid_724/cgroup.procs: No such file or directory
<14>[   64.469714][ T1@C7] init: Service 'vendor.engpcclientlte' (pid 728) received signal 15 oneshot service took 51.301998 seconds in background
<14>[   64.469748][ T1@C7] init: Sending signal 9 to service 'vendor.engpcclientlte' (pid 728) process group...
<11>[   64.469903][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_728/cgroup.kill: No such file or directory
<11>[   64.469980][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_728/cgroup.procs: No such file or directory
<14>[   64.471538][ T1@C7] init: Service 'gpsd' (pid 736) exited with status 0
<14>[   64.471572][ T1@C7] init: Sending signal 9 to service 'gpsd' (pid 736) process group...
<11>[   64.471729][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_736/cgroup.kill: No such file or directory
<11>[   64.471799][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_736/cgroup.procs: No such file or directory
<14>[   64.474654][ T1@C7] init: Service 'vendor.modem_control' (pid 739) received signal 15
<14>[   64.474690][ T1@C7] init: Sending signal 9 to service 'vendor.modem_control' (pid 739) process group...
<11>[   64.474857][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_739/cgroup.kill: No such file or directory
<11>[   64.474927][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_739/cgroup.procs: No such file or directory
<14>[   64.476885][ T1@C7] init: Service 'vendor.rpmbproxy' (pid 743) received signal 15 oneshot service took 51.217999 seconds in background
<14>[   64.476919][ T1@C7] init: Sending signal 9 to service 'vendor.rpmbproxy' (pid 743) process group...
<11>[   64.477095][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_743/cgroup.kill: No such file or directory
<11>[   64.477171][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_743/cgroup.procs: No such file or directory
<14>[   64.479044][ T1@C7] init: Service 'vendor.nsproxy' (pid 744) received signal 15
<14>[   64.479076][ T1@C7] init: Sending signal 9 to service 'vendor.nsproxy' (pid 744) process group...
<11>[   64.479224][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_744/cgroup.kill: No such file or directory
<11>[   64.479300][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_744/cgroup.procs: No such file or directory
<14>[   64.480796][ T1@C7] init: Service 'vendor.tee_rpc' (pid 745) received signal 15
<14>[   64.480830][ T1@C7] init: Sending signal 9 to service 'vendor.tee_rpc' (pid 745) process group...
<11>[   64.480997][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_745/cgroup.kill: No such file or directory
<11>[   64.481088][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_745/cgroup.procs: No such file or directory
<14>[   64.483315][ T1@C1] init: Service 'vendor.tsupplicant' (pid 749) received signal 15
<14>[   64.483375][ T1@C1] init: Sending signal 9 to service 'vendor.tsupplicant' (pid 749) process group...
<11>[   64.483653][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_749/cgroup.kill: No such file or directory
<11>[   64.483805][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_749/cgroup.procs: No such file or directory
<14>[   64.486357][ T1@C3] init: Service 'vendor.ril-daemon' (pid 752) received signal 15
<14>[   64.486418][ T1@C3] init: Sending signal 9 to service 'vendor.ril-daemon' (pid 752) process group...
<11>[   64.486775][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1001/pid_752/cgroup.kill: No such file or directory
<11>[   64.486927][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1001/pid_752/cgroup.procs: No such file or directory
<11>[   64.489048][T219@C1] init: Unable to set property 'ctl.interface_start' from uid:1000 gid:1000 pid:303: Received control message after shutdown, ignoring
<14>[   64.506017][ T1@C5] init: Service 'drm' (pid 803) received signal 15
<14>[   64.506079][ T1@C5] init: Sending signal 9 to service 'drm' (pid 803) process group...
<11>[   64.506416][ T1@C5] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1019/pid_803/cgroup.kill: No such file or directory
<11>[   64.506551][ T1@C5] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1019/pid_803/cgroup.procs: No such file or directory
<14>[   64.512478][ T1@C1] init: Service 'traced_probes' (pid 804) received signal 15
<14>[   64.512549][ T1@C1] init: Sending signal 9 to service 'traced_probes' (pid 804) process group...
<11>[   64.512906][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_9999/pid_804/cgroup.kill: No such file or directory
<11>[   64.513050][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_9999/pid_804/cgroup.procs: No such file or directory
<6>[   64.514176][T1244@C3] [Audio:DSPDUMP] audio_dsp_release
<6>[   64.514339][T1244@C3] [Audio:DSPDUMP] audio_dsp_release
<6>[   64.514369][T1244@C3] [Audio:DSPDUMP] audio_dsp_release
<6>[   64.514410][T1244@C3] [Audio:DSPDUMP] audio_dsp_release
<14>[   64.516912][ T1@C7] init: Service 'traced' (pid 805) received signal 15
<14>[   64.516949][ T1@C7] init: Sending signal 9 to service 'traced' (pid 805) process group...
<11>[   64.517080][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_9999/pid_805/cgroup.kill: No such file or directory
<11>[   64.517143][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_9999/pid_805/cgroup.procs: No such file or directory
<14>[   64.519966][ T1@C2] init: Service 'vendor.face-default' (pid 820) received signal 15
<14>[   64.520030][ T1@C2] init: Sending signal 9 to service 'vendor.face-default' (pid 820) process group...
<11>[   64.520316][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_820/cgroup.kill: No such file or directory
<11>[   64.520468][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_820/cgroup.procs: No such file or directory
<14>[   64.523376][ T1@C7] init: Service 'vendor.fingerprint-default' (pid 823) received signal 15
<14>[   64.523411][ T1@C7] init: Sending signal 9 to service 'vendor.fingerprint-default' (pid 823) process group...
<11>[   64.523607][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_823/cgroup.kill: No such file or directory
<11>[   64.523677][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_823/cgroup.procs: No such file or directory
<14>[   64.525392][ T1@C7] init: Service 'vendor.identity-default' (pid 824) received signal 15
<14>[   64.525432][ T1@C7] init: Sending signal 9 to service 'vendor.identity-default' (pid 824) process group...
<11>[   64.525582][ T1@C7] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_824/cgroup.kill: No such file or directory
<11>[   64.525652][ T1@C7] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_824/cgroup.procs: No such file or directory
<14>[   64.528500][ T1@C3] init: Service 'vendor.audio-hal' (pid 603) received signal 15
<14>[   64.528556][ T1@C3] init: Sending signal 9 to service 'vendor.audio-hal' (pid 603) process group...
<14>[   64.529951][ T1@C3] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1041/pid_603
<14>[   64.533199][ T1@C3] init: Service 'nfc_hal_service.tms.aidl' (pid 828) received signal 15
<14>[   64.533259][ T1@C3] init: Sending signal 9 to service 'nfc_hal_service.tms.aidl' (pid 828) process group...
<11>[   64.533621][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1027/pid_828/cgroup.kill: No such file or directory
<11>[   64.533779][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1027/pid_828/cgroup.procs: No such file or directory
<14>[   64.537011][ T1@C3] init: Service 'vendor.teensproxy' (pid 833) received signal 15
<14>[   64.537070][ T1@C3] init: Sending signal 9 to service 'vendor.teensproxy' (pid 833) process group...
<11>[   64.537472][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_833/cgroup.kill: No such file or directory
<11>[   64.537658][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_833/cgroup.procs: No such file or directory
<14>[   64.539943][ T1@C1] init: Service 'aprd' (pid 842) received signal 15
<14>[   64.540007][ T1@C1] init: Sending signal 9 to service 'aprd' (pid 842) process group...
<11>[   64.540296][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_842/cgroup.kill: No such file or directory
<11>[   64.540447][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_842/cgroup.procs: No such file or directory
<14>[   64.543025][ T1@C3] init: Service 'uniview' (pid 843) received signal 15
<14>[   64.543084][ T1@C3] init: Sending signal 9 to service 'uniview' (pid 843) process group...
<11>[   64.543362][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_843/cgroup.kill: No such file or directory
<11>[   64.543512][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_843/cgroup.procs: No such file or directory
<14>[   64.547055][ T1@C3] init: Service 'poweronlog' (pid 854) received signal 15 oneshot service took 49.157001 seconds in background
<14>[   64.547123][ T1@C3] init: Sending signal 9 to service 'poweronlog' (pid 854) process group...
<11>[   64.547463][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_854/cgroup.kill: No such file or directory
<11>[   64.547618][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_854/cgroup.procs: No such file or directory
<14>[   64.549882][ T1@C1] init: Service 'cameraserver' (pid 859) received signal 15
<14>[   64.549949][ T1@C1] init: Sending signal 9 to service 'cameraserver' (pid 859) process group...
<11>[   64.550222][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1047/pid_859/cgroup.kill: No such file or directory
<11>[   64.550357][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1047/pid_859/cgroup.procs: No such file or directory
<14>[   64.559038][ T1@C4] init: Service 'incidentd' (pid 873) received signal 15
<14>[   64.559106][ T1@C4] init: Sending signal 9 to service 'incidentd' (pid 873) process group...
<11>[   64.559394][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1067/pid_873/cgroup.kill: No such file or directory
<11>[   64.559542][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1067/pid_873/cgroup.procs: No such file or directory
<14>[   64.562050][ T1@C4] init: Service 'installd' (pid 875) received signal 15
<14>[   64.562128][ T1@C4] init: Sending signal 9 to service 'installd' (pid 875) process group...
<11>[   64.562420][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_875/cgroup.kill: No such file or directory
<11>[   64.562572][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_875/cgroup.procs: No such file or directory
<14>[   64.565121][ T1@C4] init: Service 'mediaextractor' (pid 876) received signal 15
<14>[   64.565189][ T1@C4] init: Sending signal 9 to service 'mediaextractor' (pid 876) process group...
<11>[   64.565623][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1040/pid_876/cgroup.kill: No such file or directory
<11>[   64.565773][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1040/pid_876/cgroup.procs: No such file or directory
<14>[   64.569631][ T1@C4] init: Service 'mediametrics' (pid 882) received signal 15
<14>[   64.569692][ T1@C4] init: Sending signal 9 to service 'mediametrics' (pid 882) process group...
<11>[   64.569966][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1013/pid_882/cgroup.kill: No such file or directory
<11>[   64.570104][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1013/pid_882/cgroup.procs: No such file or directory
<14>[   64.572387][ T1@C4] init: Service 'media' (pid 886) received signal 15
<14>[   64.572448][ T1@C4] init: Sending signal 9 to service 'media' (pid 886) process group...
<11>[   64.572668][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1013/pid_886/cgroup.kill: No such file or directory
<11>[   64.572799][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1013/pid_886/cgroup.procs: No such file or directory
<14>[   64.576095][ T1@C1] init: Service 'storaged' (pid 887) received signal 15
<14>[   64.576159][ T1@C1] init: Sending signal 9 to service 'storaged' (pid 887) process group...
<11>[   64.576451][ T1@C1] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_887/cgroup.kill: No such file or directory
<11>[   64.576660][ T1@C1] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_887/cgroup.procs: No such file or directory
<14>[   64.579170][ T1@C4] init: Service 'wificond' (pid 896) exited with status 0
<14>[   64.579234][ T1@C4] init: Sending signal 9 to service 'wificond' (pid 896) process group...
<11>[   64.579502][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1010/pid_896/cgroup.kill: No such file or directory
<11>[   64.579639][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1010/pid_896/cgroup.procs: No such file or directory
<14>[   64.582427][ T1@C4] init: Service 'ims_bridged' (pid 900) received signal 15
<14>[   64.582488][ T1@C4] init: Sending signal 9 to service 'ims_bridged' (pid 900) process group...
<11>[   64.582792][ T1@C4] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_900/cgroup.kill: No such file or directory
<11>[   64.582948][ T1@C4] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_900/cgroup.procs: No such file or directory
<14>[   64.585410][ T1@C0] init: Service 'linkturbonative' (pid 904) received signal 15
<14>[   64.585484][ T1@C0] init: Sending signal 9 to service 'linkturbonative' (pid 904) process group...
<11>[   64.585778][ T1@C0] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_904/cgroup.kill: No such file or directory
<11>[   64.585926][ T1@C0] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_904/cgroup.procs: No such file or directory
<14>[   64.589342][ T1@C0] init: Service 'remotedisplay' (pid 906) received signal 15
<14>[   64.589459][ T1@C0] init: Sending signal 9 to service 'remotedisplay' (pid 906) process group...
<11>[   64.589692][ T1@C0] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1013/pid_906/cgroup.kill: No such file or directory
<11>[   64.589824][ T1@C0] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1013/pid_906/cgroup.procs: No such file or directory
<14>[   64.596751][ T1@C2] init: Service 'slogmodem' (pid 907) received signal 15
<14>[   64.596816][ T1@C2] init: Sending signal 9 to service 'slogmodem' (pid 907) process group...
<11>[   64.597106][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_907/cgroup.kill: No such file or directory
<11>[   64.597256][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_907/cgroup.procs: No such file or directory
<14>[   64.603815][ T1@C2] init: Service 'uniresctlopt' (pid 910) received signal 15 oneshot service took 48.771000 seconds in background
<14>[   64.603915][ T1@C2] init: Sending signal 9 to service 'uniresctlopt' (pid 910) process group...
<11>[   64.604398][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_910/cgroup.kill: No such file or directory
<11>[   64.604559][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_910/cgroup.procs: No such file or directory
<14>[   64.608811][ T1@C2] init: Service 'unisocaudioutils' (pid 912) received signal 15
<14>[   64.608877][ T1@C2] init: Sending signal 9 to service 'unisocaudioutils' (pid 912) process group...
<11>[   64.609244][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_912/cgroup.kill: No such file or directory
<11>[   64.609619][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_912/cgroup.procs: No such file or directory
<14>[   64.615372][ T1@C2] init: Service 'vendor.media.omx' (pid 914) received signal 15
<14>[   64.615438][ T1@C2] init: Sending signal 9 to service 'vendor.media.omx' (pid 914) process group...
<11>[   64.615798][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1046/pid_914/cgroup.kill: No such file or directory
<11>[   64.615953][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1046/pid_914/cgroup.procs: No such file or directory
<14>[   64.622158][ T1@C2] init: Service 'vendor.charged' (pid 929) received signal 15
<14>[   64.622220][ T1@C2] init: Sending signal 9 to service 'vendor.charged' (pid 929) process group...
<11>[   64.622555][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_929/cgroup.kill: No such file or directory
<11>[   64.622707][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_929/cgroup.procs: No such file or directory
<14>[   64.625914][ T1@C2] init: Service 'phasecheckserver' (pid 939) received signal 15
<14>[   64.625972][ T1@C2] init: Sending signal 9 to service 'phasecheckserver' (pid 939) process group...
<11>[   64.626299][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_939/cgroup.kill: No such file or directory
<11>[   64.626530][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_939/cgroup.procs: No such file or directory
<14>[   64.628280][ T1@C2] init: Service 'vendor.refnotify' (pid 946) received signal 15
<14>[   64.628342][ T1@C2] init: Sending signal 9 to service 'vendor.refnotify' (pid 946) process group...
<11>[   64.628656][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_946/cgroup.kill: No such file or directory
<11>[   64.628807][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_946/cgroup.procs: No such file or directory
<14>[   64.630889][ T1@C2] init: Service 'vendor.srtd' (pid 950) received signal 15
<14>[   64.630947][ T1@C2] init: Sending signal 9 to service 'vendor.srtd' (pid 950) process group...
<11>[   64.631243][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_950/cgroup.kill: No such file or directory
<11>[   64.631395][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_950/cgroup.procs: No such file or directory
<14>[   64.633777][ T1@C2] init: Service 'vendor.thermald' (pid 953) received signal 15
<14>[   64.633843][ T1@C2] init: Sending signal 9 to service 'vendor.thermald' (pid 953) process group...
<11>[   64.634203][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_953/cgroup.kill: No such file or directory
<11>[   64.634365][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_953/cgroup.procs: No such file or directory
<14>[   64.636597][ T1@C2] init: Service 'unionpnp_service' (pid 958) received signal 15
<14>[   64.636658][ T1@C2] init: Sending signal 9 to service 'unionpnp_service' (pid 958) process group...
<11>[   64.636981][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_958/cgroup.kill: No such file or directory
<11>[   64.637133][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_958/cgroup.procs: No such file or directory
<14>[   64.640732][ T1@C3] init: Service 'ext_data' (pid 960) received signal 15
<14>[   64.640794][ T1@C3] init: Sending signal 9 to service 'ext_data' (pid 960) process group...
<11>[   64.641128][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_960/cgroup.kill: No such file or directory
<11>[   64.641279][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_960/cgroup.procs: No such file or directory
<14>[   64.644897][ T1@C2] init: Service 'ip_monitor' (pid 963) received signal 15 oneshot service took 48.241001 seconds in background
<14>[   64.644964][ T1@C2] init: Sending signal 9 to service 'ip_monitor' (pid 963) process group...
<11>[   64.645321][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_963/cgroup.kill: No such file or directory
<11>[   64.645577][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_963/cgroup.procs: No such file or directory
<14>[   64.649276][ T1@C2] init: Service 'srmi_proxyd' (pid 966) received signal 15
<14>[   64.649338][ T1@C2] init: Sending signal 9 to service 'srmi_proxyd' (pid 966) process group...
<11>[   64.649724][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_966/cgroup.kill: No such file or directory
<11>[   64.649878][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_966/cgroup.procs: No such file or directory
<14>[   64.658066][ T1@C2] init: Service 'tool_service' (pid 973) received signal 15
<14>[   64.658137][ T1@C2] init: Sending signal 9 to service 'tool_service' (pid 973) process group...
<11>[   64.658471][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_973/cgroup.kill: No such file or directory
<11>[   64.658641][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_973/cgroup.procs: No such file or directory
<14>[   64.660452][ T1@C2] init: Service 'media.swcodec' (pid 989) received signal 15
<14>[   64.660508][ T1@C2] init: Sending signal 9 to service 'media.swcodec' (pid 989) process group...
<14>[   64.662307][ T1@C2] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1046/pid_989
<14>[   64.663936][ T1@C6] init: Service 'vendor.cp_diskserver' (pid 994) received signal 15
<14>[   64.663976][ T1@C6] init: Sending signal 9 to service 'vendor.cp_diskserver' (pid 994) process group...
<11>[   64.664227][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_994/cgroup.kill: No such file or directory
<11>[   64.664306][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_994/cgroup.procs: No such file or directory
<14>[   64.665860][ T1@C6] init: Service 'gatekeeperd' (pid 995) received signal 15
<14>[   64.665895][ T1@C6] init: Sending signal 9 to service 'gatekeeperd' (pid 995) process group...
<11>[   64.666086][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_995/cgroup.kill: No such file or directory
<11>[   64.666159][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_995/cgroup.procs: No such file or directory
<14>[   64.667421][ T1@C6] init: Service 'update_engine' (pid 996) exited with status 0
<14>[   64.667453][ T1@C6] init: Sending signal 9 to service 'update_engine' (pid 996) process group...
<11>[   64.667637][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_996/cgroup.kill: No such file or directory
<11>[   64.667716][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_996/cgroup.procs: No such file or directory
<14>[   64.668979][ T1@C6] init: Service 'vendor.wcnd' (pid 1002) received signal 15
<14>[   64.669012][ T1@C6] init: Sending signal 9 to service 'vendor.wcnd' (pid 1002) process group...
<11>[   64.669189][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_1002/cgroup.kill: No such file or directory
<11>[   64.669263][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_1002/cgroup.procs: No such file or directory
<14>[   64.670565][ T1@C6] init: Service 'vendor.prodproxy' (pid 1003) received signal 15 oneshot service took 47.821999 seconds in background
<14>[   64.670601][ T1@C6] init: Sending signal 9 to service 'vendor.prodproxy' (pid 1003) process group...
<11>[   64.670899][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_1003/cgroup.kill: No such file or directory
<11>[   64.670993][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_1003/cgroup.procs: No such file or directory
<14>[   64.672222][ T1@C6] init: Service 'vendor.wcn_chr' (pid 1010) received signal 15
<14>[   64.672256][ T1@C6] init: Sending signal 9 to service 'vendor.wcn_chr' (pid 1010) process group...
<11>[   64.672397][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_12345/pid_1010/cgroup.kill: No such file or directory
<11>[   64.672462][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_12345/pid_1010/cgroup.procs: No such file or directory
<14>[   64.673746][ T1@C6] init: Service 'wpa_supplicant' (pid 1638) exited with status 0 oneshot service took 27.867001 seconds in background
<14>[   64.673777][ T1@C6] init: Sending signal 9 to service 'wpa_supplicant' (pid 1638) process group...
<11>[   64.673958][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_1638/cgroup.kill: No such file or directory
<11>[   64.674030][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_1638/cgroup.procs: No such file or directory
<14>[   64.675781][ T1@C6] init: Service 'zramwb-sh' (pid 2315) received signal 15 oneshot service took 21.371000 seconds in background
<14>[   64.675820][ T1@C6] init: Sending signal 9 to service 'zramwb-sh' (pid 2315) process group...
<11>[   64.675997][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_2315/cgroup.kill: No such file or directory
<11>[   64.676068][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_2315/cgroup.procs: No such file or directory
<14>[   64.677259][ T1@C6] init: Service 'swappiness-sh' (pid 2320) received signal 15 oneshot service took 21.346001 seconds in background
<14>[   64.677291][ T1@C6] init: Sending signal 9 to service 'swappiness-sh' (pid 2320) process group...
<11>[   64.677496][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_2320/cgroup.kill: No such file or directory
<11>[   64.677569][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_2320/cgroup.procs: No such file or directory
<14>[   64.679069][ T1@C6] init: Service 'zygote_secondary' (pid 556) received signal 15
<14>[   64.679101][ T1@C6] init: Sending signal 9 to service 'zygote_secondary' (pid 556) process group...
<14>[   64.679708][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_556
<14>[   64.681307][ T1@C6] init: Service 'uniStabDataService' (pid 3156) received signal 15
<14>[   64.681340][ T1@C6] init: Sending signal 9 to service 'uniStabDataService' (pid 3156) process group...
<11>[   64.681555][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_3156/cgroup.kill: No such file or directory
<11>[   64.681630][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_3156/cgroup.procs: No such file or directory
<14>[   64.682874][ T1@C6] init: Service 'bootanim' (pid 3294) received signal 15 oneshot service took 7.340000 seconds in background
<14>[   64.682906][ T1@C6] init: Sending signal 9 to service 'bootanim' (pid 3294) process group...
<11>[   64.683083][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1003/pid_3294/cgroup.kill: No such file or directory
<11>[   64.683147][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1003/pid_3294/cgroup.procs: No such file or directory
<14>[   64.684821][ T1@C6] init: Service 'watchdogd' (pid 3528) exited with status 1 oneshot service took 1.279000 seconds in background
<14>[   64.684859][ T1@C6] init: Sending signal 9 to service 'watchdogd' (pid 3528) process group...
<11>[   64.685015][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_3528/cgroup.kill: No such file or directory
<11>[   64.685085][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_3528/cgroup.procs: No such file or directory
<14>[   64.687005][ T1@C6] init: Service 'blank_screen' (pid 3529) exited with status 0 oneshot service took 1.245000 seconds in background
<14>[   64.687037][ T1@C6] init: Sending signal 9 to service 'blank_screen' (pid 3529) process group...
<11>[   64.687196][ T1@C6] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1000/pid_3529/cgroup.kill: No such file or directory
<11>[   64.687265][ T1@C6] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1000/pid_3529/cgroup.procs: No such file or directory
<14>[   64.689172][ T1@C6] init: Untracked pid 969 exited with status 0
<14>[   64.689207][ T1@C6] init: Untracked pid 969 did not have an associated service entry and will not be reaped
<14>[   64.689327][ T1@C6] init: Untracked pid 935 received signal 15
<14>[   64.689343][ T1@C6] init: Untracked pid 935 did not have an associated service entry and will not be reaped
<14>[   64.689548][ T1@C6] init: Untracked pid 924 received signal 15
<14>[   64.689567][ T1@C6] init: Untracked pid 924 did not have an associated service entry and will not be reaped
<14>[   64.689702][ T1@C6] init: Untracked pid 925 received signal 15
<14>[   64.689719][ T1@C6] init: Untracked pid 925 did not have an associated service entry and will not be reaped
<14>[   64.689832][ T1@C6] init: Untracked pid 927 received signal 15
<14>[   64.689847][ T1@C6] init: Untracked pid 927 did not have an associated service entry and will not be reaped
<14>[   64.689956][ T1@C6] init: Untracked pid 592 received signal 15
<14>[   64.689971][ T1@C6] init: Untracked pid 592 did not have an associated service entry and will not be reaped
<14>[   64.690086][ T1@C6] init: Untracked pid 593 received signal 15
<14>[   64.690101][ T1@C6] init: Untracked pid 593 did not have an associated service entry and will not be reaped
<14>[   64.690211][ T1@C6] init: Untracked pid 1511 received signal 15
<14>[   64.690227][ T1@C6] init: Untracked pid 1511 did not have an associated service entry and will not be reaped
<14>[   64.690343][ T1@C6] init: Untracked pid 1806 received signal 15
<14>[   64.690359][ T1@C6] init: Untracked pid 1806 did not have an associated service entry and will not be reaped
<14>[   64.690578][ T1@C6] init: Untracked pid 1989 received signal 15
<14>[   64.690595][ T1@C6] init: Untracked pid 1989 did not have an associated service entry and will not be reaped
<14>[   64.690761][ T1@C6] init: Untracked pid 2290 received signal 15
<14>[   64.690776][ T1@C6] init: Untracked pid 2290 did not have an associated service entry and will not be reaped
<14>[   64.690886][ T1@C6] init: Untracked pid 2302 received signal 15
<14>[   64.690901][ T1@C6] init: Untracked pid 2302 did not have an associated service entry and will not be reaped
<14>[   64.691005][ T1@C6] init: Untracked pid 2308 received signal 15
<14>[   64.691020][ T1@C6] init: Untracked pid 2308 did not have an associated service entry and will not be reaped
<14>[   64.691127][ T1@C6] init: Untracked pid 2512 received signal 15
<14>[   64.691143][ T1@C6] init: Untracked pid 2512 did not have an associated service entry and will not be reaped
<14>[   64.691258][ T1@C6] init: Untracked pid 2650 received signal 15
<14>[   64.691273][ T1@C6] init: Untracked pid 2650 did not have an associated service entry and will not be reaped
<14>[   64.691430][ T1@C6] init: Untracked pid 2893 received signal 15
<14>[   64.691446][ T1@C6] init: Untracked pid 2893 did not have an associated service entry and will not be reaped
<14>[   64.691761][ T1@C6] init: Untracked pid 2917 received signal 15
<14>[   64.691777][ T1@C6] init: Untracked pid 2917 did not have an associated service entry and will not be reaped
<14>[   64.691975][ T1@C6] init: Untracked pid 540 exited with status 143
<14>[   64.691991][ T1@C6] init: Untracked pid 540 did not have an associated service entry and will not be reaped
<14>[   64.692101][ T1@C6] init: Untracked pid 545 exited with status 143
<6>[   64.692104][T394@C1] binder: release 3441:3523 transaction 87003 out, still active
<14>[   64.692117][ T1@C6] init: Untracked pid 545 did not have an associated service entry and will not be reaped
<6>[   64.692137][T394@C1] binder: undelivered TRANSACTION_COMPLETE
<14>[   64.692247][ T1@C6] init: Untracked pid 548 received signal 15
<14>[   64.692263][ T1@C6] init: Untracked pid 548 did not have an associated service entry and will not be reaped
<14>[   64.692371][ T1@C6] init: Untracked pid 566 exited with status 143
<14>[   64.692387][ T1@C6] init: Untracked pid 566 did not have an associated service entry and will not be reaped
<14>[   64.692492][ T1@C6] init: Untracked pid 570 received signal 15
<14>[   64.692508][ T1@C6] init: Untracked pid 570 did not have an associated service entry and will not be reaped
<14>[   64.692614][ T1@C6] init: Untracked pid 577 exited with status 0
<14>[   64.692630][ T1@C6] init: Untracked pid 577 did not have an associated service entry and will not be reaped
<14>[   64.692741][ T1@C6] init: Untracked pid 582 received signal 15
<14>[   64.692756][ T1@C6] init: Untracked pid 582 did not have an associated service entry and will not be reaped
<14>[   64.692864][ T1@C6] init: Untracked pid 550 exited with status 143
<14>[   64.692880][ T1@C6] init: Untracked pid 550 did not have an associated service entry and will not be reaped
<14>[   64.692986][ T1@C6] init: Untracked pid 551 exited with status 0
<14>[   64.693000][ T1@C6] init: Untracked pid 551 did not have an associated service entry and will not be reaped
<14>[   64.693106][ T1@C6] init: Untracked pid 558 exited with status 143
<14>[   64.693122][ T1@C6] init: Untracked pid 558 did not have an associated service entry and will not be reaped
<14>[   64.693228][ T1@C6] init: Untracked pid 562 exited with status 143
<14>[   64.693243][ T1@C6] init: Untracked pid 562 did not have an associated service entry and will not be reaped
<14>[   64.693397][ T1@C6] init: Untracked pid 3561 received signal 9
<14>[   64.693415][ T1@C6] init: Untracked pid 3561 did not have an associated service entry and will not be reaped
<14>[   64.693733][ T1@C6] init: Untracked pid 1521 received signal 15
<14>[   64.693751][ T1@C6] init: Untracked pid 1521 did not have an associated service entry and will not be reaped
<14>[   64.695456][ T1@C6] init: Untracked pid 1522 received signal 15
<14>[   64.695481][ T1@C6] init: Untracked pid 1522 did not have an associated service entry and will not be reaped
<14>[   64.695612][ T1@C6] init: Untracked pid 1525 received signal 15
<14>[   64.695627][ T1@C6] init: Untracked pid 1525 did not have an associated service entry and will not be reaped
<14>[   64.695737][ T1@C6] init: Untracked pid 1527 received signal 15
<14>[   64.695753][ T1@C6] init: Untracked pid 1527 did not have an associated service entry and will not be reaped
<14>[   64.695862][ T1@C6] init: Untracked pid 1643 received signal 15
<14>[   64.695877][ T1@C6] init: Untracked pid 1643 did not have an associated service entry and will not be reaped
<14>[   64.696288][ T1@C6] init: Untracked pid 3441 received signal 15
<14>[   64.696307][ T1@C6] init: Untracked pid 3441 did not have an associated service entry and will not be reaped
<14>[   64.704859][ T1@C0] init: Untracked pid 2372 received signal 15
<14>[   64.704927][ T1@C0] init: Untracked pid 2372 did not have an associated service entry and will not be reaped
<14>[   64.706806][ T1@C0] init: Untracked pid 1680 received signal 15
<14>[   64.706864][ T1@C0] init: Untracked pid 1680 did not have an associated service entry and will not be reaped
<6>[   64.729555][T1773@C2] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[   64.731464][ T1@C6] init: Untracked pid 2713 received signal 15
<14>[   64.731495][ T1@C6] init: Untracked pid 2713 did not have an associated service entry and will not be reaped
<14>[   64.737912][ T1@C6] init: Untracked pid 1507 received signal 15
<14>[   64.737950][ T1@C6] init: Untracked pid 1507 did not have an associated service entry and will not be reaped
<14>[   64.740364][ T1@C6] init: Untracked pid 3035 received signal 15
<14>[   64.740396][ T1@C6] init: Untracked pid 3035 did not have an associated service entry and will not be reaped
<14>[   64.742242][ T1@C6] init: Service 'lmkd' (pid 301) received signal 15
<14>[   64.742277][ T1@C6] init: Sending signal 9 to service 'lmkd' (pid 301) process group...
<14>[   64.742945][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1069/pid_301
<14>[   64.790466][ T1@C6] init: Untracked pid 2770 received signal 15
<14>[   64.790504][ T1@C6] init: Untracked pid 2770 did not have an associated service entry and will not be reaped
<14>[   64.806390][ T1@C6] init: Untracked pid 1509 received signal 15
<14>[   64.806432][ T1@C6] init: Untracked pid 1509 did not have an associated service entry and will not be reaped
<14>[   64.806708][ T1@C6] init: Untracked pid 2213 received signal 15
<14>[   64.806726][ T1@C6] init: Untracked pid 2213 did not have an associated service entry and will not be reaped
<14>[   64.808141][ T1@C6] init: Untracked pid 1910 received signal 15
<14>[   64.808166][ T1@C6] init: Untracked pid 1910 did not have an associated service entry and will not be reaped
<14>[   64.845042][ T1@C1] init: Untracked pid 1733 received signal 15
<14>[   64.845119][ T1@C1] init: Untracked pid 1733 did not have an associated service entry and will not be reaped
<14>[   64.846835][ T1@C1] init: Untracked pid 2467 received signal 15
<14>[   64.846890][ T1@C1] init: Untracked pid 2467 did not have an associated service entry and will not be reaped
<6>[   64.873231][T2979@C6] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[   64.889734][ T1@C1] init: Untracked pid 2196 received signal 15
<14>[   64.889803][ T1@C1] init: Untracked pid 2196 did not have an associated service entry and will not be reaped
<14>[   64.890566][ T1@C1] init: Untracked pid 3385 received signal 15
<14>[   64.890610][ T1@C1] init: Untracked pid 3385 did not have an associated service entry and will not be reaped
<14>[   64.894900][ T1@C1] init: Untracked pid 2434 received signal 15
<14>[   64.894968][ T1@C1] init: Untracked pid 2434 did not have an associated service entry and will not be reaped
<14>[   64.896580][ T1@C0] init: Untracked pid 1851 received signal 15
<14>[   64.896662][ T1@C0] init: Untracked pid 1851 did not have an associated service entry and will not be reaped
<14>[   64.901626][ T1@C1] init: Untracked pid 2419 received signal 15
<14>[   64.901698][ T1@C1] init: Untracked pid 2419 did not have an associated service entry and will not be reaped
<14>[   64.914026][ T1@C1] init: Untracked pid 2259 received signal 15
<14>[   64.914085][ T1@C1] init: Untracked pid 2259 did not have an associated service entry and will not be reaped
<14>[   64.921636][ T1@C1] init: Untracked pid 2400 received signal 15
<14>[   64.921703][ T1@C1] init: Untracked pid 2400 did not have an associated service entry and will not be reaped
<14>[   64.922000][ T1@C1] init: Untracked pid 2560 received signal 15
<14>[   64.922038][ T1@C1] init: Untracked pid 2560 did not have an associated service entry and will not be reaped
<14>[   64.926949][ T1@C1] init: Untracked pid 1538 received signal 15
<14>[   64.927007][ T1@C1] init: Untracked pid 1538 did not have an associated service entry and will not be reaped
<14>[   64.933787][ T1@C1] init: Untracked pid 2324 received signal 15
<14>[   64.933846][ T1@C1] init: Untracked pid 2324 did not have an associated service entry and will not be reaped
<14>[   64.936551][ T1@C1] init: Untracked pid 2358 received signal 15
<14>[   64.936605][ T1@C1] init: Untracked pid 2358 did not have an associated service entry and will not be reaped
<14>[   64.953519][ T1@C1] init: Untracked pid 2534 received signal 15
<14>[   64.953587][ T1@C1] init: Untracked pid 2534 did not have an associated service entry and will not be reaped
<6>[   64.954201][T336@C4] binder: undelivered TRANSACTION_COMPLETE
<6>[   64.954235][T336@C4] binder: undelivered transaction 87013, process died.
<14>[   64.956668][ T1@C1] init: Untracked pid 1508 received signal 15
<14>[   64.956729][ T1@C1] init: Untracked pid 1508 did not have an associated service entry and will not be reaped
<14>[   64.975125][ T1@C1] init: Untracked pid 2501 received signal 15
<14>[   64.975191][ T1@C1] init: Untracked pid 2501 did not have an associated service entry and will not be reaped
<14>[   64.981343][ T1@C1] init: Untracked pid 1560 received signal 15
<14>[   64.981467][ T1@C1] init: Untracked pid 1560 did not have an associated service entry and will not be reaped
<14>[   65.003002][ T1@C1] init: Untracked pid 2274 received signal 15
<14>[   65.003059][ T1@C1] init: Untracked pid 2274 did not have an associated service entry and will not be reaped
<6>[   65.029924][T2161@C0] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<14>[   65.048954][ T1@C1] init: Service 'surfaceflinger' (pid 720) received signal 15
<14>[   65.049021][ T1@C1] init: Sending signal 9 to service 'surfaceflinger' (pid 720) process group...
<4>[   65.049101][ T1@C1] Signal_Debug: Req_proc is init, pid=1, Tar_proc is surfaceflinger, pid=720
<4>[   65.049119][ T1@C1] Signal_Debug: Tar_proc_group is surfaceflinger, tgid=720, sig=9
<14>[   65.050513][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1000/pid_720
<6>[   65.053484][T319@C0] sprd-apcpu-dvfs: policy[0] disables boost it is 60 seconds after boot up
<6>[   65.256383][T2878@C2] SPRDDEBUG gpu shader core power on polling SUCCESS !!
<6>[   65.264609][T2878@C2] unisoc_userlog: userlog_release
<6>[   65.388935][T387@C3] binder: undelivered death notification, b40000762c6efdb0
<6>[   65.405345][T64@C5] binder: send failed reply for transaction 87003, target dead
<14>[   65.425293][ T1@C1] init: Untracked pid 1229 received signal 15
<14>[   65.425553][ T1@C1] init: Untracked pid 1229 did not have an associated service entry and will not be reaped
<6>[   65.427028][T64@C5] binder: undelivered death notification, b40000762c724520
<6>[   65.427082][T64@C5] binder: undelivered death notification, b40000762c6ee9c0
<14>[   66.220674][ T1@C1] init: Service 'uniber' (pid 931) received signal 15
<14>[   66.220827][ T1@C1] init: Sending signal 9 to service 'uniber' (pid 931) process group...
<14>[   66.224281][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_931
<14>[   66.228824][ T1@C5] init: Waiting for 106 pids to be reaped took 2059ms with 0 of them still running
<14>[   66.229083][ T1@C5] init: Stopping 234 services by sending SIGKILL
<14>[   66.229489][ T1@C5] init: Calling /system/bin/vdc volume abort_fuse
<6>[   66.276450][    C6] |_mmcblk0    total complete 5429 requests
<6>[   66.276487][    C6] |_mmcblk0    R complete:   4473 request   435104 sectors
<6>[   66.276497][    C6] |_mmcblk0    R i2i[  8- 8192ms]: 4460    4    7    0    1    1    0    0    0    0    0    0
<6>[   66.276509][    C6] |_mmcblk0    R i2c[  8- 8192ms]: 4473    0    0    0    0    0    0    0    0    0    0    0
<6>[   66.276521][    C6] |_mmcblk0    W complete:    846 request    20632 sectors
<6>[   66.276527][    C6] |_mmcblk0    W i2i[  8- 8192ms]:  839    5    1    1    0    0    0    0    0    0    0    0
<3>[   66.276525][T394@C1] |__c2e     mmc0: 2145    2    2    0    0    0    0    0    0    0    0    0    0    0
<6>[   66.276538][    C6] |_mmcblk0    W i2c[  8- 8192ms]:  846    0    0    0    0    0    0    0    0    0    0    0
<6>[   66.276550][    C6] |_mmcblk0    F complete:    110 request        0 sectors
<6>[   66.276555][    C6] |_mmcblk0    F i2i[  8- 8192ms]:  110    0    0    0    0    0    0    0    0    0    0    0
<6>[   66.276567][    C6] |_mmcblk0    F i2c[  8- 8192ms]:  109    0    1    0    0    0    0    0    0    0    0    0
<6>[   66.276578][    C6] |_loop       total complete 125 requests
<6>[   66.276582][    C6] |_loop       R complete:    125 request     8024 sectors
<3>[   66.276578][T394@C1] |__d2e     mmc0: 1987   36    2    5    0    0    0    0    0    0    0    0    0    0
<6>[   66.276588][    C6] |_loop       R i2i[  8- 8192ms]:  125    0    0    0    0    0    0    0    0    0    0    0
<6>[   66.276599][    C6] |_loop       R i2c[  8- 8192ms]:  102   16    6    1    0    0    0    0    0    0    0    0
<3>[   66.276607][T394@C1] |__blocks  mmc0:    0    0    0    0  784  205  175  272  368  192   34    0    0    0
<3>[   66.276697][T394@C1] |__speed   mmc0: r= 138.73M/s, w= 165.4M/s, r_blk= 169624, w_blk= 6776
<3>[   66.282733][T114@C6] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_get_tempvol_ntc_uv=256893,calib_resistance_vol=-107,vol_adc_mv=257
<6>[   66.284647][T114@C6] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sprd normal_cap_diff = -1, adjust_step = 649, temp_cap = 1000, smooth_cap_diff = -10, *cap = 999, smooth_soc = 997, smooth_soc_decimal = 0
<6>[   66.284755][T114@C6] sprd-fgu 64200000.spi:pmic@0:fgu@c00: sc27xx_fgu_info : init_clbcnt = 49958208, start_work_clbcnt = 49958208, cur_clbcnt = 49917247, cur_1000ma_adc = 1380, vol_1000mv_adc = 690, calib_resist = 10000
<6>[   66.284768][T114@C6] sprd-fgu 64200000.spi:pmic@0:fgu@c00: init_cap = 1000, init_mah = 0, normal_cap = 999, data->cc_mah = -4, Tbat = 322, uusoc_vbat = 0, uusoc_mah = 0, track_sts = 1
<6>[   66.284781][T114@C6] sprd-fgu 64200000.spi:pmic@0:fgu@c00: ocv_uv = 4392000, vbatt_mv = 4381, vbat_cur_ma = -107, vbat_avg_mv = 4335, vbat_cur_avg_ma = -526, absolute_charger_mode = 0, full_percent = 0
<6>[   66.284793][T114@C6] sprd-fgu 64200000.spi:pmic@0:fgu@c00: battery soc = 997, cycle = 15
<15>[   66.315000][T3563@C7] vdc: Waited 0ms for vold
<36>[   66.323577][T316@C3] type=1400 audit(1754537705.675:212): avc:  denied  { create } for  comm="binder:323_5" name="abort.tmp" scontext=u:r:vold:s0 tcontext=u:object_r:fusectlfs:s0 tclass=file permissive=0
<14>[   66.328356][ T1@C6] init: Calling /system/bin/vdc volume shutdown
<15>[   66.402687][T3564@C6] vdc: Waited 0ms for vold
<6>[   66.404297][T415@C4] binder: 323:415 transaction failed 29189/-22, size 104-0 line 3217
<6>[   66.545475][T64@C5] charger-manager charger-manager: vbat: 4381000, vbat_avg: 4335000, OCV: 4392000, ibat: -107000, ibat_avg: -526000, ibus: -22, vbus: 0, msoc: 997, chg_sts: 2, frce_full: 0, chg_lmt_cur: 0, inpt_lmt_cur: 0, chgr_type: 0, Tboard: 466, Tbatt: 322, thm_cur: -22, thm_pwr: 0, is_fchg: 0, fchg_en: 0, tflush: 60, tperiod: 15
<6>[   66.546409][T64@C5] charger-manager charger-manager: new_uisoc = 997, old_uisoc = 998, work_cycle = 15s, cap_one_time = 30s
<14>[   66.557431][ T1@C6] init: Sending signal 9 to service 'vold' (pid 323) process group...
<14>[   66.605715][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_323
<14>[   66.607475][ T1@C6] init: Stopping 4 services by sending SIGKILL
<14>[   66.607627][ T1@C6] init: Sending signal 9 to service 'adbd' (pid 785) process group...
<6>[   66.648495][T1039@C3] musb-sprd 64900000.usb: musb_sprd_runtime_resume: enter
<6>[   66.648554][T1039@C3] musb-sprd 64900000.usb: musb_sprd_resume: enter
<6>[   66.679788][T1039@C3] musb-hdrc musb-hdrc.1.auto: musb runtime resume
<6>[   66.679983][T1039@C3] musb_gadget_vbus_draw 1742 0
<6>[   66.679998][T1039@C3] usb_phy_set_charger_current 233 0
<6>[   66.680012][T1039@C3] musb-sprd 64900000.usb: sprd_musb_disable: enter
<6>[   66.680029][T1039@C3] sprd_musb_try_idle enter, otg->state 0.
<6>[   66.680037][T1039@C3] sprd_musb_try_idle enter, otg->state 0.
<14>[   66.683823][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_0/pid_785
<14>[   66.686387][ T1@C6] init: Sending signal 9 to service 'tombstoned' (pid 446) process group...
<14>[   66.695155][ T1@C6] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1058/pid_446
<14>[   66.697532][ T1@C6] init: Sending signal 9 to service 'console' (pid 305) process group...
<14>[   66.704331][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_2000/pid_305
<14>[   66.707186][ T1@C1] init: Sending signal 9 to service 'logd' (pid 292) process group...
<14>[   66.736681][ T1@C1] libprocessgroup: Removed cgroup /sys/fs/cgroup/uid_1036/pid_292
<14>[   66.738757][ T1@C3] init: Subcontext received signal 15
<14>[   66.738834][ T1@C3] init: Subcontext did not have an associated service entry and will not be reaped
<14>[   66.739511][ T1@C3] init: Service 'logd' (pid 292) received signal 9
<14>[   66.739559][ T1@C3] init: Sending signal 9 to service 'logd' (pid 292) process group...
<11>[   66.739939][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1036/pid_292/cgroup.kill: No such file or directory
<11>[   66.740099][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1036/pid_292/cgroup.procs: No such file or directory
<14>[   66.744680][ T1@C2] init: Service 'console' (pid 305) received signal 9
<14>[   66.744733][ T1@C2] init: Sending signal 9 to service 'console' (pid 305) process group...
<11>[   66.744985][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_2000/pid_305/cgroup.kill: No such file or directory
<11>[   66.745141][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_2000/pid_305/cgroup.procs: No such file or directory
<14>[   66.748283][ T1@C2] init: Service 'vold' (pid 323) received signal 9
<14>[   66.748331][ T1@C2] init: Sending signal 9 to service 'vold' (pid 323) process group...
<11>[   66.748555][ T1@C2] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_323/cgroup.kill: No such file or directory
<11>[   66.748708][ T1@C2] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_323/cgroup.procs: No such file or directory
<11>[   66.748768][ T1@C2] init: Service vold has 'reboot_on_failure' option and failed, shutting down system.
<14>[   66.753792][ T1@C0] init: Service 'tombstoned' (pid 446) received signal 9
<14>[   66.753849][ T1@C0] init: Sending signal 9 to service 'tombstoned' (pid 446) process group...
<11>[   66.754130][ T1@C0] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_1058/pid_446/cgroup.kill: No such file or directory
<11>[   66.754324][ T1@C0] libprocessgroup: Failed to open /sys/fs/cgroup/uid_1058/pid_446/cgroup.procs: No such file or directory
<14>[   66.759675][ T1@C3] init: Service 'adbd' (pid 785) received signal 9
<14>[   66.759724][ T1@C3] init: Sending signal 9 to service 'adbd' (pid 785) process group...
<11>[   66.759949][ T1@C3] libprocessgroup: Failed to write 1 to /sys/fs/cgroup/uid_0/pid_785/cgroup.kill: No such file or directory
<11>[   66.760097][ T1@C3] libprocessgroup: Failed to open /sys/fs/cgroup/uid_0/pid_785/cgroup.procs: No such file or directory
<14>[   66.764526][ T1@C2] init: sync() before umount...
<14>[   67.113862][ T1@C1] init: sync() before umount took349ms
<14>[   67.116437][ T1@C1] init: swapoff() start...
<14>[   67.286659][ T1@C6] init: swapoff() took 170ms
<6>[   67.287283][ T1@C6] zram0: detected capacity change from 4695536 to 0
<6>[   67.305806][T387@C3] musb-hdrc musb-hdrc.1.auto: musb runtime suspend
<6>[   67.306169][T387@C3] musb-sprd 64900000.usb: enter into idle mode
<14>[   67.320172][ T1@C6] init: Ready to unmount apexes. So far shutdown sequence took 3991ms
<14>[   67.419953][T3565@C7] apexd: Started. subcommand = --unmount-all
<14>[   67.422436][T3565@C6] apexd-unmount-all: Populating APEX database from mounts...
<14>[   67.426144][T3565@C6] apexd-unmount-all: Found "/apex/com.android.wifi@351610000" backed by file /system/apex/com.google.android.wifi.apex
<14>[   67.426859][T3565@C6] apexd-unmount-all: Found "/apex/com.android.btservices@352090000" backed by file /system/apex/com.android.btservices.apex
<14>[   67.427223][T3565@C6] apexd-unmount-all: Found "/apex/com.android.adbd@351010000" backed by file /system/apex/com.google.android.adbd.apex
<14>[   67.427565][T3565@C6] apexd-unmount-all: Found "/apex/com.android.media.swcodec@351504000" backed by file /system/apex/com.google.android.media.swcodec.apex
<14>[   67.427902][T3565@C6] apexd-unmount-all: Found "/apex/com.android.nfcservices@352090000" backed by file /system/apex/com.android.nfcservices.apex
<14>[   67.428220][T3565@C6] apexd-unmount-all: Found "/apex/com.android.virt@2" backed by file /system/apex/com.android.virt.apex
<14>[   67.428551][T3565@C6] apexd-unmount-all: Found "/apex/com.android.tzdata@351400020" backed by file /system/apex/com.google.android.tzdata6.apex
<14>[   67.428889][T3565@C6] apexd-unmount-all: Found "/apex/com.android.conscrypt@351412000" backed by file /system/apex/com.google.android.conscrypt.apex
<14>[   67.429231][T3565@C6] apexd-unmount-all: Found "/apex/com.android.i18n@1" backed by file /system/apex/com.android.i18n.apex
<14>[   67.429629][T3565@C6] apexd-unmount-all: Found "/apex/com.android.runtime@1" backed by file /system/apex/com.android.runtime.apex
<14>[   67.429958][T3565@C6] apexd-unmount-all: Found "/apex/com.android.cellbroadcast@351511000" backed by file /system/apex/com.google.android.cellbroadcast.apex
<14>[   67.430296][T3565@C6] apexd-unmount-all: Found "/apex/com.android.neuralnetworks@351010040" backed by file /system/apex/com.google.android.neuralnetworks.apex
<14>[   67.430615][T3565@C6] apexd-unmount-all: Found "/apex/com.android.appsearch@351412000" backed by file /system/apex/com.google.android.appsearch.apex
<14>[   67.430891][T3565@C6] apexd-unmount-all: Found "/apex/com.android.profiling@352090000" backed by file /system/apex/com.android.profiling.apex
<14>[   67.431232][T3565@C6] apexd-unmount-all: Found "/apex/com.android.apex.cts.shim@1" backed by file /system/apex/com.android.apex.cts.shim.apex
<14>[   67.431514][T3565@C6] apexd-unmount-all: Found "/apex/com.android.configinfrastructure@351010000" backed by file /system/apex/com.google.android.configinfrastructure.apex
<14>[   67.431842][T3565@C6] apexd-unmount-all: Found "/apex/com.android.devicelock@342410000" backed by file /system/apex/com.google.android.devicelock.apex
<14>[   67.432167][T3565@C6] apexd-unmount-all: Found "/apex/com.android.adservices@351537040" backed by file /system/apex/com.google.android.adservices.apex
<14>[   67.432802][T3565@C6] apexd-unmount-all: Found "/apex/com.android.healthfitness@351511060" backed by file /system/apex/com.google.android.healthfitness.apex
<14>[   67.433132][T3565@C6] apexd-unmount-all: Found "/apex/com.android.ipsec@351410000" backed by file /system/apex/com.google.android.ipsec.apex
<14>[   67.433497][T3565@C6] apexd-unmount-all: Found "/apex/com.android.rkpd@351310000" backed by file /system/apex/com.google.android.rkpd.apex
<14>[   67.433790][T3565@C6] apexd-unmount-all: Found "/apex/com.android.media@351504000" backed by file /system/apex/com.google.android.media.apex
<14>[   67.434121][T3565@C6] apexd-unmount-all: Found "/apex/com.android.os.statsd@351610000" backed by file /system/apex/com.google.android.os.statsd.apex
<14>[   67.434460][T3565@C6] apexd-unmount-all: Found "/apex/com.android.vndk.v33@1" backed by file /system_ext/apex/com.android.vndk.v33.apex
<14>[   67.434809][T3565@C6] apexd-unmount-all: Found "/apex/com.android.extservices@351538083" backed by file /system/apex/com.google.android.extservices_tplus.apex
<14>[   67.435099][T3565@C6] apexd-unmount-all: Found "/apex/com.android.uwb@351310040" backed by file /system/apex/com.google.android.uwb.apex
<14>[   67.435434][T3565@C6] apexd-unmount-all: Found "/apex/com.android.mediaprovider@351613160" backed by file /system/apex/com.google.android.mediaprovider.apex
<14>[   67.435768][T3565@C6] apexd-unmount-all: Found "/apex/com.android.art@351610080" backed by file /system/apex/com.google.android.art.apex
<14>[   67.436091][T3565@C6] apexd-unmount-all: Found "/apex/com.android.permission@351610020" backed by file /system/apex/com.google.android.permission.apex
<14>[   67.436435][T3565@C6] apexd-unmount-all: Found "/apex/com.android.compos@2" backed by file /system_ext/apex/com.android.compos.apex
<14>[   67.436773][T3565@C6] apexd-unmount-all: Found "/apex/com.android.sdkext@351415000" backed by file /system/apex/com.google.android.sdkext.apex
<14>[   67.437082][T3565@C6] apexd-unmount-all: Found "/apex/com.google.mainline.primary.libs@351165000" backed by file /system/apex/com.google.mainline.primary.libs.apex
<14>[   67.437445][T3565@C6] apexd-unmount-all: Found "/apex/com.android.resolv@351510000" backed by file /system/apex/com.google.android.resolv.apex
<14>[   67.437739][T3565@C6] apexd-unmount-all: Found "/apex/com.android.ondevicepersonalization@351541000" backed by file /system/apex/com.google.android.ondevicepersonalization.apex
<14>[   67.438067][T3565@C6] apexd-unmount-all: Found "/apex/com.android.tethering@351510080" backed by file /system/apex/com.google.android.tethering.apex
<14>[   67.438418][T3565@C6] apexd-unmount-all: Found "/apex/com.android.scheduling@351010000" backed by file /system/apex/com.google.android.scheduling.apex
<14>[   67.438708][T3565@C6] apexd-unmount-all: 36 packages restored.
<14>[   67.438781][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.adbd.apex mounted on /apex/com.android.adbd@351010000
<14>[   67.472905][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.adservices.apex mounted on /apex/com.android.adservices@351537040
<14>[   67.505854][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.android.apex.cts.shim.apex mounted on /apex/com.android.apex.cts.shim@1
<14>[   67.537265][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.appsearch.apex mounted on /apex/com.android.appsearch@351412000
<14>[   67.565110][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.art.apex mounted on /apex/com.android.art@351610080
<14>[   67.616206][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.android.btservices.apex mounted on /apex/com.android.btservices@352090000
<14>[   67.657789][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.cellbroadcast.apex mounted on /apex/com.android.cellbroadcast@351511000
<3>[   67.676615][T1023@C0] sprd-screen_check: bl_dev is null, skip check, np:000000000e625cbb, bl_np:0000000000000000!!
<14>[   67.702106][T3565@C6] apexd-unmount-all: Unmounting /system_ext/apex/com.android.compos.apex mounted on /apex/com.android.compos@2
<14>[   67.733657][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.configinfrastructure.apex mounted on /apex/com.android.configinfrastructure@351010000
<14>[   67.768946][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.conscrypt.apex mounted on /apex/com.android.conscrypt@351412000
<14>[   67.797046][T3565@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.devicelock.apex mounted on /apex/com.android.devicelock@342410000
<14>[   67.838335][T3565@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.extservices_tplus.apex mounted on /apex/com.android.extservices@351538083
<14>[   67.873037][T3565@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.healthfitness.apex mounted on /apex/com.android.healthfitness@351511060
<14>[   67.901747][T3565@C2] apexd-unmount-all: Unmounting /system/apex/com.android.i18n.apex mounted on /apex/com.android.i18n@1
<6>[   67.930271][T387@C3] musb-sprd 64900000.usb: musb_sprd_runtime_suspend: enter
<6>[   67.930356][T387@C3] musb-sprd 64900000.usb: musb_sprd_suspend: enter
<14>[   67.945147][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.ipsec.apex mounted on /apex/com.android.ipsec@351410000
<14>[   67.977159][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.media.apex mounted on /apex/com.android.media@351504000
<14>[   68.014398][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.media.swcodec.apex mounted on /apex/com.android.media.swcodec@351504000
<14>[   68.064919][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.mediaprovider.apex mounted on /apex/com.android.mediaprovider@351613160
<14>[   68.098324][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.neuralnetworks.apex mounted on /apex/com.android.neuralnetworks@351010040
<14>[   68.136191][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.android.nfcservices.apex mounted on /apex/com.android.nfcservices@352090000
<14>[   68.173437][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.ondevicepersonalization.apex mounted on /apex/com.android.ondevicepersonalization@351541000
<14>[   68.213326][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.os.statsd.apex mounted on /apex/com.android.os.statsd@351610000
<14>[   68.234472][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.permission.apex mounted on /apex/com.android.permission@351610020
<14>[   68.268335][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.android.profiling.apex mounted on /apex/com.android.profiling@352090000
<14>[   68.290099][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.resolv.apex mounted on /apex/com.android.resolv@351510000
<14>[   68.317058][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.rkpd.apex mounted on /apex/com.android.rkpd@351310000
<14>[   68.349203][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.android.runtime.apex mounted on /apex/com.android.runtime@1
<11>[   68.350802][T3565@C6] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.runtime: Device or resource busy
<14>[   68.350859][T3565@C6] apexd-unmount-all: Unmounting /system/apex/com.google.android.scheduling.apex mounted on /apex/com.android.scheduling@351010000
<14>[   68.384994][T3565@C1] apexd-unmount-all: Unmounting /system/apex/com.google.android.sdkext.apex mounted on /apex/com.android.sdkext@351415000
<14>[   68.409290][T3565@C2] apexd-unmount-all: Unmounting /system/apex/com.google.android.tethering.apex mounted on /apex/com.android.tethering@351510080
<14>[   68.458564][T3565@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.tzdata6.apex mounted on /apex/com.android.tzdata@351400020
<14>[   68.484846][T3565@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.uwb.apex mounted on /apex/com.android.uwb@351310040
<14>[   68.514138][T3565@C0] apexd-unmount-all: Unmounting /system/apex/com.android.virt.apex mounted on /apex/com.android.virt@2
<14>[   68.545265][T3565@C0] apexd-unmount-all: Unmounting /system_ext/apex/com.android.vndk.v33.apex mounted on /apex/com.android.vndk.v33@1
<11>[   68.547158][T3565@C0] apexd-unmount-all: Failed to unmount bind-mount /apex/com.android.vndk.v33: Device or resource busy
<14>[   68.547239][T3565@C0] apexd-unmount-all: Unmounting /system/apex/com.google.android.wifi.apex mounted on /apex/com.android.wifi@351610000
<14>[   68.595998][T3565@C2] apexd-unmount-all: Unmounting /system/apex/com.google.mainline.primary.libs.apex mounted on /apex/com.google.mainline.primary.libs@351165000
<14>[   68.647053][ T1@C6] apexd: apexd terminated by exit(1)
<14>[   68.647053][ T1@C6] 
<11>[   68.647523][ T1@C6] init: '/system/bin/apexd --unmount-all' failed : 256
<14>[   68.669581][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/data_ce/null/0 opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.670798][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/data_ce/null/0 opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.670870][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/ref_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.671506][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/ref_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.671529][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/cur_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.672442][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/cur_profiles opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.672501][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/storage_area opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.673090][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/storage_area opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.673115][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/misc_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.673659][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/misc_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.673681][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/misc_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.674563][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/misc_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.674619][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/data_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.675212][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/data_de/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.675234][ T1@C7] init: Unmounting /dev/block/dm-50:/data_mirror/data_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.675777][ T1@C7] init: Umounted /dev/block/dm-50:/data_mirror/data_ce/null opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   68.675816][ T1@C7] init: Unmounting /dev/block/dm-50:/data opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<6>[   68.848323][T460@C6] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[   68.848358][T460@C6] WCN BASE: tx:at+loopcheck=68842,36185
<6>[   68.848358][T460@C6] 
<6>[   68.848454][T460@C6] WCN BASE: mdbg_tx_cb, chn:0
<6>[   68.856675][T369@C5] WCN BASE: : rx:loopcheck_ack:ap_send=68842,cp2_bootup=36185,cp2_send=69320
<6>[   68.856675][T369@C5] 
<3>[   69.602697][T1023@C0] sprd-screen_check: bl_dev is null, skip check, np:000000000e625cbb, bl_np:0000000000000000!!
<3>[   70.028764][T1004@C3] |__i2s     mmc0: 2145   37   24   22    0    3    0    0    0    0    0    0    0    0
<3>[   70.028836][T1004@C3] |__i2e     mmc0: 2041  107   34   45    0    3    1    0    0    0    0    0    0    0
<3>[   70.028864][T1004@C3] |__swcq    mmc0: cmdq_mode= 0, recovery_cnt= 0, qcnt= 0, cmdq_cnt= 0
<3>[   70.028878][T1004@C3] |__swcq    mmc0: cmd resp err cnt 0, 0, 0, 0, 0, 0, 0
<3>[   70.161929][T1023@C0] sprd-screen_check: bl_dev is null, skip check, np:000000000e625cbb, bl_np:0000000000000000!!
<3>[   70.537907][T1023@C0] sprd-screen_check: bl_dev is null, skip check, np:000000000e625cbb, bl_np:0000000000000000!!
<6>[   70.857918][T1252@C6] sprd-apcpu-dvfs: update cluster 0 temp -274 dvfs table
<6>[   70.872751][T1252@C6] sprd-apcpu-dvfs: cluster[0] update max freq[1612000] by temp[48012]
<6>[   70.872929][T1252@C6] sprd-apcpu-dvfs: update cluster 1 temp -274 dvfs table
<6>[   70.883905][T1252@C6] sprd-apcpu-dvfs: cluster[1] update max freq[1612000] by temp[48012]
<3>[   71.281027][T394@C1] |__c2e     mmc0: 1543    0    0    0    0    0    0    0    0    0    0    0    0    0
<3>[   71.281126][T394@C1] |__d2e     mmc0:  143   55  365   29    6    1    0    0    0    0    0    0    0    0
<3>[   71.281188][T394@C1] |__blocks  mmc0:  404    0    0    0   66   51   16    7    5    6   19   25    0    0
<3>[   71.281248][T394@C1] |__speed   mmc0: r= 24.6M/s, w= 165.51M/s, r_blk= 1336, w_blk= 45696
<6>[   73.968824][T460@C0] WCN BASE: Platform Version:WCN_TRUNK_22A_W24.38.3Project Version:uww2631_qogirL6HW Version:....09-18-2024 16:26:32
<6>[   73.968905][T460@C0] WCN BASE: tx:at+loopcheck=73962,36185
<6>[   73.968905][T460@C0] 
<6>[   73.969228][T460@C0] WCN BASE: mdbg_tx_cb, chn:0
<6>[   73.977496][T369@C5] WCN BASE: : rx:loopcheck_ack:ap_send=73962,cp2_bootup=36185,cp2_send=74441
<6>[   73.977496][T369@C5] 
<4>[   73.978049][T371@C2] mdbg_ring_write: 120 callbacks suppressed
<6>[   73.978074][T371@C2] WCN BASE: mdbg_ring_write totallen:456000 write:2048 wp:0000000018799d6c rp:000000005369c8da [rate:10634]
<14>[   74.221996][ T1@C6] init: Umounted /dev/block/dm-50:/data opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal
<14>[   74.222054][ T1@C6] init: Unmounting /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   74.564015][ T1@C0] init: Umounted /dev/block/mmcblk0p48:/blackbox opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   74.564106][ T1@C0] init: Unmounting /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   74.604194][ T1@C7] init: Umounted /dev/block/mmcblk0p47:/cache opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   74.604279][ T1@C7] init: Unmounting /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[   74.618506][ T1@C7] init: Umounted /dev/block/mmcblk0p1:/mnt/vendor opts rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc
<14>[   74.618568][ T1@C7] init: Unmounting /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   74.689551][ T1@C6] init: Umounted /dev/block/mmcblk0p51:/metadata opts rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal
<14>[   74.689620][ T1@C6] init: Pause reboot monitor thread before fsck
<14>[   74.698313][T3524@C2] init: remaining_shutdown_time: 295
<14>[   74.789192][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   74.789192][ T1@C7] 
<14>[   74.789233][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   74.789233][ T1@C7] 
<14>[   74.789247][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   74.789247][ T1@C7] 
<14>[   74.789261][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.789261][ T1@C7] 
<14>[   74.789274][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   74.789274][ T1@C7] 
<14>[   74.789286][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.789286][ T1@C7] 
<14>[   74.789299][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.789299][ T1@C7] 
<14>[   74.789312][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   74.789312][ T1@C7] 
<14>[   74.789325][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   74.789325][ T1@C7] 
<14>[   74.789337][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   74.789337][ T1@C7] 
<14>[   74.789349][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   74.789349][ T1@C7] 
<14>[   74.789423][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   74.789423][ T1@C7] 
<14>[   74.789436][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   74.789436][ T1@C7] 
<14>[   74.789449][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   74.789449][ T1@C7] 
<14>[   74.789461][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007a028be020,cp1_version:0xa694ce1
<14>[   74.789461][ T1@C7] 
<14>[   74.789474][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   74.789474][ T1@C7] 
<14>[   74.789486][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   74.789486][ T1@C7] 
<14>[   74.789499][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007a028c0040,cp2_version:0xa694ce0
<14>[   74.789499][ T1@C7] 
<14>[   74.789512][ T1@C7] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   74.789512][ T1@C7] 
<14>[   74.789524][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   74.789524][ T1@C7] 
<14>[   74.789538][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   74.789538][ T1@C7] 
<14>[   74.789551][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   74.789551][ T1@C7] 
<14>[   74.875664][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   74.875664][ T1@C7] 
<14>[   74.875705][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   74.875705][ T1@C7] 
<14>[   74.875718][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   74.875718][ T1@C7] 
<14>[   74.875731][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.875731][ T1@C7] 
<14>[   74.875743][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   74.875743][ T1@C7] 
<14>[   74.875756][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.875756][ T1@C7] 
<14>[   74.875770][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.875770][ T1@C7] 
<14>[   74.875782][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   74.875782][ T1@C7] 
<14>[   74.875794][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   74.875794][ T1@C7] 
<14>[   74.875807][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   74.875807][ T1@C7] 
<14>[   74.875819][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   74.875819][ T1@C7] 
<14>[   74.875832][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   74.875832][ T1@C7] 
<14>[   74.875844][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   74.875844][ T1@C7] 
<14>[   74.875856][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   74.875856][ T1@C7] 
<14>[   74.875868][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000076991e1030,cp1_version:0xa694ce1
<14>[   74.875868][ T1@C7] 
<14>[   74.875880][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   74.875880][ T1@C7] 
<14>[   74.875892][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   74.875892][ T1@C7] 
<14>[   74.875903][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000076991e2040,cp2_version:0xa694ce0
<14>[   74.875903][ T1@C7] 
<14>[   74.875915][ T1@C7] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   74.875915][ T1@C7] 
<14>[   74.875927][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   74.875927][ T1@C7] 
<14>[   74.875939][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   74.875939][ T1@C7] 
<14>[   74.875950][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   74.875950][ T1@C7] 
<14>[   74.969159][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   74.969159][ T1@C6] 
<14>[   74.969208][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[   74.969208][ T1@C6] 
<14>[   74.969226][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[   74.969226][ T1@C6] 
<14>[   74.969243][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.969243][ T1@C6] 
<14>[   74.969259][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[   74.969259][ T1@C6] 
<14>[   74.969275][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.969275][ T1@C6] 
<14>[   74.969292][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   74.969292][ T1@C6] 
<14>[   74.969308][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   74.969308][ T1@C6] 
<14>[   74.969325][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   74.969325][ T1@C6] 
<14>[   74.969341][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[   74.969341][ T1@C6] 
<14>[   74.969400][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[   74.969400][ T1@C6] 
<14>[   74.969416][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   74.969416][ T1@C6] 
<14>[   74.969433][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   74.969433][ T1@C6] 
<14>[   74.969449][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   74.969449][ T1@C6] 
<14>[   74.969465][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007dab4a4020,cp1_version:0xa694ce1
<14>[   74.969465][ T1@C6] 
<14>[   74.969481][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   74.969481][ T1@C6] 
<14>[   74.969498][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   74.969498][ T1@C6] 
<14>[   74.969515][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007dab4a6040,cp2_version:0xa694ce0
<14>[   74.969515][ T1@C6] 
<14>[   74.969531][ T1@C6] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   74.969531][ T1@C6] 
<14>[   74.969547][ T1@C6] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   74.969547][ T1@C6] 
<14>[   74.969563][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   74.969563][ T1@C6] 
<14>[   74.969579][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[   74.969579][ T1@C6] 
<14>[   75.058215][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.058215][ T1@C6] 
<14>[   75.058274][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.058274][ T1@C6] 
<14>[   75.058300][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[   75.058300][ T1@C6] 
<14>[   75.058328][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.058328][ T1@C6] 
<14>[   75.058354][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[   75.058354][ T1@C6] 
<14>[   75.058380][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.058380][ T1@C6] 
<14>[   75.058407][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.058407][ T1@C6] 
<14>[   75.058433][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   75.058433][ T1@C6] 
<14>[   75.058459][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.058459][ T1@C6] 
<14>[   75.058484][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[   75.058484][ T1@C6] 
<14>[   75.058509][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.058509][ T1@C6] 
<14>[   75.058534][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   75.058534][ T1@C6] 
<14>[   75.058560][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.058560][ T1@C6] 
<14>[   75.058585][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.058585][ T1@C6] 
<14>[   75.058610][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb40000771ee7f020,cp1_version:0xa694ce1
<14>[   75.058610][ T1@C6] 
<14>[   75.058636][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.058636][ T1@C6] 
<14>[   75.058661][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.058661][ T1@C6] 
<14>[   75.058686][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb40000771ee81040,cp2_version:0xa694ce0
<14>[   75.058686][ T1@C6] 
<14>[   75.058711][ T1@C6] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   75.058711][ T1@C6] 
<14>[   75.058735][ T1@C6] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   75.058735][ T1@C6] 
<14>[   75.058760][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   75.058760][ T1@C6] 
<14>[   75.058785][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[   75.058785][ T1@C6] 
<14>[   75.145600][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.145600][ T1@C7] 
<14>[   75.145650][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.145650][ T1@C7] 
<14>[   75.145668][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   75.145668][ T1@C7] 
<14>[   75.145685][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.145685][ T1@C7] 
<14>[   75.145702][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   75.145702][ T1@C7] 
<14>[   75.145718][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.145718][ T1@C7] 
<14>[   75.145735][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.145735][ T1@C7] 
<14>[   75.145752][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   75.145752][ T1@C7] 
<14>[   75.145769][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.145769][ T1@C7] 
<14>[   75.145785][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   75.145785][ T1@C7] 
<14>[   75.145801][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.145801][ T1@C7] 
<14>[   75.145817][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   75.145817][ T1@C7] 
<14>[   75.145833][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.145833][ T1@C7] 
<14>[   75.145849][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.145849][ T1@C7] 
<14>[   75.145865][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007855a3b010,cp1_version:0xa694ce1
<14>[   75.145865][ T1@C7] 
<14>[   75.145881][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.145881][ T1@C7] 
<14>[   75.145898][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.145898][ T1@C7] 
<14>[   75.145914][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007855a3e040,cp2_version:0xa694ce0
<14>[   75.145914][ T1@C7] 
<14>[   75.145930][ T1@C7] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   75.145930][ T1@C7] 
<14>[   75.145946][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   75.145946][ T1@C7] 
<14>[   75.145962][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   75.145962][ T1@C7] 
<14>[   75.145978][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   75.145978][ T1@C7] 
<14>[   75.231820][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.231820][ T1@C7] 
<14>[   75.231868][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.231868][ T1@C7] 
<14>[   75.231885][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   75.231885][ T1@C7] 
<14>[   75.231902][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.231902][ T1@C7] 
<14>[   75.231918][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   75.231918][ T1@C7] 
<14>[   75.231937][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.231937][ T1@C7] 
<14>[   75.231954][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.231954][ T1@C7] 
<14>[   75.231971][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   75.231971][ T1@C7] 
<14>[   75.231988][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.231988][ T1@C7] 
<14>[   75.232005][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   75.232005][ T1@C7] 
<14>[   75.232022][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.232022][ T1@C7] 
<14>[   75.232037][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   75.232037][ T1@C7] 
<14>[   75.232053][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.232053][ T1@C7] 
<14>[   75.232070][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.232070][ T1@C7] 
<14>[   75.232086][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007dbeecb020,cp1_version:0xa694ce1
<14>[   75.232086][ T1@C7] 
<14>[   75.232102][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.232102][ T1@C7] 
<14>[   75.232118][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.232118][ T1@C7] 
<14>[   75.232134][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007dbeecc030,cp2_version:0xa694ce0
<14>[   75.232134][ T1@C7] 
<14>[   75.232150][ T1@C7] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   75.232150][ T1@C7] 
<14>[   75.232166][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   75.232166][ T1@C7] 
<14>[   75.232182][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   75.232182][ T1@C7] 
<14>[   75.232198][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   75.232198][ T1@C7] 
<14>[   75.319240][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.319240][ T1@C6] 
<14>[   75.319286][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.319286][ T1@C6] 
<14>[   75.319303][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[   75.319303][ T1@C6] 
<14>[   75.319320][ T1@C6] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.319320][ T1@C6] 
<14>[   75.319337][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[   75.319337][ T1@C6] 
<14>[   75.319353][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.319353][ T1@C6] 
<14>[   75.319370][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.319370][ T1@C6] 
<14>[   75.319388][ T1@C6] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   75.319388][ T1@C6] 
<14>[   75.319404][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.319404][ T1@C6] 
<14>[   75.319420][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[   75.319420][ T1@C6] 
<14>[   75.319436][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.319436][ T1@C6] 
<14>[   75.319452][ T1@C6] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   75.319452][ T1@C6] 
<14>[   75.319469][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.319469][ T1@C6] 
<14>[   75.319484][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.319484][ T1@C6] 
<14>[   75.319500][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007a43198040,cp1_version:0xa694ce1
<14>[   75.319500][ T1@C6] 
<14>[   75.319516][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.319516][ T1@C6] 
<14>[   75.319532][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.319532][ T1@C6] 
<14>[   75.319548][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007a43195010,cp2_version:0xa694ce0
<14>[   75.319548][ T1@C6] 
<14>[   75.319563][ T1@C6] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   75.319563][ T1@C6] 
<14>[   75.319579][ T1@C6] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   75.319579][ T1@C6] 
<14>[   75.319595][ T1@C6] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   75.319595][ T1@C6] 
<14>[   75.319610][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[   75.319610][ T1@C6] 
<14>[   75.407712][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.407712][ T1@C7] 
<14>[   75.407763][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.407763][ T1@C7] 
<14>[   75.407782][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   75.407782][ T1@C7] 
<14>[   75.407802][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.407802][ T1@C7] 
<14>[   75.407820][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   75.407820][ T1@C7] 
<14>[   75.407841][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.407841][ T1@C7] 
<14>[   75.407862][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.407862][ T1@C7] 
<14>[   75.407881][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   75.407881][ T1@C7] 
<14>[   75.407903][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.407903][ T1@C7] 
<14>[   75.407922][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   75.407922][ T1@C7] 
<14>[   75.407941][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.407941][ T1@C7] 
<14>[   75.407959][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   75.407959][ T1@C7] 
<14>[   75.407978][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.407978][ T1@C7] 
<14>[   75.407997][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.407997][ T1@C7] 
<14>[   75.408015][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007514690040,cp1_version:0xa694ce1
<14>[   75.408015][ T1@C7] 
<14>[   75.408034][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.408034][ T1@C7] 
<14>[   75.408052][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.408052][ T1@C7] 
<14>[   75.408071][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb40000751468e020,cp2_version:0xa694ce0
<14>[   75.408071][ T1@C7] 
<14>[   75.408090][ T1@C7] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   75.408090][ T1@C7] 
<14>[   75.408108][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   75.408108][ T1@C7] 
<14>[   75.408126][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   75.408126][ T1@C7] 
<14>[   75.408145][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   75.408145][ T1@C7] 
<14>[   75.497773][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.497773][ T1@C7] 
<14>[   75.497825][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.497825][ T1@C7] 
<14>[   75.497845][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   75.497845][ T1@C7] 
<14>[   75.497865][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.497865][ T1@C7] 
<14>[   75.497886][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   75.497886][ T1@C7] 
<14>[   75.497906][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.497906][ T1@C7] 
<14>[   75.497927][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.497927][ T1@C7] 
<14>[   75.497948][ T1@C7] fsck.f2fs: Info: superblock features = 1499 :  encrypt extra_attr project_quota quota verity casefold
<14>[   75.497948][ T1@C7] 
<14>[   75.497967][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.497967][ T1@C7] 
<14>[   75.497986][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   75.497986][ T1@C7] 
<14>[   75.498005][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.498005][ T1@C7] 
<14>[   75.498024][ T1@C7] fsck.f2fs: Info: total FS sectors = 12043008 (47043 MB)
<14>[   75.498024][ T1@C7] 
<14>[   75.498043][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.498043][ T1@C7] 
<14>[   75.498062][ T1@C7] fsck.f2fs: cp_addr: 206, cur_version:0xa694ce1 ubc:0xb54800, vbc:0x1d0e0c
<14>[   75.498062][ T1@C7] 
<14>[   75.498081][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb400007cce3c3020,cp1_version:0xa694ce1
<14>[   75.498081][ T1@C7] 
<14>[   75.498100][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.498100][ T1@C7] 
<14>[   75.498118][ T1@C7] fsck.f2fs: cp_addr: 407, cur_version:0xa694ce0 ubc:0xb54800, vbc:0x1d0e1e
<14>[   75.498118][ T1@C7] 
<14>[   75.498137][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb400007cce3c4030,cp2_version:0xa694ce0
<14>[   75.498137][ T1@C7] 
<14>[   75.498155][ T1@C7] fsck.f2fs: Info: CKPT version = a694ce1
<14>[   75.498155][ T1@C7] 
<14>[   75.498174][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7617, prev: 60
<14>[   75.498174][ T1@C7] 
<14>[   75.498192][ T1@C7] fsck.f2fs: Info: checkpoint state = c5 :  nat_bits crc compacted_summary unmount
<14>[   75.498192][ T1@C7] 
<14>[   75.498211][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   75.498211][ T1@C7] 
<5>[   75.570913][T91@C5] audit: type=1400 audit(1754537714.923:213): avc:  denied  { getattr } for  pid=3575 comm="fsck.f2fs" path="/sys/devices/platform/soc/soc:ap-apb/201d0000.sdio/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p48/partition" dev="sysfs" ino=38623 scontext=u:r:fsck:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
<14>[   75.583927][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.583927][ T1@C6] 
<14>[   75.583977][ T1@C6] fsck.f2fs: 	Info: can't find /sys, assuming normal block device
<14>[   75.583977][ T1@C6] 
<14>[   75.583997][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.583997][ T1@C6] 
<14>[   75.584017][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[   75.584017][ T1@C6] 
<14>[   75.584035][ T1@C6] fsck.f2fs:   "5.15.0-139-generic"
<14>[   75.584035][ T1@C6] 
<14>[   75.584054][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[   75.584054][ T1@C6] 
<14>[   75.584073][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.584073][ T1@C6] 
<14>[   75.584092][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.584092][ T1@C6] 
<14>[   75.584111][ T1@C6] fsck.f2fs: Info: superblock features = 499 :  encrypt extra_attr project_quota quota verity
<14>[   75.584111][ T1@C6] 
<14>[   75.584132][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.584132][ T1@C6] 
<14>[   75.584151][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[   75.584151][ T1@C6] 
<14>[   75.584169][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.584169][ T1@C6] 
<14>[   75.584188][ T1@C6] fsck.f2fs: Info: total FS sectors = 1024000 (500 MB)
<14>[   75.584188][ T1@C6] 
<14>[   75.584207][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x58df69f5 ubc:0x18200, vbc:0xd7e
<14>[   75.584207][ T1@C6] 
<14>[   75.584225][ T1@C6] fsck.f2fs: cp_addr: 206, cur_version:0x58df69f5 ubc:0x18200, vbc:0xd7e
<14>[   75.584225][ T1@C6] 
<14>[   75.584244][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000078bf799010,cp1_version:0x58df69f5
<14>[   75.584244][ T1@C6] 
<14>[   75.584263][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x58df69f4 ubc:0x18200, vbc:0xd7e
<14>[   75.584263][ T1@C6] 
<14>[   75.584281][ T1@C6] fsck.f2fs: cp_addr: 406, cur_version:0x58df69f4 ubc:0x18200, vbc:0xd7e
<14>[   75.584281][ T1@C6] 
<14>[   75.584300][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000078bf79a020,cp2_version:0x58df69f4
<14>[   75.584300][ T1@C6] 
<14>[   75.584318][ T1@C6] fsck.f2fs: Info: CKPT version = 58df69f5
<14>[   75.584318][ T1@C6] 
<14>[   75.584337][ T1@C6] fsck.f2fs: Info: version timestamp cur: 9143, prev: 64
<14>[   75.584337][ T1@C6] 
<14>[   75.584355][ T1@C6] fsck.f2fs: Info: checkpoint state = 45 :  crc compacted_summary unmount
<14>[   75.584355][ T1@C6] 
<14>[   75.584373][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[   75.584373][ T1@C6] 
<5>[   75.651885][T91@C5] audit: type=1400 audit(1754537715.003:214): avc:  denied  { getattr } for  pid=3576 comm="fsck.f2fs" path="/sys/devices/platform/soc/soc:ap-apb/201d0000.sdio/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p47/partition" dev="sysfs" ino=38602 scontext=u:r:fsck:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
<14>[   75.665982][ T1@C6] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.665982][ T1@C6] 
<14>[   75.666030][ T1@C6] fsck.f2fs: 	Info: can't find /sys, assuming normal block device
<14>[   75.666030][ T1@C6] 
<14>[   75.666050][ T1@C6] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.666050][ T1@C6] 
<14>[   75.666070][ T1@C6] fsck.f2fs: Info: MKFS version
<14>[   75.666070][ T1@C6] 
<14>[   75.666089][ T1@C6] fsck.f2fs:   "5.15.0-139-generic"
<14>[   75.666089][ T1@C6] 
<14>[   75.666107][ T1@C6] fsck.f2fs: Info: FSCK version
<14>[   75.666107][ T1@C6] 
<14>[   75.666127][ T1@C6] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.666127][ T1@C6] 
<14>[   75.666146][ T1@C6] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.666146][ T1@C6] 
<14>[   75.666166][ T1@C6] fsck.f2fs: Info: superblock features = 499 :  encrypt extra_attr project_quota quota verity
<14>[   75.666166][ T1@C6] 
<14>[   75.666186][ T1@C6] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.666186][ T1@C6] 
<14>[   75.666205][ T1@C6] fsck.f2fs: Info: Segments per section = 1
<14>[   75.666205][ T1@C6] 
<14>[   75.666223][ T1@C6] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.666223][ T1@C6] 
<14>[   75.666242][ T1@C6] fsck.f2fs: Info: total FS sectors = 131072 (64 MB)
<14>[   75.666242][ T1@C6] 
<14>[   75.666260][ T1@C6] fsck.f2fs: cp_addr: 200, pre_version:0x58df68c3 ubc:0x1000, vbc:0x25
<14>[   75.666260][ T1@C6] 
<14>[   75.666279][ T1@C6] fsck.f2fs: cp_addr: 207, cur_version:0x58df68c3 ubc:0x1000, vbc:0x25
<14>[   75.666279][ T1@C6] 
<14>[   75.666297][ T1@C6] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000074344cd030,cp1_version:0x58df68c3
<14>[   75.666297][ T1@C6] 
<14>[   75.666316][ T1@C6] fsck.f2fs: cp_addr: 400, pre_version:0x58df68c4 ubc:0x1000, vbc:0x25
<14>[   75.666316][ T1@C6] 
<14>[   75.666334][ T1@C6] fsck.f2fs: cp_addr: 407, cur_version:0x58df68c4 ubc:0x1000, vbc:0x25
<14>[   75.666334][ T1@C6] 
<14>[   75.666353][ T1@C6] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000074344cb010,cp2_version:0x58df68c4
<14>[   75.666353][ T1@C6] 
<14>[   75.666371][ T1@C6] fsck.f2fs: Info: CKPT version = 58df68c4
<14>[   75.666371][ T1@C6] 
<14>[   75.666389][ T1@C6] fsck.f2fs: Info: version timestamp cur: 7760, prev: 64
<14>[   75.666389][ T1@C6] 
<14>[   75.666408][ T1@C6] fsck.f2fs: Info: checkpoint state = c1 :  nat_bits crc unmount
<14>[   75.666408][ T1@C6] 
<14>[   75.666426][ T1@C6] fsck.f2fs: Info: No error was reported
<14>[   75.666426][ T1@C6] 
<14>[   75.782496][ T1@C6] e2fsck: e2fsck 1.46.6 (1-Feb-2023)
<14>[   75.782496][ T1@C6] 
<14>[   75.782546][ T1@C6] e2fsck: productinfo: clean, 33/2560 files, 1427/2560 blocks
<14>[   75.782546][ T1@C6] 
<5>[   75.849979][T91@C5] audit: type=1400 audit(1754537715.203:215): avc:  denied  { getattr } for  pid=3578 comm="fsck.f2fs" path="/sys/devices/platform/soc/soc:ap-apb/201d0000.sdio/mmc_host/mmc0/mmc0:0001/block/mmcblk0/mmcblk0p51/partition" dev="sysfs" ino=38704 scontext=u:r:fsck:s0 tcontext=u:object_r:sysfs:s0 tclass=file permissive=0
<14>[   75.864082][ T1@C7] fsck.f2fs: Info: Automatic fix mode enabled.
<14>[   75.864082][ T1@C7] 
<14>[   75.864126][ T1@C7] fsck.f2fs: 	Info: can't find /sys, assuming normal block device
<14>[   75.864126][ T1@C7] 
<14>[   75.864138][ T1@C7] fsck.f2fs: Info: not exist /proc/version!
<14>[   75.864138][ T1@C7] 
<14>[   75.864151][ T1@C7] fsck.f2fs: Info: MKFS version
<14>[   75.864151][ T1@C7] 
<14>[   75.864164][ T1@C7] fsck.f2fs:   "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.864164][ T1@C7] 
<14>[   75.864177][ T1@C7] fsck.f2fs: Info: FSCK version
<14>[   75.864177][ T1@C7] 
<14>[   75.864189][ T1@C7] fsck.f2fs:   from "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.864189][ T1@C7] 
<14>[   75.864202][ T1@C7] fsck.f2fs:     to "5.15.149-android13-8-00205-g14df487ff1ea-dirty"
<14>[   75.864202][ T1@C7] 
<14>[   75.864216][ T1@C7] fsck.f2fs: Info: superblock features = 499 :  encrypt extra_attr project_quota quota verity
<14>[   75.864216][ T1@C7] 
<14>[   75.864229][ T1@C7] fsck.f2fs: Info: superblock encrypt level = 0, salt = 00000000000000000000000000000000
<14>[   75.864229][ T1@C7] 
<14>[   75.864240][ T1@C7] fsck.f2fs: Info: Segments per section = 1
<14>[   75.864240][ T1@C7] 
<14>[   75.864252][ T1@C7] fsck.f2fs: Info: Sections per zone = 1
<14>[   75.864252][ T1@C7] 
<14>[   75.864264][ T1@C7] fsck.f2fs: Info: total FS sectors = 131072 (64 MB)
<14>[   75.864264][ T1@C7] 
<14>[   75.864276][ T1@C7] fsck.f2fs: cp_addr: 200, pre_version:0x6ebf7a47 ubc:0x1400, vbc:0x2a
<14>[   75.864276][ T1@C7] 
<14>[   75.864288][ T1@C7] fsck.f2fs: cp_addr: 205, cur_version:0x6ebf7a47 ubc:0x1400, vbc:0x2a
<14>[   75.864288][ T1@C7] 
<14>[   75.864299][ T1@C7] fsck.f2fs: cp_start_blk_no:0x200,cp1:0xb4000072cc43f030,cp1_version:0x6ebf7a47
<14>[   75.864299][ T1@C7] 
<14>[   75.864311][ T1@C7] fsck.f2fs: cp_addr: 400, pre_version:0x6ebf7a48 ubc:0x1400, vbc:0x2a
<14>[   75.864311][ T1@C7] 
<14>[   75.864323][ T1@C7] fsck.f2fs: cp_addr: 405, cur_version:0x6ebf7a48 ubc:0x1400, vbc:0x2a
<14>[   75.864323][ T1@C7] 
<14>[   75.864335][ T1@C7] fsck.f2fs: cp_start_blk_no:0x400,cp2:0xb4000072cc440040,cp2_version:0x6ebf7a48
<14>[   75.864335][ T1@C7] 
<14>[   75.864346][ T1@C7] fsck.f2fs: Info: CKPT version = 6ebf7a48
<14>[   75.864346][ T1@C7] 
<14>[   75.864358][ T1@C7] fsck.f2fs: Info: version timestamp cur: 7776, prev: 0
<14>[   75.864358][ T1@C7] 
<14>[   75.864370][ T1@C7] fsck.f2fs: [update_superblock: 901] Info: Done to update superblock
<14>[   75.864370][ T1@C7] 
<14>[   75.864381][ T1@C7] fsck.f2fs: Info: checkpoint state = 1c5 :  trimmed nat_bits crc compacted_summary unmount
<14>[   75.864381][ T1@C7] 
<14>[   75.864393][ T1@C7] fsck.f2fs: Info: No error was reported
<14>[   75.864393][ T1@C7] 
<14>[   75.864866][ T1@C7] init: Resume reboot monitor thread after fsck
<14>[   75.865186][ T1@C7] init: sync() after umount...
<14>[   75.865956][T3524@C2] init: shutdown_timeout_timespec.tv_sec: 370
<14>[   75.866332][ T1@C7] init: sync() after umount took1ms
<11>[   75.866387][ T1@C7] init: umount timeout  umount_end_time12537umount_start_time5319
<3>[   75.866616][ T1@C7] shutdown_detect_check: shutdown_detect_timeout: umount timeout
<12>[   75.967188][ T1@C7] init: powerctl_shutdown_time_ms:12638:0
<14>[   75.967889][T3524@C2] init: remaining_shutdown_time: 295
<3>[   75.968389][ T1@C7] shutdown_detect_check: shutdown_detect_phase: shutdown  current phase systemcall
<14>[   75.968462][ T1@C7] init: Reboot ending, jumping to kernel
<3>[   75.968617][ T1@C7] failed to open '/dev/block/by-name/sd_klog':-2!


