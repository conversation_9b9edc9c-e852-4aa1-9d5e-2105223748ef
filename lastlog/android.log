

 root  08-07 11:33:36.940   718   718 D SurfaceFlinger: Setting power mode 0 on display 4619827259835644672
M000001  08-07 11:33:36.941 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
  bin  08-07 11:33:36.941 27811 27861 I <PERSON><PERSON>:background: [1356] rcb.h(44): DFS: Listener added for afdt@12966c7
  sys  08-07 11:33:36.941 27797 27797 I AtlasPixelTipsContentPr: enter
M000004  08-07 11:33:36.955  1237  4344 D ConnectivityService: requestNetwork for uid/pid:10143/27811 activeRequest: null callbackRequest: 558 [NetworkRequest [ REQUEST id=559, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]] callback flags: 0 order: 2147483647 isUidTracked: false declaredMethods: ALL
M000005  08-07 11:33:36.958 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M000006  08-07 11:33:36.962  1237  1426 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=559, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]
M000007  08-07 11:33:36.963  1237  1426 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=559, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]
M000008  08-07 11:33:36.964  1237  1426 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=559, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]
M000009  08-07 11:33:36.964  1237  1439 D ConnectivityService: NetReassign [559 : null → 101] [c 1] [a 1] [i 6]
M00000A  08-07 11:33:36.966 27797 27797 I RevelioTipsContentProvi: enter
M00000B  08-07 11:33:36.967 27797 27797 I TidepodsRevelioTipsCont: enter
M00000C  08-07 11:33:36.969 27811 27870 I Finsky:background: [1361] vpw.a(119): IQ: Internet connection lost.
M00000D  08-07 11:33:36.974 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M00000E  08-07 11:33:36.980   703  1096 I AudioFlinger: acquireWakeLock_l() AudioOut_1D status 0
M00000F  08-07 11:33:36.980   703  1096 D AudioFlinger: ro.audio.silent is ignored since no output device is set
M000010  08-07 11:33:36.987 27811 27861 I Finsky:background: [1356] vlg.a(833): IV2: found recoverable installs: []
M000011  08-07 11:33:36.991  1237  1237 D UNIPNP: SystemListener: Notification Posted data:[mEventName=unievent_notificaion_post, mData=10244, mTimeStamp=1754537616990, mBundle=Bundle[{isClearAble=false, uid=10244, packageName=com.github.kr328.clash}]]
M000012  08-07 11:33:36.994 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M000013  08-07 11:33:36.995   611   611 I mali_gralloc: allocate: id=263000002db, halFormat=RGBA8888, maliFormat=RGBA8888, afbc=SPLIT, usage=GPU_RW|HWC|BACKBUF, size=720x1600(4680704), stride=720, name=[BBQ]ShutdownAnimation#928#0(BLAST Consumer)0, costTime=1ms146us885
M000014  08-07 11:33:36.996 27564 27621 D mali_gralloc: register: id=263000002db, handle=0xb400007cfd46c410, importpid=27564
M000015  08-07 11:33:36.999   718  1311 D mali_gralloc: register: id=263000002db, handle=0xb400007956476af0, importpid=718
M000016  08-07 11:33:37.000 27811 27869 I Finsky:background: [1360] vqj.call(98): IQ: Pruning inactive install requests
M000017  08-07 11:33:37.015 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M000018  08-07 11:33:37.019 27811 27869 I Finsky:background: [1360] agrh.<init>(49): Resetting scheduler db
M000019  08-07 11:33:37.026 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M00001A  08-07 11:33:37.039  1554  1554 D VRI[NotificationShade]: performtraversals: skip draw, reason=view_not_visible
M00001B  08-07 11:33:37.046 27564 27621 D mali_gralloc: register: id=263000002dc, handle=0xb400007cfd46cd10, importpid=27564
M00001C  08-07 11:33:37.049   718  1311 D mali_gralloc: register: id=263000002dc, handle=0xb40000795647a570, importpid=718
M00001D  08-07 11:33:37.061 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M00001E  08-07 11:33:37.071  4052  4052 I wpa_supplicant: p2p-dev-wlan0: CTRL-EVENT-DSCP-POLICY clear_all
M00001F  08-07 11:33:37.071  4052  4052 I wpa_supplicant: Deinit sm=0xb400007289657b90
M000020  08-07 11:33:37.072 27811 27853 I Finsky:background: [1349] rrq.accept(58): SCH: Scheduling phonesky job Id: 3-6, CT: 1754532381111, Constraints: [{ L: 0, D: 86400000, C: CHARGING_REQUIRED, I: IDLE_REQUIRED, N: NET_UNMETERED, B: BATTERY_ANY }]
M000021  08-07 11:33:37.075 27811 27853 I Finsky:background: [1349] rrq.accept(58): SCH: Scheduling phonesky job Id: 3-7, CT: 1754532381111, Constraints: [{ L: 0, D: 86400000, C: CHARGING_REQUIRED, I: IDLE_REQUIRED, N: NET_UNMETERED, B: BATTERY_ANY }]
M000022  08-07 11:33:37.078 27811 27853 I Finsky:background: [1349] rrq.accept(58): SCH: Scheduling phonesky job Id: 47-15, CT: 1754474313002, Constraints: [{ L: 0, D: 13699099, C: CHARGING_NONE, I: IDLE_REQUIRED, N: NET_NONE, B: BATTERY_ANY }]
M000023  08-07 11:33:37.092 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M000024  08-07 11:33:37.103  4052  4052 I wpa_supplicant: p2p-dev-wlan0: CTRL-EVENT-DSCP-POLICY clear_all
M000025  08-07 11:33:37.107 27811 27857 D nativeloader: Load /product/priv-app/Phonesky/Phonesky.apk!/lib/arm64-v8a/libcronet.133.0.6876.3.so using ns product-clns-7 from class loader (caller=/product/priv-app/Phonesky/Phonesky.apk): ok
M000026  08-07 11:33:37.111  4052  4052 I wpa_supplicant: nl80211: deinit ifname=p2p-dev-wlan0 disabled_11b_rates=0
M000027  08-07 11:33:37.124   874   874 D NuPlayerDriver: reset(0xe9680510) at state 6
M000028  08-07 11:33:37.133   676   697 D PowerHAL: PowerSessionManager::enableSystemTopAppBoost!!
M000029  08-07 11:33:37.136 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M00002A  08-07 11:33:37.143  4052  4052 I wpa_supplicant: p2p-dev-wlan0: CTRL-EVENT-TERMINATING 
M00002B  08-07 11:33:37.156  4052  4052 I wpa_supplicant: wlan0: CTRL-EVENT-DSCP-POLICY clear_all
M00002C  08-07 11:33:37.163 27811 27853 I Finsky:background: [1349] nat.apply(290): SCH: Scheduling 0 system job(s)
M00002D  08-07 11:33:37.170   613   712 I SPRDHWComposer: Excessive delay in drm_blank(): 229ms, dpms_value:3
M00002E  08-07 11:33:37.171   718   718 D SurfaceFlinger: Finished setting power mode 0 on display 4619827259835644672
M00002F  08-07 11:33:37.172   718   718 I SurfaceFlinger: updating visibility visibleForInput:0, Snapshot{TraversalPath{.id = 929}[BBQ] ShutdownAnimation#928#929 isVisible=0 {layer not reachable from root} changes=Content | Visibility | Buffer layerStack=0 geomLayerBounds={0,0,1600,720} geomLayerTransform={tx=0,ty=0}}
M000030  08-07 11:33:37.177  4052  4052 I BpBinder: onLastStrongRef automatically unlinking death recipients: android.hardware.wifi.supplicant.ISupplicantStaNetworkCallback
M000031  08-07 11:33:37.179  1237  1625 I InputDispatcher: request window cea021f com.android.launcher3/com.android.searchlauncher.SearchLauncher NO_WINDOW
M000032  08-07 11:33:37.184   874  1224 D BufferPoolAccessor2.0: evictor expired: 1, evicted: 1
M000033  08-07 11:33:37.197  4052  4052 I wpa_supplicant: Deinit sm=0xb400007289654550
M000034  08-07 11:33:37.210  4052  4052 I wpa_supplicant: wlan0: CTRL-EVENT-DSCP-POLICY clear_all
M000035  08-07 11:33:37.210  4052  4052 I wpa_supplicant: nl80211: deinit ifname=wlan0 disabled_11b_rates=0
M000036  08-07 11:33:37.211 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M000037  08-07 11:33:37.218 27811 27880 I Finsky:background: [1366] afff.apply(152): SCH: Canceling job 3-6
M000038  08-07 11:33:37.219  4052  4052 I BpBinder: onLastStrongRef automatically unlinking death recipients: android.hardware.wifi.supplicant.ISupplicantStaIfaceCallback
M000039  08-07 11:33:37.219  4052  4052 I wpa_supplicant: wlan0: CTRL-EVENT-TERMINATING 
B00003A  09-30 06:27:56.569   874  4864 ? Netd    : NetlinkEvent: LinkDown, interface: wlan0
M00003B  08-07 11:33:37.226  1670  4123 D IpClient/wlan0: interfaceLinkStateChanged: ifindex 28 down
M00003C  08-07 11:33:37.228  1237  1275 D NetworkAdapter: interfaceLinkStateChanged iface: wlan0 up: false
M00003D  08-07 11:33:37.232   643   664 D vendor.sprd.hardware.aprd-service: socket_name=wcnd, cfd=8
M00003E  08-07 11:33:37.233   726   904 D ENGPC:  : [tid:0]timesync: refnotify socket disconnect
M00003E  08-07 11:33:37.233   726   904 D ENGPC:  : 
M00003F  08-07 11:33:37.242   545   617 E Netd    : NetlinkEvent: InterfaceRemoved, interface: vowifi_tun1
M000040  08-07 11:33:37.252   643   664 D vendor.sprd.hardware.aprd-service: socket_name=wcnd, cfd=-1
M000041  08-07 11:33:37.255   545   633 I netd    : interfaceGetCfg(wlan0) -> {InterfaceConfigurationParcel{ifName: wlan0, hwAddr: ca:4e:5e:10:77:31, ipv4Addr: 0.0.0.0, prefixLength: 0, flags: [up, broadcast, multicast]}} <0.30ms>
R000042  08-07 11:33:37.256   748   783 E RIL     : read exception from wcnd
M000043  08-07 11:33:37.257  1237 27867 D SurfaceControl: Excessive delay in setPowerMode()
M000044  08-07 11:33:37.258 27811 27855 I Finsky:background: [1351] rrq.accept(58): SCH: Scheduling phonesky job Id: 3-7, CT: 1754532381111, Constraints: [{ L: 0, D: 86400000, C: CHARGING_REQUIRED, I: IDLE_REQUIRED, N: NET_UNMETERED, B: BATTERY_ANY }]
S000045  08-07 11:33:37.260   885   885 I wificond: wificond is about to exit
M000046  08-07 11:33:37.276   599  1230 D audio_hw_stream: asm_out_write_check_and_standby_stream: app_scene(0: primary-playback) standby because underrun trigger
M000047  08-07 11:33:37.277   599  1230 D audio_hw_stream: asm_out_standby_l: enter: stream(0xf0483670) app_scene(0: primary-playback)
M000048  08-07 11:33:37.282   726   904 D ENGPC:  : [tid:0]try_connect: connect cp_time_sync_server socket error(Connection refused), try_num=1
M000048  08-07 11:33:37.282   726   904 D ENGPC:  : 
M000049  08-07 11:33:37.282   726   904 D ENGPC:  :  [tid:0]timesync: total_conn=1
M000049  08-07 11:33:37.282   726   904 D ENGPC:  : 
M00004A  08-07 11:33:37.301   653   653 D LIGHTS  : func:write_int, path=/sys/class/backlight/sprd_backlight/brightness, value=0
M00004B  08-07 11:33:37.313   653   653 D LIGHTS  : func:setLightKeyboard, on=0
M00004C  08-07 11:33:37.313   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/keyboard-backlight/brightness, value=0
M00004D  08-07 11:33:37.314   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/keyboard-backlight/brightness, fd = -1
M00004E  08-07 11:33:37.314   653   653 D LIGHTS  : func:setLightButtons, on=0
M00004F  08-07 11:33:37.314   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/keyboard-backlight/brightness, value=0
M000050  08-07 11:33:37.315   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/keyboard-backlight/brightness, fd = -1
M000051  08-07 11:33:37.315   653   653 D LIGHTS  : type:0,0|0|0, onTime:0, offtime:0
M000052  08-07 11:33:37.315   653   653 E LIGHTS  : set_led_state colorRGB=00000000, unknown mode 0
M000053  08-07 11:33:37.317   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/sc27xx:red/brightness, value=0
M000054  08-07 11:33:37.318   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/red/brightness, value=0
M000055  08-07 11:33:37.318   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/red/brightness, fd = -1
M000056  08-07 11:33:37.318   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/red_bl/on_off, value=0
M000057  08-07 11:33:37.318   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/red_bl/on_off, fd = -1
R000058  08-07 11:33:37.319   748   783 E RIL     : fail to connect wcnd socket, retrytime: 3
M000059  08-07 11:33:37.323   545   617 E Netd    : NetlinkEvent: InterfaceRemoved, interface: vowifi_tun0
M00005A  08-07 11:33:37.326   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/sc27xx:green/brightness, value=0
M00005B  08-07 11:33:37.327   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/green/brightness, value=0
M00005C  08-07 11:33:37.328   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/green/brightness, fd = -1
M00005D  08-07 11:33:37.328   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/green_bl/on_off, value=0
M00005E  08-07 11:33:37.328   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/green_bl/on_off, fd = -1
M00005F  08-07 11:33:37.328   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/sc27xx:blue/brightness, value=0
M000060  08-07 11:33:37.329   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/blue/brightness, value=0
M000061  08-07 11:33:37.329   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/blue/brightness, fd = -1
M000062  08-07 11:33:37.329   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/blue_bl/on_off, value=0
M000063  08-07 11:33:37.330   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/blue_bl/on_off, fd = -1
M000064  08-07 11:33:37.330   653   653 D LIGHTS  : type:0,0|0|0, onTime:0, offtime:0
M000065  08-07 11:33:37.330   653   653 E LIGHTS  : set_led_state colorRGB=00000000, unknown mode 0
M000066  08-07 11:33:37.331   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/sc27xx:red/brightness, value=0
M000067  08-07 11:33:37.332   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/red/brightness, value=0
M000068  08-07 11:33:37.332   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/red/brightness, fd = -1
M000069  08-07 11:33:37.332   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/red_bl/on_off, value=0
M00006A  08-07 11:33:37.333   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/red_bl/on_off, fd = -1
M00006B  08-07 11:33:37.334   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/sc27xx:green/brightness, value=0
M00006C  08-07 11:33:37.335   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/green/brightness, value=0
M00006D  08-07 11:33:37.335   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/green/brightness, fd = -1
M00006E  08-07 11:33:37.335   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/green_bl/on_off, value=0
M00006F  08-07 11:33:37.335   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/green_bl/on_off, fd = -1
M000070  08-07 11:33:37.336   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/sc27xx:blue/brightness, value=0
M000071  08-07 11:33:37.342  1554  1964 D LocalImageResolver: Couldn't use ImageDecoder for drawable, falling back to non-resized load.
M000072  08-07 11:33:37.344 11983 11983 D VRI[ActivityMain]: performtraversals: skip draw, reason=view_not_visible
M000073  08-07 11:33:37.354   718   718 E BpTransactionCompletedListener: Failed to transact (-32)
M000074  08-07 11:33:37.356   718   764 D mali_gralloc: unregister: id=263000002db, handle=0xb400007956476af0, base=0x0, importpid=718, clone_count=0
M000075  08-07 11:33:37.359   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/blue/brightness, value=0
M000076  08-07 11:33:37.359   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/blue/brightness, fd = -1
M000077  08-07 11:33:37.359   653   653 D LIGHTS  : func:write_int, path=/sys/class/leds/blue_bl/on_off, value=0
M000078  08-07 11:33:37.359   653   653 E LIGHTS  : func:write_int, failed to open /sys/class/leds/blue_bl/on_off, fd = -1
M000079  08-07 11:33:37.374  1995  2438 D [IMS_SRMI] SrmiServiceProxy: Srmi service has died.
M00007A  08-07 11:33:37.295  1237  1275 D NetworkAdapter: interfaceRemoved: vowifi_tun1
M00007B  08-07 11:33:37.376  1237  1275 D NetworkAdapter: interfaceRemoved: vowifi_tun0
M00007C  08-07 11:33:37.389  1237  1874 D UniActivityTaskManagerServiceExt: mUatms.mHomeProcess = ProcessRecord{f4125cc 1908:com.android.launcher3/u0a182}
M00007D  08-07 11:33:37.396  1237  4381 I InputDispatcher: request window cea021f com.android.launcher3/com.android.searchlauncher.SearchLauncher NO_WINDOW
M00007E  08-07 11:33:37.409   653   653 E LIGHTS  : func:setLightLedsAttention, unsupported light!
M00007F  08-07 11:33:37.418  1237  1268 D ConnectivityService: requestNetwork for uid/pid:10143/27811 activeRequest: null callbackRequest: 560 [NetworkRequest [ REQUEST id=561, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]] callback flags: 0 order: 2147483647 isUidTracked: false declaredMethods: ALL
M000080  08-07 11:33:37.438 27797 27797 W .android.dialer: Verification of java.lang.Object ery.b() took 176.261ms (18347.76 bytecodes/s) (64488B approximate peak alloc)
M000081  08-07 11:33:37.441  1237  1237 E WifiNl80211Manager: Wificond died!
S000082  08-07 11:33:37.446  1237  4369 W StorageManagerService: storaged died; reconnecting
M000083  08-07 11:33:37.450  1237  1439 D ConnectivityService: NetReassign [561 : null → 101] [c 0] [a 1] [i 31]
M000084  08-07 11:33:37.457   615   615 E android.hardware.health-service.example: unlinkToDeath: removed reference to death recipient but unlink failed: DEAD_OBJECT
M000085  08-07 11:33:37.472   615   615 E IPCThreadState: attemptIncStrongHandle(1): Not supported
E000086  08-07 11:33:37.488  1237  4369 I service_manager_slow: [42,storaged]
S000087  08-07 11:33:37.489  1237  4369 W StorageManagerService: storaged not found; trying again
M000088  08-07 11:33:37.514  1237  1426 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=561, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]
M000089  08-07 11:33:37.514  1237  1426 I WifiNative: wificond died. Cleaning up internal state.
M00008A  08-07 11:33:37.515  1237  1426 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=561, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]
M00008B  08-07 11:33:37.515  1237  1426 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=561, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10143 RequestorUid: 10143 RequestorPkg: com.android.vending UnderlyingNetworks: Null] ]
M00008C  08-07 11:33:37.519 27601 27892 I Finsky  : [1371] afcr.a(66): SM: There are no stale sessions to be pruned
S00008D  08-07 11:33:37.529  1237  1314 I AppsFilter: interaction: PackageSetting{978f306 com.android.microdroid.empty_payload/10219} -> PackageSetting{ee6347b com.google.android.apps.carrier.carrierwifi/10126} BLOCKED
M00008E  08-07 11:33:37.530  1237  1314 V PackageManager: Sending package changed: package=com.google.android.apps.carrier.carrierwifi components=[androidx.work.impl.background.systemalarm.RescheduleReceiver]
S00008F  08-07 11:33:37.531  1237  1314 V BroadcastQueue: Enqueuing BroadcastRecord{e6096d6 android.intent.action.PACKAGE_CHANGED/u0} ordered=false from uid 1000 pid 1237 for 43 receivers
S000090  08-07 11:33:37.589  1237  1268 W Installer: installd died; reconnecting
S000091  08-07 11:33:37.591  1237  1268 W Installer: installd not found; trying again
S000092  08-07 11:33:37.591  1237  1268 W Installer: installd died; reconnecting
S000093  08-07 11:33:37.600  1237  1268 W Installer: installd not found; trying again
S000094  08-07 11:33:37.600  1237  1268 W Installer: installd died; reconnecting
S000095  08-07 11:33:37.603  1554  1554 W Looper  : Slow delivery took 98ms main h=android.os.AsyncTask$InternalHandler c=null m=1
S000096  08-07 11:33:37.606  1237  1268 W Installer: installd not found; trying again
S000097  08-07 11:33:37.607  1237  1268 W Installer: installd died; reconnecting
S000098  08-07 11:33:37.610  1237  1268 W Installer: installd not found; trying again
M000099  08-07 11:33:37.633  1554  1554 D VRI[NotificationShade]: performtraversals: skip draw, reason=view_not_visible
S00009A  08-07 11:33:37.633  1554  1554 W Looper  : Drained
M00009B  08-07 11:33:37.659 27811 27857 I cn_CronetLibraryLoader: Cronet version: 133.0.6876.3, arch: aarch64
M00009C  08-07 11:33:37.700  1554  1964 D LocalImageResolver: Couldn't use ImageDecoder for drawable, falling back to non-resized load.
M00009D  08-07 11:33:37.720 27601 27601 I Finsky  : [2] aicp.a(9): Connecting InstallListener to SplitInstallService broadcaster...
M00009E  08-07 11:33:37.723   599  1230 D audio_hw_stream: asm_stop_output_stream: enter: app_scene(0: primary-playback)
M00009F  08-07 11:33:37.723   599  1230 D audio_hw_device_manager: dmgr_disable_audio_route: be-switch: 'codec_p'
M0000A0  08-07 11:33:37.724   599  1230 D audio_device_route: device_route_reset_path: reset path (2: be-switch: codec_p)
M0000A1  08-07 11:33:37.775   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_NORMAL_AP01_P_CODEC SWITCH] = 0
M0000A2  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_NORMAL_AP23_P_CODEC SWITCH] = 0
M0000A3  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FAST_P_CODEC SWITCH] = 0
M0000A4  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_OFFLOAD_CODEC SWITCH] = 0
M0000A5  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_VOICE_P_CODEC SWITCH] = 0
M0000A6  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_VOIP_P_CODEC SWITCH] = 0
M0000A7  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FM_CODEC SWITCH] = 0
M0000A8  08-07 11:33:37.776   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_LOOP_P_CODEC SWITCH] = 0
M0000A9  08-07 11:33:37.777   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FM_DSP_CODEC SWITCH] = 0
M0000AA  08-07 11:33:37.777   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_VOICE_PCM_P SWITCH] = 0
M0000AB  08-07 11:33:37.777   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FAST_P_SMART_AMP SWITCH] = 0
M0000AC  08-07 11:33:37.777   599  1230 D audio_hw_device_manager: dmgr_disable_audio_route: vbc-iis-mux: 'only_codec_p'
M0000AD  08-07 11:33:37.777   599  1230 D audio_device_route: device_route_reset_path: reset path (1: vbc-iis-mux: only_codec_p)
M0000AE  08-07 11:33:37.832   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_IIS_TX0_LRMOD_SEL] = LEFT_HIGH
M0000AF  08-07 11:33:37.832   599  1230 D audio_hw_device_manager: dmgr_disable_audio_route: vbc-iis: 'playback'
M0000B0  08-07 11:33:37.832   599  1230 D audio_device_route: device_route_reset_path: reset path (3: vbc-iis: playback)
M0000B1  08-07 11:33:37.832   599  1230 D audio_hw_device_manager: dmgr_disable_audio_device: audio-device: 'speaker'
R0000B2  08-07 11:33:37.653  1534  1559 D RILJ    : Service NETWORK has died. [PHONE0]
M0000B3  08-07 11:33:37.832   599  1230 D audio_device_route: device_route_reset_path: reset path (0: audio-device: speaker)
R0000B4  08-07 11:33:37.833  1534  1559 D RadioInteractor: extService EXT_VOICE has died. [SUB0]
M0000B5  08-07 11:33:37.833   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[Speaker1 Function] = 0
R0000B6  08-07 11:33:37.833  1534  1559 D RadioInteractor: extService EXT_VOICE has died. [SUB1]
M0000B7  08-07 11:33:37.833   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[Speaker Function] = 0
M0000B8  08-07 11:33:37.834   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[DAC LRCLK Select] = normal
M0000B9  08-07 11:33:37.834   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HPL Mixer DACLHPL Switch] = 0
M0000BA  08-07 11:33:37.835   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HPR Mixer DACRHPR Switch] = 0
M0000BB  08-07 11:33:37.835   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HPL EAR Sel] = EAR
M0000BC  08-07 11:33:37.835   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HP mix mode] = 1
M0000BD  08-07 11:33:37.835   599  1230 D audio_hw_platform: platform_set_audio_param: app_scene(0: primary-playback) state(2)
M0000BE  08-07 11:33:37.835   599  1230 D audio_param: param_set_cal_data: enter: app_type(0) dai_id(0) volume(0) set(false) app_type_status(0x1)
M0000BF  08-07 11:33:37.835   599  1230 D audio_param: param_set_cal_data: exit: app_type_status(0)
M0000C0  08-07 11:33:37.835   599  1230 D audio_hw_agdsp: agdsp_send_msg: cmd(0x34) param(0x69 0 0 0)
M0000C1  08-07 11:33:37.837   599  1230 D audio_hw_agdsp: agdsp_access_disable: access_ref_cnt(0)
M0000C2  08-07 11:33:37.837   599  1230 D audio_hw_stream: asm_stop_output_stream: exit: out_app_scene_ref_cnt(0)
M0000C3  08-07 11:33:37.837   599  1230 D audio_hw_stream: asm_start_output_stream: enter: app_scene(0: primary-playback) devices(0x2)
M0000C4  08-07 11:33:37.837   599  1230 D audio_hw_agdsp: agdsp_access_enable: access_ref_cnt(1)
M0000C5  08-07 11:33:37.837   599  1230 D audio_hw_agdsp: agdsp_send_msg: cmd(0x34) param(0x6a 0 0x1 0)
M0000C6  08-07 11:33:37.838   599  1230 D audio_hw_device_manager: dmgr_select_devices: app_scene(0: primary-playback)
M0000C7  08-07 11:33:37.838   599  1230 D audio_hw_device_manager: dmgr_select_devices: out_hal_device(2: speaker) in_hal_device(0: none)
M0000C8  08-07 11:33:37.838   599  1230 D audio_hw_device_manager: dmgr_enable_audio_device: audio-device: 'speaker'
M0000C9  08-07 11:33:37.838   599  1230 D audio_device_route: device_route_apply_path: apply path (0: audio-device: speaker)
M0000CA  08-07 11:33:37.838   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HP mix mode] = 0
M0000CB  08-07 11:33:37.839   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[DAC LRCLK Select] = invert
M0000CC  08-07 11:33:37.840   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HPL EAR Sel] = HPL
M0000CD  08-07 11:33:37.840   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HPL Mixer DACLHPL Switch] = 1
M0000CE  08-07 11:33:37.841   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[HPR Mixer DACRHPR Switch] = 1
M0000CF  08-07 11:33:37.841   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[Speaker Function] = 1
M0000D0  08-07 11:33:37.841   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[Speaker1 Function] = 1
M0000D1  08-07 11:33:37.842   599  1230 D audio_hw_device_manager: dmgr_enable_audio_route: vbc-iis: 'playback'
M0000D2  08-07 11:33:37.842   599  1230 D audio_device_route: device_route_apply_path: apply path (3: vbc-iis: playback)
M0000D3  08-07 11:33:37.842   599  1230 D audio_hw_device_manager: dmgr_enable_audio_route: vbc-iis-mux: 'only_codec_p'
M0000D4  08-07 11:33:37.842   599  1230 D audio_device_route: device_route_apply_path: apply path (1: vbc-iis-mux: only_codec_p)
M0000D5  08-07 11:33:37.866   658   678 D NetworkHALAIDL: AIDL Listen onDataAvailable !
M0000D6  08-07 11:33:37.884   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_IIS_TX0_WD_SEL] = WD_24BIT
M0000D7  08-07 11:33:37.898   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_IIS_TX1_WD_SEL] = WD_24BIT
M0000D8  08-07 11:33:37.899   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_IIS_TX0_LRMOD_SEL] = RIGHT_HIGH
M0000D9  08-07 11:33:37.901   658   677 E NetworkAIDL:  read socket failed **
M0000DA  08-07 11:33:37.889   728   728 E GNSS_DAEMON: gnss_signals_handler: enter, get a signal 15, and ignore it.
M0000DB  08-07 11:33:37.914   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_MUX_DAC0_IIS_PORT_SEL] = VBC_IIS_PORT_IIS0
M0000DC  08-07 11:33:37.921   658   678 E NetworkHALAIDL: read failed
R0000DD  08-07 11:33:37.922  1534  1534 D RILJ    : handleMessage: EVENT_AIDL_PROXY_DEAD cookie = 1, service = NETWORK, cookie = 1 [PHONE0]
M0000DE  08-07 11:33:37.923 27797 27797 W .android.dialer: Verification of void aaui.A(int, java.util.List, nhd, boolean) took 110.579ms (1636.84 bytecodes/s) (4808B approximate peak alloc)
R0000DF  08-07 11:33:37.923  1534  4716 D RILJ    : Service NETWORK has died. [PHONE1]
R0000E0  08-07 11:33:37.924  1534  4716 D RadioInteractor: extService EXT_DATA has died. [SUB1]
R0000E1  08-07 11:33:37.924  1534  4716 D RadioInteractor: extService EXT_SIM has died. [SUB1]
R0000E2  08-07 11:33:37.925  1534  4716 D RadioInteractor: extService EXT_MODEM has died. [SUB1]
R0000E3  08-07 11:33:37.925  1534  4716 D RILJ    : Service VOICE has died. [PHONE0]
R0000E4  08-07 11:33:37.925  1534  2735 D RadioInteractor: extService EXT_NETWORK has died. [SUB1]
M0000E5  08-07 11:33:37.925  1534  4716 E IPCThreadState: binder thread pool (15 threads) starved for 214 ms
M0000E6  08-07 11:33:37.925   599  1074 D audio_hw_monitor: monitor_thread_loop: reconnect count(0)
M0000E7  08-07 11:33:37.939  1237  1439 D ConnectivityService: NetReassign [no changes] [c 1] [a 1] [i 7]
M0000E8  08-07 11:33:37.949  1237  1268 W SupplicantStaIfaceHalAidlImpl: ISupplicant binder died. who=android.os.BinderProxy@3c5b729, service=android.os.BinderProxy@3c5b729
M0000E9  08-07 11:33:37.950  1237  1268 W SupplicantStaIfaceHalAidlImpl: Handle supplicant death
M0000EA  08-07 11:33:37.950  1237  1268 I SupplicantStaIfaceHalAidlImpl: Clearing internal state
S0000EB  08-07 11:33:37.951  1237  1268 W PowerStatsHALWrapper: PowerStats HAL died
M0000EC  08-07 11:33:37.952  1237  1426 I WifiNative: wpa_supplicant died. Cleaning up internal state.
M0000ED  08-07 11:33:37.957   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_MUX_DAC1_IIS_PORT_SEL] = VBC_IIS_PORT_IIS0
R0000EE  08-07 11:33:37.957  1995  2438 D ImsRIL  : Service IMS_AIDL_SERVICE has died. [SUB0]
R0000EF  08-07 11:33:37.959  1995  1995 D ImsRIL  : handleMessage: EVENT_AIDL_PROXY_DEAD cookie = 1, service = IMS_AIDL_SERVICE, cookie = 1 [SUB0]
R0000F0  08-07 11:33:37.962  1534  2735 D RadioInteractor: extService EXT_SIM has died. [SUB0]
M0000F1  08-07 11:33:37.962   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_MUX_IIS0_PORT_DO_SEL] = IIS_DO_VAL_DAC0
M0000F2  08-07 11:33:37.963   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_MUX_IIS2_PORT_DO_SEL] = IIS_DO_VAL_DAC2
M0000F3  08-07 11:33:37.963   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[VBC_MUX_IIS3_PORT_DO_SEL] = IIS_DO_VAL_DAC2
M0000F4  08-07 11:33:37.963   599  1230 D audio_hw_device_manager: dmgr_enable_audio_route: be-switch: 'codec_p'
M0000F5  08-07 11:33:37.963   599  1230 D audio_device_route: device_route_apply_path: apply path (2: be-switch: codec_p)
M0000F6  08-07 11:33:37.967   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[ag_iis0_ext_sel_v1] = top_dac_iis
M0000F7  08-07 11:33:37.968   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_NORMAL_AP01_P_CODEC SWITCH] = 1
M0000F8  08-07 11:33:37.969   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_NORMAL_AP23_P_CODEC SWITCH] = 1
M0000F9  08-07 11:33:37.969   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FAST_P_CODEC SWITCH] = 1
M0000FA  08-07 11:33:37.969   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_OFFLOAD_CODEC SWITCH] = 1
M0000FB  08-07 11:33:37.969   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_VOICE_P_CODEC SWITCH] = 1
M0000FC  08-07 11:33:37.970   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_VOIP_P_CODEC SWITCH] = 1
M0000FD  08-07 11:33:37.970   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FM_CODEC SWITCH] = 1
M0000FE  08-07 11:33:37.970   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_LOOP_P_CODEC SWITCH] = 1
M0000FF  08-07 11:33:37.970   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FM_DSP_CODEC SWITCH] = 1
M000100  08-07 11:33:37.985   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_VOICE_PCM_P SWITCH] = 1
M000101  08-07 11:33:37.985   599  1230 D audio_device_route: device_route_update_path: mixer_ctl[S_FAST_P_SMART_AMP SWITCH] = 1
M000102  08-07 11:33:37.986   599  1230 D audio_hw_stream: asm_start_output_stream: open pcm device card_id(0) dev_id(3)
M000103  08-07 11:33:37.995 17676 18208 W CameraBase: Camera service died!
M000104  08-07 11:33:37.999   599  1230 D audio_hw_platform: platform_set_audio_param: app_scene(0: primary-playback) state(0)
M000105  08-07 11:33:37.999   599  1230 D audio_param: param_set_cal_data: enter: app_type(0) dai_id(0) volume(0) set(true) app_type_status(0)
M000106  08-07 11:33:38.000   599  1230 D audio_param: param_set_cal_data: scene_id(124: Music\Handsfree\Playback)
M000107  08-07 11:33:38.000   599  1230 D audio_param: param_set_struct_config: enter: app_type(0) scene_id(124: Music\Handsfree\Playback) dai_id(0)
M000108  08-07 11:33:38.001   599  1230 D audio_param: param_set_struct_config: app_type(0) no need to config audio structure
M000109  08-07 11:33:38.001   599  1230 D audio_param: param_set_struct_config: exit
M00010A  08-07 11:33:38.002   599  1230 D audio_param: param_set_cvs_config: enter: app_type(0) scene_id(124: Music\Handsfree\Playback) dai_id(0) volume(0)
M00010B  08-07 11:33:38.002   599  1230 D audio_param: param_set_cvs_config: app_type(0) no need to config cvs
M00010C  08-07 11:33:38.002   599  1230 D audio_param: param_set_cvs_config: exit
M00010D  08-07 11:33:38.002   599  1230 D audio_param: param_set_vbc_config: enter: app_type(0) scene_id(124: Music\Handsfree\Playback) dai_id(0)
M00010E  08-07 11:33:38.002   599  1230 D audio_param: param_set_vbc_config: change scene_id_vbc_playback from (-1: none) to (124: Music\Handsfree\Playback)
M00010F  08-07 11:33:38.007   599  1230 D audio_param: param_apply_firmware_data: mixer_ctl[DSP VBC Profile Select] = [0x517c0000]
M000110  08-07 11:33:38.007   599  1230 D audio_param: param_set_vbc_config: exit
M000111  08-07 11:33:38.007   599  1230 D audio_param: param_set_digital_gain: enter: app_type(0) scene_id(124: Music\Handsfree\Playback) volume(0)
M000112  08-07 11:33:38.007   599  1230 D audio_param: param_set_digital_gain: change scene_id_dg_playback from (-1: none) to (124: Music\Handsfree\Playback)
R000113  08-07 11:33:38.019  1534  1907 D RILJ    : Service SIM has died. [PHONE0]
R000114  08-07 11:33:38.020  1534  1593 D RILJ    : Service MODEM has died. [PHONE1]
M000115  08-07 11:33:38.024   599  1230 D audio_param: param_set_digital_gain_ctl_value: mixer_ctl[VBC DAC0 DG Set] = [26 26]
M000116  08-07 11:33:38.024   599  1230 D audio_param: param_set_digital_gain: exit
M000117  08-07 11:33:38.024   599  1230 D audio_param: param_set_codec_config: enter: app_type(0) scene_id(124: Music\Handsfree\Playback) volume(0)
M000118  08-07 11:33:38.024   599  1230 D audio_param: param_set_codec_config: change scene_id_codec_out from (-1: none, volume 0) to (124: Music\Handsfree\Playback, volume 0)
M000119  08-07 11:33:38.024   599  1230 D audio_param: param_set_codec_config: output device(0x8)
E00011A  08-07 11:33:38.033 27811 27857 I service_manager_slow: [53,isub]
R00011B  08-07 11:33:38.037  1534  2763 D RILJ    : Service MESSAGING has died. [PHONE1]
M00011C  08-07 11:33:38.057   599  1230 D audio_param: param_set_codec_config: exit
M00011D  08-07 11:33:38.057   599  1230 D audio_param: param_set_cal_data: exit: app_type_status(0x1)
M00011E  08-07 11:33:38.058   599  1230 D audio_hw_stream: asm_start_output_stream: exit: out_app_scene_ref_cnt(1)
R00011F  08-07 11:33:38.073  1534  2115 D SatelliteController: EVENT_RADIO_STATE_CHANGED: radioState=2
R000120  08-07 11:33:38.080  1534  4716 D RadioInteractor: extService EXT_DATA has died. [SUB0]
M000121  08-07 11:33:38.086  2503  2533 W DrmManagerClientImpl(Native): DrmManager server died!
M000122  08-07 11:33:38.091  1237  1598 E HardwarePropertiesManagerService-JNI: Thermal HAL just died
M000123  08-07 11:33:38.099  1554  1754 D FlashlightController: dispatchAvailabilityChanged(false)
M000124  08-07 11:33:38.102 27797 27797 W .android.dialer: Verification of void sss.run() took 102.320ms (9636.40 bytecodes/s) (26960B approximate peak alloc)
M000125  08-07 11:33:38.133  1237  4369 W system_server: Long monitor contention with owner ActivityManager (1287) at void com.android.server.am.BroadcastQueueModernImpl.updateRunningList()(BroadcastQueueModernImpl.java:453) waiters=0 in void com.android.server.am.ActivityManagerService.setServiceForeground(android.content.ComponentName, android.os.IBinder, int, android.app.Notification, int, int) for 249ms
R000126  08-07 11:33:38.134  1534 24197 D RILJ    : Service DATA has died. [PHONE0]
S000127  08-07 11:33:38.171  1237  1268 E FaceProvider/default: HAL died
R000128  08-07 11:33:38.193  1534 24187 D RILJ    : Service MESSAGING has died. [PHONE0]
M000129  08-07 11:33:38.195  1237  1906 E PowerHALManager: Power HAL service has died
R00012A  08-07 11:33:38.195  1534  1559 D RadioInteractor: extService EXT_MESSAGING has died. [SUB0]
R00012B  08-07 11:33:38.206  1534 25751 D RILJ    : Service DATA has died. [PHONE1]
M00012C  08-07 11:33:38.440 11983 11983 I DEBUG   : begin to generate native report
M00012D  08-07 11:33:38.440 11983 11983 W DEBUG   : Dump for signal 15 is disabled in background!
M00012E  08-07 11:33:38.728   534   534 W statsd  : statsd terminated on receiving signal 15.
M00012F  08-07 11:33:38.826   534   577 I statsd  : StatsService::Terminating
M000130  08-07 11:33:38.826   534   577 I statsd  : Statsd skipping writing data to disk. Already wrote data in last 15 seconds
M000131  08-07 11:33:38.826   534   577 I statsd  : Statsd skipping writing active metrics to disk. Already wrote data in last 15 seconds
M000132  08-07 11:33:38.826   534   577 I statsd  : Statsd skipping writing metadata to disk. Already wrote data in last 15 seconds
M000133  08-07 11:33:39.117   986   986 I update_engine: [INFO:main.cc(81)] A/B Update Engine terminating with exit code 0
M000134  08-07 11:33:39.585   788   788 I adbd    : Remote process closed the socket (on MSG_PEEK)
M000135  08-07 11:33:40.634   788   788 I adbd    : Remote process closed the socket (on MSG_PEEK)
M000136  08-07 11:33:41.426   788   788 I adbd    : Remote process closed the socket (on MSG_PEEK)
S000137  08-07 11:33:41.583   329   406 D vold    : Aborting fuse connection entry /sys/fs/fuse/connections/46/abort
S000138  08-07 11:33:41.583   329   406 E vold    : Failed to open /sys/fs/fuse/connections/46/abort.tmp: Permission denied
S000139  08-07 11:33:41.583   329   406 W vold    : Failed to write to /sys/fs/fuse/connections/46/abort
S00013A  08-07 11:33:41.793   329   406 I vold    : Killing all processes referencing /storage/emulated/0
M00013B  08-07 11:33:41.898   788   788 I adbd    : Remote process closed the socket (on MSG_PEEK)
S00013C  08-07 11:33:41.983   329   406 I vold    : fuse-bpf is enabled because of property ro.fuse.bpf.is_running
S00013D  08-07 11:33:41.983   329   406 I vold    : Unmounting fuse path /mnt/user/0/emulated
S00013E  08-07 11:33:42.028   329   406 I vold    : Unmounting pass_through_path /mnt/pass_through/0/emulated
