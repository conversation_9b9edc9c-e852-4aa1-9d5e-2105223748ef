phoneinfo start


cat /proc/cmdline
Thu Aug  7 11:35:34 CST 2025
stack_depot_disable=on kasan.stacktrace=off kvm-arm.mode=protected cgroup_disable=pressure earlycon console=ttyS1,921600n8
       loop.max_part=7 loglevel=1 log_buf_len=1M kpti=0
       firmware_class.path=/odm/firmware,/vendor/firmware
       init=/init root=/dev/ram0 rw printk.devkmsg=on ftrace_dump_on_oops
       swiotlb=1 dummy_hcd.num=0 rcupdate.rcu_expedited=1 rcu_nocbs=0-7 kvm-arm.mode=none sprdlog.idx=0xf lcd_id=ID77666 lcd_name=lcd_ft8057m_sharp_dzx_mipi lcd_base=9caa8000 lcd_size=1600x720 logo_bpix=24  sysdump_magic=80001000 sysdump_re_flag=1  sprdboot.usbmux=0x0 modem=shutdown sprdboot.mode=normal ltemode=lcsfb rfboard.id=0 rfhw.id=32816 crystal=6 32k.less=1 marlin.clktype=1 power.from.extern=0 cpcmdline=end  bootconfig bootcause="Pbint triggered" pwroffcause="write pwroff" charge.shutdown_rtc_time=1754537700  charge.charge_cycle=1027  charge.basp=-1  charge.total_mah=5028000   sprdboot.wdten=e551  sprdboot.dswdten=enabled  sprdboot.dvfs_set=0x0,0,0 sprdboot.slot_suffix=_a FlashID=363447594E45 sprdboot.flash=emmc  ro.product.name=A601N 


cat /proc/version
Thu Aug  7 11:35:34 CST 2025
Linux version 5.15.149-android13-8-00205-g14df487ff1ea-dirty (build-user@build-host) (Android (8508608, based on r450784e) clang version 14.0.7 (https://android.googlesource.com/toolchain/llvm-project 4c603efb0cca074e9238af8b4106c30add4418f6), LLD 14.0.7) #1 SMP PREEMPT Mon Aug 4 10:57:16 UTC 2025


cat /proc/meminfo
Thu Aug  7 11:35:34 CST 2025
MemTotal:        3786728 kB
MemFree:         3249864 kB
MemAvailable:    3152760 kB
Buffers:           19168 kB
Cached:           209140 kB
SwapCached:            0 kB
Active:            25956 kB
Inactive:         261636 kB
Active(anon):        748 kB
Inactive(anon):    66900 kB
Active(file):      25208 kB
Inactive(file):   194736 kB
Unevictable:        4808 kB
Mlocked:            4808 kB
SwapTotal:             0 kB
SwapFree:              0 kB
Dirty:               120 kB
Writeback:             4 kB
AnonPages:         64492 kB
Mapped:            51152 kB
Shmem:              4560 kB
KReclaimable:      43884 kB
Slab:             133984 kB
SReclaimable:      43884 kB
SUnreclaim:        90100 kB
KernelStack:        7628 kB
ShadowCallStack:    1952 kB
PageTables:         7500 kB
NFS_Unstable:          0 kB
Bounce:                0 kB
WritebackTmp:          0 kB
CommitLimit:     1893364 kB
Committed_AS:     400072 kB
VmallocTotal:   259653632 kB
VmallocUsed:       47700 kB
VmallocChunk:          0 kB
Percpu:             8096 kB
AnonHugePages:         0 kB
ShmemHugePages:        0 kB
ShmemPmdMapped:        0 kB
FileHugePages:         0 kB
FilePmdMapped:         0 kB
CmaTotal:         344064 kB
CmaFree:          338992 kB


cat /proc/slabinfo
Thu Aug  7 11:35:35 CST 2025
slabinfo - version: 2.1
# name            <active_objs> <num_objs> <objsize> <objperslab> <pagesperslab> : tunables <limit> <batchcount> <sharedfactor> : slabdata <active_slabs> <num_slabs> <sharedavail>
f2fs_page_array_entry-254:50      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-254:50    312    312    208   39    2 : tunables    0    0    0 : slabdata      8      8      0
bio_fallback_crypt_ctx    714    714    120   34    1 : tunables    0    0    0 : slabdata     21     21      0
imsbr_flow           102    102     80   51    1 : tunables    0    0    0 : slabdata      2      2      0
f2fs_page_array_entry-259:41      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-259:41     39     39    208   39    2 : tunables    0    0    0 : slabdata      1      1      0
va-region-slab-mali0      0      0    168   24    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_page_array_entry-259:40      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-259:40    156    156    208   39    2 : tunables    0    0    0 : slabdata      4      4      0
ext4_groupinfo_4k    296    296    216   37    2 : tunables    0    0    0 : slabdata      8      8      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4     25     25    160   25    1 : tunables    0    0    0 : slabdata      1      1      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    100    100    160   25    1 : tunables    0    0    0 : slabdata      4      4      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    125    125    160   25    1 : tunables    0    0    0 : slabdata      5      5      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    100    100    160   25    1 : tunables    0    0    0 : slabdata      4      4      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    350    350    160   25    1 : tunables    0    0    0 : slabdata     14     14      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    300    300    160   25    1 : tunables    0    0    0 : slabdata     12     12      0
bio-373              144    144   3840    8    8 : tunables    0    0    0 : slabdata     18     18      0
bio-380              192    192   3840    8    8 : tunables    0    0    0 : slabdata     24     24      0
dm_verity_fec_buffers      8      8   4048    8    8 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer       26     26    152   26    1 : tunables    0    0    0 : slabdata      1      1      0
dm_bufio_buffer-4    850    850    160   25    1 : tunables    0    0    0 : slabdata     34     34      0
bio-208              768    768    256   32    2 : tunables    0    0    0 : slabdata     24     24      0
bio-272             1260   1260    384   21    2 : tunables    0    0    0 : slabdata     60     60      0
f2fs_page_array_entry-259:44      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_xattr_entry-259:44    195    195    208   39    2 : tunables    0    0    0 : slabdata      5      5      0
fsverity_info         32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
fscrypt_info         420    420    136   30    1 : tunables    0    0    0 : slabdata     14     14      0
wakeup_irq_node_cache      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
AF_VSOCK               0      0   1472   22    8 : tunables    0    0    0 : slabdata      0      0      0
lowpan-frags           0      0    200   20    1 : tunables    0    0    0 : slabdata      0      0      0
IEEE-802.15.4-MAC      0      0    960   34    8 : tunables    0    0    0 : slabdata      0      0      0
IEEE-802.15.4-RAW      0      0    896   36    8 : tunables    0    0    0 : slabdata      0      0      0
p9_req_t               0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
TIPC                  28     28   1152   28    8 : tunables    0    0    0 : slabdata      1      1      0
can_gw                 0      0    824   39    8 : tunables    0    0    0 : slabdata      0      0      0
can_receiver           0      0     80   51    1 : tunables    0    0    0 : slabdata      0      0      0
bridge_fdb_cache       0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
nf-frags               0      0    200   20    1 : tunables    0    0    0 : slabdata      0      0      0
xfrm6_tunnel_spi       0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
ip6-frags              0      0    200   20    1 : tunables    0    0    0 : slabdata      0      0      0
fib6_nodes            64     64    128   32    1 : tunables    0    0    0 : slabdata      2      2      0
ip6_dst_cache         32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
PINGv6                 0      0   1280   25    8 : tunables    0    0    0 : slabdata      0      0      0
RAWv6                125    125   1280   25    8 : tunables    0    0    0 : slabdata      5      5      0
UDPLITEv6              0      0   1408   23    8 : tunables    0    0    0 : slabdata      0      0      0
UDPv6                  0      0   1408   23    8 : tunables    0    0    0 : slabdata      0      0      0
tw_sock_TCPv6          0      0    256   32    2 : tunables    0    0    0 : slabdata      0      0      0
request_sock_TCPv6      0      0    320   25    2 : tunables    0    0    0 : slabdata      0      0      0
TCPv6                 12     12   2560   12    8 : tunables    0    0    0 : slabdata      1      1      0
xt_hashlimit           0      0    120   34    1 : tunables    0    0    0 : slabdata      0      0      0
nf_conncount_rb        0      0     96   42    1 : tunables    0    0    0 : slabdata      0      0      0
nf_conncount_tuple      0      0     72   56    1 : tunables    0    0    0 : slabdata      0      0      0
nf_conntrack_expect      0      0    232   35    2 : tunables    0    0    0 : slabdata      0      0      0
nf_conntrack           0      0    320   25    2 : tunables    0    0    0 : slabdata      0      0      0
fq_flow_cache          0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
ashmem_range_cache      0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
ashmem_area_cache      0      0    312   26    2 : tunables    0    0    0 : slabdata      0      0      0
dm_snap_pending_exception      0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
dm_exception           0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
kcopyd_job             0      0   3384    9    8 : tunables    0    0    0 : slabdata      0      0      0
io                   384    384     64   64    1 : tunables    0    0    0 : slabdata      6      6      0
dm_uevent              0      0   2888   11    8 : tunables    0    0    0 : slabdata      0      0      0
wg_peer                0      0   1760   18    8 : tunables    0    0    0 : slabdata      0      0      0
allowedips_node        0      0     72   56    1 : tunables    0    0    0 : slabdata      0      0      0
sd_ext_cdb           128    128     32  128    1 : tunables    0    0    0 : slabdata      1      1      0
bio-160              384    384    256   32    2 : tunables    0    0    0 : slabdata     12     12      0
sgpool-128             8      8   4096    8    8 : tunables    0    0    0 : slabdata      1      1      0
sgpool-64             16     16   2048   16    8 : tunables    0    0    0 : slabdata      1      1      0
sgpool-32             32     32   1024   32    8 : tunables    0    0    0 : slabdata      1      1      0
sgpool-16             32     32    512   32    4 : tunables    0    0    0 : slabdata      1      1      0
sgpool-8              32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
io_kiocb               0      0    256   32    2 : tunables    0    0    0 : slabdata      0      0      0
bfq_io_cq              0      0    232   35    2 : tunables    0    0    0 : slabdata      0      0      0
bfq_queue              0      0    560   29    4 : tunables    0    0    0 : slabdata      0      0      0
bio-248               32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
erofs_pcluster-256      0      0   2160   15    8 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-128      0      0   1136   28    8 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-64      0      0    624   26    4 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-16      0      0    240   34    2 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-4       0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
erofs_pcluster-1   21148  21148    120   34    1 : tunables    0    0    0 : slabdata    622    622      0
erofs_inode         1320   1320    808   20    4 : tunables    0    0    0 : slabdata     66     66      0
f2fs_casefolded_name     32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
f2fs_dic_entry         0      0    232   35    2 : tunables    0    0    0 : slabdata      0      0      0
f2fs_cic_entry         0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_bio_entry_slab   1020   1020     24  170    1 : tunables    0    0    0 : slabdata      6      6      0
f2fs_bio_iostat_ctx   1280   1280     32  128    1 : tunables    0    0    0 : slabdata     10     10      0
f2fs_bio_post_read_ctx    224    224     72   56    1 : tunables    0    0    0 : slabdata      4      4      0
f2fs_victim_entry      0      0     56   73    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_extent_node     320    320     64   64    1 : tunables    0    0    0 : slabdata      5      5      0
f2fs_extent_tree     255    255     80   51    1 : tunables    0    0    0 : slabdata      5      5      0
f2fs_fsync_inode_entry      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_inode_entry       0      0     24  170    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_ino_entry       850    850     24  170    1 : tunables    0    0    0 : slabdata      5      5      0
f2fs_revoke_entry      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
f2fs_sit_entry_set    340    340     24  170    1 : tunables    0    0    0 : slabdata      2      2      0
f2fs_discard_cmd      72     72    112   36    1 : tunables    0    0    0 : slabdata      2      2      0
f2fs_discard_entry     92     92     88   46    1 : tunables    0    0    0 : slabdata      2      2      0
f2fs_fsync_node_entry   1024   1024     32  128    1 : tunables    0    0    0 : slabdata      8      8      0
f2fs_nat_entry_set    612    612     40  102    1 : tunables    0    0    0 : slabdata      6      6      0
f2fs_free_nid      11390  11390     24  170    1 : tunables    0    0    0 : slabdata     67     67      0
f2fs_nat_entry      1152   1152     32  128    1 : tunables    0    0    0 : slabdata      9      9      0
f2fs_inode_cache     575    575   1424   23    8 : tunables    0    0    0 : slabdata     25     25      0
ovl_aio_req            0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
ovl_inode              0      0    864   37    8 : tunables    0    0    0 : slabdata      0      0      0
fuse_bpf_aio_req       0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
fuse_request           0      0    152   26    1 : tunables    0    0    0 : slabdata      0      0      0
fuse_inode             0      0   1024   32    8 : tunables    0    0    0 : slabdata      0      0      0
exfat_inode_cache      0      0    984   33    8 : tunables    0    0    0 : slabdata      0      0      0
exfat_cache            0      0     40  102    1 : tunables    0    0    0 : slabdata      0      0      0
fat_inode_cache        0      0    920   35    8 : tunables    0    0    0 : slabdata      0      0      0
fat_cache              0      0     40  102    1 : tunables    0    0    0 : slabdata      0      0      0
jbd2_transaction_s     32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
jbd2_inode             0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
jbd2_journal_handle    219    219     56   73    1 : tunables    0    0    0 : slabdata      3      3      0
jbd2_journal_head     34     34    120   34    1 : tunables    0    0    0 : slabdata      1      1      0
jbd2_revoke_table_s    512    512     16  256    1 : tunables    0    0    0 : slabdata      2      2      0
jbd2_revoke_record_s      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_fc_dentry_update      0      0     80   51    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_inode_cache     621    621   1376   23    8 : tunables    0    0    0 : slabdata     27     27      0
ext4_free_data         0      0     56   73    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_allocation_context      0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_prealloc_space      0      0    104   39    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_system_zone     816    816     40  102    1 : tunables    0    0    0 : slabdata      8      8      0
ext4_io_end_vec        0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_io_end            0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_bio_post_read_ctx    128    128     64   64    1 : tunables    0    0    0 : slabdata      2      2      0
ext4_pending_reservation      0      0     32  128    1 : tunables    0    0    0 : slabdata      0      0      0
ext4_extent_status   1122   1122     40  102    1 : tunables    0    0    0 : slabdata     11     11      0
mbcache                0      0     56   73    1 : tunables    0    0    0 : slabdata      0      0      0
kioctx                 0      0    640   25    4 : tunables    0    0    0 : slabdata      0      0      0
aio_kiocb              0      0    192   21    1 : tunables    0    0    0 : slabdata      0      0      0
userfaultfd_ctx_cache     21     21    192   21    1 : tunables    0    0    0 : slabdata      1      1      0
dio                    0      0    640   25    4 : tunables    0    0    0 : slabdata      0      0      0
fasync_cache           0      0     48   85    1 : tunables    0    0    0 : slabdata      0      0      0
audit_tree_mark        0      0     80   51    1 : tunables    0    0    0 : slabdata      0      0      0
posix_timers_cache     31     31    264   31    2 : tunables    0    0    0 : slabdata      1      1      0
UNIX-STREAM          208    208   1216   26    8 : tunables    0    0    0 : slabdata      8      8      0
UNIX                 208    208   1216   26    8 : tunables    0    0    0 : slabdata      8      8      0
ip4-frags              0      0    216   37    2 : tunables    0    0    0 : slabdata      0      0      0
UDP-Lite               0      0   1216   26    8 : tunables    0    0    0 : slabdata      0      0      0
tcp_bind_bucket       32     32    128   32    1 : tunables    0    0    0 : slabdata      1      1      0
inet_peer_cache        0      0    192   21    1 : tunables    0    0    0 : slabdata      0      0      0
xfrm_dst_cache         0      0    320   25    2 : tunables    0    0    0 : slabdata      0      0      0
xfrm_state             0      0    768   21    4 : tunables    0    0    0 : slabdata      0      0      0
ip_fib_trie          170    170     48   85    1 : tunables    0    0    0 : slabdata      2      2      0
ip_fib_alias         146    146     56   73    1 : tunables    0    0    0 : slabdata      2      2      0
ip_dst_cache           0      0    192   21    1 : tunables    0    0    0 : slabdata      0      0      0
PING                   0      0   1088   30    8 : tunables    0    0    0 : slabdata      0      0      0
RAW                  240    240   1088   30    8 : tunables    0    0    0 : slabdata      8      8      0
UDP                   78     78   1216   26    8 : tunables    0    0    0 : slabdata      3      3      0
tw_sock_TCP            0      0    256   32    2 : tunables    0    0    0 : slabdata      0      0      0
request_sock_TCP       0      0    320   25    2 : tunables    0    0    0 : slabdata      0      0      0
TCP                   26     26   2368   13    8 : tunables    0    0    0 : slabdata      2      2      0
dquot                160    160    256   32    2 : tunables    0    0    0 : slabdata      5      5      0
bio-280               42     42    384   21    2 : tunables    0    0    0 : slabdata      2      2      0
ep_head             2048   2048     16  256    1 : tunables    0    0    0 : slabdata      8      8      0
eventpoll_pwq        512    512     64   64    1 : tunables    0    0    0 : slabdata      8      8      0
eventpoll_epi        256    256    128   32    1 : tunables    0    0    0 : slabdata      8      8      0
inotify_inode_mark     51     51     80   51    1 : tunables    0    0    0 : slabdata      1      1      0
dax_cache            170    170    960   34    8 : tunables    0    0    0 : slabdata      5      5      0
bio_crypt_ctx        918    918     40  102    1 : tunables    0    0    0 : slabdata      9      9      0
request_queue        154    154   2200   14    8 : tunables    0    0    0 : slabdata     11     11      0
blkdev_ioc           312    312    104   39    1 : tunables    0    0    0 : slabdata      8      8      0
bio-224              928    928    256   32    2 : tunables    0    0    0 : slabdata     29     29      0
biovec-max           440    440   4096    8    8 : tunables    0    0    0 : slabdata     55     55      0
biovec-128             0      0   2048   16    8 : tunables    0    0    0 : slabdata      0      0      0
biovec-64            256    256   1024   32    8 : tunables    0    0    0 : slabdata      8      8      0
biovec-16            256    256    256   32    2 : tunables    0    0    0 : slabdata      8      8      0
khugepaged_mm_slot      0      0    112   36    1 : tunables    0    0    0 : slabdata      0      0      0
uid_cache            147    147    192   21    1 : tunables    0    0    0 : slabdata      7      7      0
iommu_iova             0      0     64   64    1 : tunables    0    0    0 : slabdata      0      0      0
dmaengine-unmap-2     64     64     64   64    1 : tunables    0    0    0 : slabdata      1      1      0
audit_buffer         510    510     24  170    1 : tunables    0    0    0 : slabdata      3      3      0
sock_inode_cache     442    442    960   34    8 : tunables    0    0    0 : slabdata     13     13      0
skbuff_ext_cache       0      0    128   32    1 : tunables    0    0    0 : slabdata      0      0      0
skbuff_fclone_cache      0      0    512   32    4 : tunables    0    0    0 : slabdata      0      0      0
skbuff_head_cache    736    736    256   32    2 : tunables    0    0    0 : slabdata     23     23      0
configfs_dir_cache    368    368     88   46    1 : tunables    0    0    0 : slabdata      8      8      0
file_lock_cache      264    264    248   33    2 : tunables    0    0    0 : slabdata      8      8      0
file_lock_ctx        146    146     56   73    1 : tunables    0    0    0 : slabdata      2      2      0
fsnotify_mark_connector    128    128     32  128    1 : tunables    0    0    0 : slabdata      1      1      0
buffer_head         1014   1014    104   39    1 : tunables    0    0    0 : slabdata     26     26      0
taskstats            115    115    352   23    2 : tunables    0    0    0 : slabdata      5      5      0
proc_dir_entry      1281   1281    192   21    1 : tunables    0    0    0 : slabdata     61     61      0
pde_opener           816    816     40  102    1 : tunables    0    0    0 : slabdata      8      8      0
proc_inode_cache    2028   2028    832   39    8 : tunables    0    0    0 : slabdata     52     52      0
seq_file             240    240    136   30    1 : tunables    0    0    0 : slabdata      8      8      0
sigqueue             357    357     80   51    1 : tunables    0    0    0 : slabdata      7      7      0
bdev_cache           238    238   1920   17    8 : tunables    0    0    0 : slabdata     14     14      0
shmem_inode_cache   1924   1924    864   37    8 : tunables    0    0    0 : slabdata     52     52      0
kernfs_iattrs_cache   1104   1104     88   46    1 : tunables    0    0    0 : slabdata     24     24      0
kernfs_node_cache  63270  63270    136   30    1 : tunables    0    0    0 : slabdata   2109   2109      0
mnt_cache            273    273    384   21    2 : tunables    0    0    0 : slabdata     13     13      0
filp                3296   3475    320   25    2 : tunables    0    0    0 : slabdata    139    139      0
inode_cache        29862  29862    760   21    4 : tunables    0    0    0 : slabdata   1422   1422      0
dentry             39195  39195    208   39    2 : tunables    0    0    0 : slabdata   1005   1005      0
names_cache           64     64   4096    8    8 : tunables    0    0    0 : slabdata      8      8      0
net_namespace          0      0   3968    8    8 : tunables    0    0    0 : slabdata      0      0      0
hashtab_node        4420   4420     24  170    1 : tunables    0    0    0 : slabdata     26     26      0
ebitmap_node        6464   6464     64   64    1 : tunables    0    0    0 : slabdata    101    101      0
avtab_extended_perms    918    918     40  102    1 : tunables    0    0    0 : slabdata      9      9      0
avtab_node         56440  56440     24  170    1 : tunables    0    0    0 : slabdata    332    332      0
avc_xperms_data      896    896     32  128    1 : tunables    0    0    0 : slabdata      7      7      0
avc_xperms_decision_node    595    595     48   85    1 : tunables    0    0    0 : slabdata      7      7      0
avc_xperms_node     1898   1898     56   73    1 : tunables    0    0    0 : slabdata     26     26      0
avc_node            4592   4592     72   56    1 : tunables    0    0    0 : slabdata     82     82      0
iint_cache             0      0    144   28    1 : tunables    0    0    0 : slabdata      0      0      0
lsm_inode_cache    37011  37011     56   73    1 : tunables    0    0    0 : slabdata    507    507      0
lsm_file_cache      3584   3584     16  256    1 : tunables    0    0    0 : slabdata     14     14      0
key_jar               64     64    256   32    2 : tunables    0    0    0 : slabdata      2      2      0
uts_namespace          0      0    432   37    4 : tunables    0    0    0 : slabdata      0      0      0
nsproxy              280    280     72   56    1 : tunables    0    0    0 : slabdata      5      5      0
vm_area_struct     15971  16660    232   35    2 : tunables    0    0    0 : slabdata    476    476      0
fs_cache             512    512     64   64    1 : tunables    0    0    0 : slabdata      8      8      0
files_cache          184    184    704   23    4 : tunables    0    0    0 : slabdata      8      8      0
signal_cache         560    560   1152   28    8 : tunables    0    0    0 : slabdata     20     20      0
sighand_cache        495    495   2112   15    8 : tunables    0    0    0 : slabdata     33     33      0
task_struct          528    528   4736    6    8 : tunables    0    0    0 : slabdata     88     88      0
cred_jar            2616   2667    192   21    1 : tunables    0    0    0 : slabdata    127    127      0
anon_vma_chain     10624  10624     64   64    1 : tunables    0    0    0 : slabdata    166    166      0
anon_vma            5760   5760    128   32    1 : tunables    0    0    0 : slabdata    180    180      0
pid                  608    608    128   32    1 : tunables    0    0    0 : slabdata     19     19      0
perf_event            26     26   1248   26    8 : tunables    0    0    0 : slabdata      1      1      0
trace_event_file    1518   1518     88   46    1 : tunables    0    0    0 : slabdata     33     33      0
ftrace_event_field   4760   4760     48   85    1 : tunables    0    0    0 : slabdata     56     56      0
pool_workqueue       256    256    256   32    2 : tunables    0    0    0 : slabdata      8      8      0
radix_tree_node     8708   8708    584   28    4 : tunables    0    0    0 : slabdata    311    311      0
task_group            32     32    512   32    4 : tunables    0    0    0 : slabdata      1      1      0
mm_struct            256    256   1024   32    8 : tunables    0    0    0 : slabdata      8      8      0
vmap_area           2688   2688     64   64    1 : tunables    0    0    0 : slabdata     42     42      0
kmalloc-rcl-8k         0      0   8192    4    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-4k         0      0   4096    8    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-2k         0      0   2048   16    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-1k         0      0   1024   32    8 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-512        0      0    512   32    4 : tunables    0    0    0 : slabdata      0      0      0
kmalloc-rcl-256       32     32    256   32    2 : tunables    0    0    0 : slabdata      1      1      0
kmalloc-rcl-128     1344   1344    128   32    1 : tunables    0    0    0 : slabdata     42     42      0
kmalloc-cg-8k         36     36   8192    4    8 : tunables    0    0    0 : slabdata      9      9      0
kmalloc-cg-4k         88     88   4096    8    8 : tunables    0    0    0 : slabdata     11     11      0
kmalloc-cg-2k         80     80   2048   16    8 : tunables    0    0    0 : slabdata      5      5      0
kmalloc-cg-1k        480    480   1024   32    8 : tunables    0    0    0 : slabdata     15     15      0
kmalloc-cg-512       416    416    512   32    4 : tunables    0    0    0 : slabdata     13     13      0
kmalloc-cg-256       288    288    256   32    2 : tunables    0    0    0 : slabdata      9      9      0
kmalloc-cg-128      3872   3872    128   32    1 : tunables    0    0    0 : slabdata    121    121      0
kmalloc-8k           213    228   8192    4    8 : tunables    0    0    0 : slabdata     57     57      0
kmalloc-4k          1304   1304   4096    8    8 : tunables    0    0    0 : slabdata    163    163      0
kmalloc-2k          1552   1552   2048   16    8 : tunables    0    0    0 : slabdata     97     97      0
kmalloc-1k          2176   2176   1024   32    8 : tunables    0    0    0 : slabdata     68     68      0
kmalloc-512         3968   3968    512   32    4 : tunables    0    0    0 : slabdata    124    124      0
kmalloc-256        49856  49856    256   32    2 : tunables    0    0    0 : slabdata   1558   1558      0
kmalloc-128       100736 100736    128   32    1 : tunables    0    0    0 : slabdata   3148   3148      0
kmem_cache_node      448    448    128   32    1 : tunables    0    0    0 : slabdata     14     14      0
kmem_cache           378    378    384   21    2 : tunables    0    0    0 : slabdata     18     18      0


cat /proc/mounts
Thu Aug  7 11:35:35 CST 2025
/dev/block/dm-7 / erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
tmpfs /dev tmpfs rw,seclabel,nosuid,relatime,mode=755 0 0
devpts /dev/pts devpts rw,seclabel,relatime,mode=600,ptmxmode=000 0 0
proc /proc proc rw,relatime,gid=3009,hidepid=invisible 0 0
sysfs /sys sysfs rw,seclabel,relatime 0 0
selinuxfs /sys/fs/selinux selinuxfs rw,relatime 0 0
tmpfs /mnt tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755,gid=1000 0 0
tmpfs /mnt/installer tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755,gid=1000 0 0
tmpfs /mnt/androidwritable tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755,gid=1000 0 0
/dev/block/mmcblk0p51 /metadata f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal 0 0
/dev/block/dm-8 /system_ext erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-9 /vendor erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-10 /odm erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-11 /product erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-12 /vendor_dlkm erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
/dev/block/dm-13 /system_dlkm erofs ro,seclabel,relatime,user_xattr,acl,cache_strategy=readaround 0 0
tmpfs /apex tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
tmpfs /bootstrap-apex tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
tmpfs /linkerconfig tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
none /dev/blkio cgroup rw,nosuid,nodev,noexec,relatime,blkio 0 0
none /sys/fs/cgroup cgroup2 rw,nosuid,nodev,noexec,relatime,memory_recursiveprot 0 0
none /dev/cpuctl cgroup rw,nosuid,nodev,noexec,relatime,cpu 0 0
none /dev/cpuset cgroup rw,nosuid,nodev,noexec,relatime,cpuset,noprefix,release_agent=/sbin/cpuset_release_agent 0 0
none /dev/memcg cgroup rw,nosuid,nodev,noexec,relatime,memory 0 0
/dev/block/loop2 /bootstrap-apex/com.android.tzdata@351400020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop1 /bootstrap-apex/com.android.i18n@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop0 /bootstrap-apex/com.android.vndk.v33@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop3 /bootstrap-apex/com.android.runtime@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop0 /bootstrap-apex/com.android.vndk.v33 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop1 /bootstrap-apex/com.android.i18n ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop2 /bootstrap-apex/com.android.tzdata ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop3 /bootstrap-apex/com.android.runtime ext4 ro,dirsync,seclabel,nodev,noatime 0 0
tmpfs /bootstrap-apex/apex-info-list.xml tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
tracefs /sys/kernel/tracing tracefs rw,seclabel,relatime,gid=3012 0 0
tmpfs /tmp tmpfs rw,seclabel,relatime 0 0
debugfs /sys/kernel/debug debugfs rw,seclabel,relatime 0 0
none /config configfs rw,nosuid,nodev,noexec,relatime 0 0
binder /dev/binderfs binder rw,relatime,max=1048576,stats=global 0 0
none /sys/fs/fuse/connections fusectl rw,relatime 0 0
bpf /sys/fs/bpf bpf rw,nosuid,nodev,noexec,relatime 0 0
pstore /sys/fs/pstore pstore rw,seclabel,nosuid,nodev,noexec,relatime 0 0
adb /dev/usb-ffs/adb functionfs rw,relatime 0 0
mtp /dev/usb-ffs/mtp functionfs rw,relatime 0 0
ptp /dev/usb-ffs/ptp functionfs rw,relatime 0 0
tracefs /sys/kernel/debug/tracing tracefs rw,seclabel,relatime,gid=3012 0 0
/dev/block/mmcblk0p1 /mnt/vendor ext4 rw,seclabel,nosuid,nodev,noatime,noauto_da_alloc 0 0
/dev/block/mmcblk0p47 /cache f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal 0 0
/dev/block/mmcblk0p48 /blackbox f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,alloc_mode=reuse,checkpoint_merge,fsync_mode=posix,discard_unit=block,memory=normal 0 0
tmpfs /storage tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755,gid=1000 0 0
/dev/block/dm-50 /data f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
tmpfs /linkerconfig tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
/dev/block/loop4 /apex/com.android.btservices@352090000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop6 /apex/com.android.wifi@351610000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop5 /apex/com.android.media.swcodec@351504000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop4 /apex/com.android.btservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop7 /apex/com.android.adbd@351010000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop5 /apex/com.android.media.swcodec ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop6 /apex/com.android.wifi ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop7 /apex/com.android.adbd ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop8 /apex/com.android.nfcservices@352090000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop8 /apex/com.android.nfcservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop9 /apex/com.android.virt@2 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop11 /apex/com.android.tzdata@351400020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop11 /apex/com.android.tzdata ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop9 /apex/com.android.virt ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop10 /apex/com.android.conscrypt@351412000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop10 /apex/com.android.conscrypt ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop12 /apex/com.android.i18n@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop12 /apex/com.android.i18n ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop13 /apex/com.android.runtime@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop13 /apex/com.android.runtime ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop14 /apex/com.android.cellbroadcast@351511000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop15 /apex/com.android.neuralnetworks@351010040 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop14 /apex/com.android.cellbroadcast ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop15 /apex/com.android.neuralnetworks ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop16 /apex/com.android.appsearch@351412000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop16 /apex/com.android.appsearch ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop17 /apex/com.android.profiling@352090000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop17 /apex/com.android.profiling ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop19 /apex/com.android.configinfrastructure@351010000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop18 /apex/com.android.apex.cts.shim@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop19 /apex/com.android.configinfrastructure ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop20 /apex/com.android.devicelock@342410000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop20 /apex/com.android.devicelock ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop21 /apex/com.android.adservices@351537040 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop21 /apex/com.android.adservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop18 /apex/com.android.apex.cts.shim ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop22 /apex/com.android.healthfitness@351511060 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop23 /apex/com.android.ipsec@351410000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop22 /apex/com.android.healthfitness ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop23 /apex/com.android.ipsec ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop24 /apex/com.android.rkpd@351310000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop25 /apex/com.android.media@351504000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop25 /apex/com.android.media ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop26 /apex/com.android.os.statsd@351610000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop24 /apex/com.android.rkpd ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop26 /apex/com.android.os.statsd ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop27 /apex/com.android.vndk.v33@1 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop27 /apex/com.android.vndk.v33 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop28 /apex/com.android.extservices@351538083 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop29 /apex/com.android.uwb@351310040 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop28 /apex/com.android.extservices ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop30 /apex/com.android.mediaprovider@351613160 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop29 /apex/com.android.uwb ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop30 /apex/com.android.mediaprovider ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop31 /apex/com.android.art@351610080 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop31 /apex/com.android.art ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop32 /apex/com.android.permission@351610020 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop32 /apex/com.android.permission ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop34 /apex/com.android.sdkext@351415000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop33 /apex/com.android.compos@2 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop33 /apex/com.android.compos ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop34 /apex/com.android.sdkext ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop35 /apex/com.google.mainline.primary.libs@351165000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop36 /apex/com.android.resolv@351510000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop36 /apex/com.android.resolv ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop37 /apex/com.android.ondevicepersonalization@351541000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop38 /apex/com.android.tethering@351510080 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop37 /apex/com.android.ondevicepersonalization ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop39 /apex/com.android.scheduling@351010000 ext4 ro,dirsync,seclabel,nodev,noatime 0 0
tmpfs /data_mirror tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=700,gid=1000 0 0
/dev/block/loop39 /apex/com.android.scheduling ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/loop38 /apex/com.android.tethering ext4 ro,dirsync,seclabel,nodev,noatime 0 0
/dev/block/dm-50 /data_mirror/data_ce/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/data_de/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/misc_ce/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/misc_de/null f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/storage_area f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/cur_profiles f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data_mirror/ref_profiles f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
tmpfs /apex/apex-info-list.xml tmpfs rw,seclabel,nosuid,nodev,noexec,relatime,mode=755 0 0
/dev/block/dm-50 /data_mirror/data_ce/null/0 f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0
/dev/block/dm-50 /data/user/0 f2fs rw,lazytime,seclabel,nosuid,nodev,noatime,background_gc=on,discard,no_heap,user_xattr,inline_xattr,acl,inline_data,inline_dentry,flush_merge,extent_cache,mode=adaptive,active_logs=6,reserve_root=32768,resuid=0,resgid=1065,inlinecrypt,alloc_mode=default,checkpoint_merge,fsync_mode=nobarrier,atgc,discard_unit=block,memory=normal 0 0


cat /proc/diskstats
Thu Aug  7 11:35:35 CST 2025
   1       0 ram0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       1 ram1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       2 ram2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       3 ram3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       4 ram4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       5 ram5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       6 ram6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       7 ram7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       8 ram8 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1       9 ram9 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      10 ram10 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      11 ram11 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      12 ram12 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      13 ram13 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      14 ram14 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   1      15 ram15 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
   7       0 loop0 118 0 11416 396 0 0 0 0 0 340 396 0 0 0 0 0 0
   7       8 loop1 40 0 3440 98 0 0 0 0 0 104 98 0 0 0 0 0 0
   7      16 loop2 13 0 304 11 0 0 0 0 0 28 11 0 0 0 0 0 0
   7      24 loop3 75 0 9184 147 0 0 0 0 0 260 147 0 0 0 0 0 0
   7      32 loop4 17 0 624 21 0 0 0 0 0 60 21 0 0 0 0 0 0
   7      40 loop5 14 0 112 13 0 0 0 0 0 24 13 0 0 0 0 0 0
   7      48 loop6 17 0 440 19 0 0 0 0 0 64 19 0 0 0 0 0 0
   7      56 loop7 9 0 72 6 0 0 0 0 0 28 6 0 0 0 0 0 0
   7      64 loop8 12 0 248 8 0 0 0 0 0 36 8 0 0 0 0 0 0
   7      72 loop9 19 0 328 14 0 0 0 0 0 56 14 0 0 0 0 0 0
   7      80 loop10 23 0 336 30 0 0 0 0 0 40 30 0 0 0 0 0 0
   7      88 loop11 14 0 312 2 0 0 0 0 0 24 2 0 0 0 0 0 0
   7      96 loop12 35 0 2192 40 0 0 0 0 0 92 40 0 0 0 0 0 0
   7     104 loop13 75 0 9096 24 0 0 0 0 0 132 24 0 0 0 0 0 0
   7     112 loop14 7 0 56 2 0 0 0 0 0 16 2 0 0 0 0 0 0
   7     120 loop15 5 0 40 1 0 0 0 0 0 12 1 0 0 0 0 0 0
 259       0 pmem0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       0 mmcblk0 4995 1556 262964 2771 126 1 1584 48 0 5848 2827 0 0 0 0 10 7
 179       1 mmcblk0p1 22 2 404 7 12 1 64 8 0 64 16 0 0 0 0 0 0
 179       2 mmcblk0p2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       3 mmcblk0p3 4 0 104 6 0 0 0 0 0 20 6 0 0 0 0 0 0
 179       4 mmcblk0p4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       5 mmcblk0p5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       6 mmcblk0p6 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179       7 mmcblk0p7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       1 mmcblk0p8 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       2 mmcblk0p9 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       3 mmcblk0p10 96 0 32768 110 0 0 0 0 0 332 110 0 0 0 0 0 0
 259       4 mmcblk0p11 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       5 mmcblk0p12 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       6 mmcblk0p13 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       7 mmcblk0p14 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       8 mmcblk0p15 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259       9 mmcblk0p16 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      10 mmcblk0p17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      11 mmcblk0p18 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      12 mmcblk0p19 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      13 mmcblk0p20 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      14 mmcblk0p21 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      15 mmcblk0p22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      16 mmcblk0p23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      17 mmcblk0p24 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      18 mmcblk0p25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      19 mmcblk0p26 2 0 16 1 0 0 0 0 0 4 1 0 0 0 0 0 0
 259      20 mmcblk0p27 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      21 mmcblk0p28 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      22 mmcblk0p29 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      23 mmcblk0p30 2 0 16 0 0 0 0 0 0 8 0 0 0 0 0 0 0
 259      24 mmcblk0p31 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      25 mmcblk0p32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      26 mmcblk0p33 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      27 mmcblk0p34 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      28 mmcblk0p35 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      29 mmcblk0p36 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      30 mmcblk0p37 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      31 mmcblk0p38 2 0 16 0 0 0 0 0 0 8 0 0 0 0 0 0 0
 259      32 mmcblk0p39 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      33 mmcblk0p40 2 0 16 0 0 0 0 0 0 8 0 0 0 0 0 0 0
 259      34 mmcblk0p41 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      35 mmcblk0p42 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      36 mmcblk0p43 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      37 mmcblk0p44 2 0 16 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      38 mmcblk0p45 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      39 mmcblk0p46 4076 1101 201520 2208 0 0 0 0 0 5080 2208 0 0 0 0 0 0
 259      40 mmcblk0p47 38 12 688 18 0 0 0 0 0 60 18 0 0 0 0 0 0
 259      41 mmcblk0p48 83 16 8184 49 0 0 0 0 0 192 49 0 0 0 0 0 0
 259      42 mmcblk0p49 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      43 mmcblk0p50 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      44 mmcblk0p51 49 9 568 30 0 0 0 0 0 80 30 0 0 0 0 0 0
 259      45 mmcblk0p52 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      46 mmcblk0p53 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      47 mmcblk0p54 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      48 mmcblk0p55 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      49 mmcblk0p56 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      50 mmcblk0p57 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      51 mmcblk0p58 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      52 mmcblk0p59 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      53 mmcblk0p60 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      54 mmcblk0p61 1 0 256 0 0 0 0 0 0 4 0 0 0 0 0 0 0
 259      55 mmcblk0p62 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      56 mmcblk0p63 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      57 mmcblk0p64 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      58 mmcblk0p65 28 0 5032 20 0 0 0 0 0 56 20 0 0 0 0 0 0
 259      59 mmcblk0p66 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      60 mmcblk0p67 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      61 mmcblk0p68 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      62 mmcblk0p69 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      63 mmcblk0p70 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      64 mmcblk0p71 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      65 mmcblk0p72 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      66 mmcblk0p73 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 259      67 mmcblk0p74 565 416 11936 301 114 0 1520 40 0 904 341 0 0 0 0 0 0
 179       8 mmcblk0boot0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 179      16 mmcblk0boot1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254       0 dm-0 3480 0 142272 1708 0 0 0 0 0 4004 1708 0 0 0 0 0 0
 254       1 dm-1 578 0 18624 452 0 0 0 0 0 792 452 0 0 0 0 0 0
 254       2 dm-2 561 0 13264 676 0 0 0 0 0 636 676 0 0 0 0 0 0
 254       3 dm-3 193 0 4008 32 0 0 0 0 0 232 32 0 0 0 0 0 0
 254       4 dm-4 107 0 4032 144 0 0 0 0 0 100 144 0 0 0 0 0 0
 254       5 dm-5 251 0 19312 112 0 0 0 0 0 744 112 0 0 0 0 0 0
 254       6 dm-6 5 0 40 4 0 0 0 0 0 16 4 0 0 0 0 0 0
 254       7 dm-7 2718 0 136176 2444 0 0 0 0 0 4076 2444 0 0 0 0 0 0
 254       8 dm-8 420 0 17360 440 0 0 0 0 1 844 440 0 0 0 0 0 0
 254       9 dm-9 302 0 11192 336 0 0 0 0 1 684 336 0 0 0 0 0 0
 254      10 dm-10 37 0 3472 44 0 0 0 0 0 112 44 0 0 0 0 0 0
 254      11 dm-11 89 0 3176 64 0 0 0 0 0 240 64 0 0 0 0 0 0
 254      12 dm-12 177 0 18720 180 0 0 0 0 0 788 180 0 0 0 0 0 0
 254      13 dm-13 4 0 32 4 0 0 0 0 0 16 4 0 0 0 0 0 0
   7     128 loop16 16 0 432 16 0 0 0 0 0 56 16 0 0 0 0 0 0
   7     136 loop17 15 0 320 8 0 0 0 0 0 44 8 0 0 0 0 0 0
   7     144 loop18 13 0 128 6 0 0 0 0 0 20 6 0 0 0 0 0 0
   7     152 loop19 16 0 432 15 0 0 0 0 0 52 15 0 0 0 0 0 0
   7     160 loop20 15 0 328 9 0 0 0 0 0 44 9 0 0 0 0 0 0
   7     168 loop21 21 0 776 21 0 0 0 0 0 64 21 0 0 0 0 0 0
   7     176 loop22 17 0 440 21 0 0 0 0 0 60 21 0 0 0 0 0 0
   7     184 loop23 13 0 256 10 0 0 0 0 0 40 10 0 0 0 0 0 0
   7     192 loop24 12 0 144 6 0 0 0 0 0 32 6 0 0 0 0 0 0
   7     200 loop25 20 0 344 16 0 0 0 0 0 64 16 0 0 0 0 0 0
   7     208 loop26 40 0 3640 91 0 0 0 0 0 156 91 0 0 0 0 0 0
   7     216 loop27 195 0 13688 2962 0 0 0 0 3 464 2962 0 0 0 0 0 0
   7     224 loop28 8 0 64 4 0 0 0 0 0 28 4 0 0 0 0 0 0
   7     232 loop29 15 0 424 15 0 0 0 0 0 52 15 0 0 0 0 0 0
   7     240 loop30 21 0 592 16 0 0 0 0 0 44 16 0 0 0 0 0 0
   7     248 loop31 146 0 17712 375 0 0 0 0 0 540 375 0 0 0 0 0 0
   7     256 loop32 18 0 448 22 0 0 0 0 0 60 22 0 0 0 0 0 0
   7     264 loop33 15 0 144 8 0 0 0 0 0 40 8 0 0 0 0 0 0
   7     272 loop34 26 0 1416 26 0 0 0 0 0 92 26 0 0 0 0 0 0
   7     280 loop35 12 0 96 7 0 0 0 0 0 24 7 0 0 0 0 0 0
   7     288 loop36 44 0 7072 143 0 0 0 0 0 216 143 0 0 0 0 0 0
   7     296 loop37 16 0 280 11 0 0 0 0 0 52 11 0 0 0 0 0 0
   7     304 loop38 87 0 4312 109 0 0 0 0 0 224 109 0 0 0 0 0 0
   7     312 loop39 15 0 208 6 0 0 0 0 0 44 6 0 0 0 0 0 0
   7     320 loop40 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      14 dm-14 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      15 dm-15 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      16 dm-16 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      17 dm-17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      18 dm-18 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      19 dm-19 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      20 dm-20 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      21 dm-21 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      22 dm-22 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      23 dm-23 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      24 dm-24 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      25 dm-25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      26 dm-26 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      27 dm-27 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      28 dm-28 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      29 dm-29 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      30 dm-30 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      31 dm-31 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      32 dm-32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      33 dm-33 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      34 dm-34 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      35 dm-35 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      36 dm-36 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      37 dm-37 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      38 dm-38 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      39 dm-39 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      40 dm-40 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      41 dm-41 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      42 dm-42 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      43 dm-43 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      44 dm-44 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      45 dm-45 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      46 dm-46 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      47 dm-47 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      48 dm-48 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      49 dm-49 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0
 254      50 dm-50 978 0 11808 2400 114 0 1520 52 0 912 2452 0 0 0 0 0 0
 253       0 zram0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0


cat /proc/modules
Thu Aug  7 11:35:35 CST 2025
focaltech_touch_spi 212992 1 - Live 0x0000000000000000 (OE)
focaltech_side_fp 65536 0 - Live 0x0000000000000000 (OE)
kprobe_iomonitor 32768 0 - Live 0x0000000000000000 (E)
sla_core 28672 0 - Live 0x0000000000000000 (E)
unisoc_cachedump 16384 0 - Live 0x0000000000000000 (E)
unisoc_binder 32768 0 [permanent], Live 0x0000000000000000 (E)
unisoc_pnp 28672 0 - Live 0x0000000000000000 (E)
zram 65536 0 - Live 0x0000000000000000 (E)
zsmalloc 57344 1 zram, Live 0x0000000000000000 (E)
kts_sync 16384 0 - Live 0x0000000000000000 (E)
screen_onoff_check 16384 0 - Live 0x0000000000000000 (E)
sprd_coresight_apetb_main 20480 0 - Live 0x0000000000000000 (E)
sprd_coresight_apetb_ctrl 16384 1 sprd_coresight_apetb_main, Live 0x0000000000000000 (E)
sprd_coresight_etm4x 151552 1 sprd_coresight_apetb_ctrl, Live 0x0000000000000000 (E)
sprd_coresight_replicator 28672 0 - Live 0x0000000000000000 (E)
sprd_coresight_funnel 28672 0 - Live 0x0000000000000000 (E)
sprd_coresight_tmc 53248 1 sprd_coresight_apetb_ctrl, Live 0x0000000000000000 (E)
sprd_coresight 110592 5 sprd_coresight_apetb_main,sprd_coresight_etm4x,sprd_coresight_replicator,sprd_coresight_funnel,sprd_coresight_tmc, Live 0x0000000000000000 (E)
sprd_suspend_helper 16384 0 - Live 0x0000000000000000 (E)
ums9230_serdes 20480 0 - Live 0x0000000000000000 (E)
mipiserdes_base 28672 1 ums9230_serdes, Live 0x0000000000000000 (E)
unisoc_gnss_pmnotify_ctl 16384 0 - Live 0x0000000000000000 (OE)
unisoc_gnss_dbg 32768 0 - Live 0x0000000000000000 (OE)
unisoc_gnss_common_ctl_all 24576 1 unisoc_gnss_pmnotify_ctl, Live 0x0000000000000000 (OE)
sprd_fm 110592 0 - Live 0x0000000000000000 (OE)
sprdbt_tty 81920 0 - Live 0x0000000000000000 (OE)
sprd_compr_2stage_dma 57344 1 - Live 0x0000000000000000 (OE)
sprd_dmaengine_pcm 53248 1 - Live 0x0000000000000000 (OE)
snd_soc_sprd_vbc_fe 45056 1 [permanent], Live 0x0000000000000000 (OE)
snd_soc_sprd_vbc_v4 507904 2 snd_soc_sprd_vbc_fe,[permanent], Live 0x0000000000000000 (OE)
sprd_platform_pcm_routing 135168 1 - Live 0x0000000000000000 (OE)
snd_soc_sprd_pdm_r2p0 40960 0 - Live 0x0000000000000000 (OE)
snd_soc_sprd_dummy_codec 16384 0 - Live 0x0000000000000000 (OE)
snd_soc_sprd_codec_sc2730_power_dev 28672 0 [permanent], Live 0x0000000000000000 (OE)
snd_soc_sprd_codec_sc2730_power 36864 30 - Live 0x0000000000000000 (OE)
snd_soc_sprd_codec_sc2730 241664 1 [permanent], Live 0x0000000000000000 (OE)
snd_soc_sprd_card 98304 6 sprd_compr_2stage_dma,sprd_dmaengine_pcm,snd_soc_sprd_vbc_v4,snd_soc_sprd_pdm_r2p0,snd_soc_sprd_codec_sc2730_power,snd_soc_sprd_codec_sc2730,[permanent], Live 0x0000000000000000 (OE)
sprd_audcp_boot 16384 0 - Live 0x0000000000000000 (OE)
sprd_audcp_dvfs 45056 0 - Live 0x0000000000000000 (OE)
mcdt_hw_r2p0 40960 3 sprd_compr_2stage_dma,snd_soc_sprd_vbc_fe,snd_soc_sprd_vbc_v4, Live 0x0000000000000000 (OE)
audio_pipe 40960 0 - Live 0x0000000000000000 (OE)
sprd_apipe 36864 0 - Live 0x0000000000000000 (OE)
sprd_audio_usb_offload 24576 0 - Live 0x0000000000000000 (OE)
audio_dsp_dump 61440 0 - Live 0x0000000000000000 (OE)
audio_sipc 57344 7 sprd_compr_2stage_dma,snd_soc_sprd_vbc_v4,snd_soc_sprd_pdm_r2p0,sprd_audcp_boot,sprd_audcp_dvfs,audio_pipe,audio_dsp_dump, Live 0x0000000000000000 (OE)
audio_mem 36864 5 sprd_compr_2stage_dma,sprd_dmaengine_pcm,sprd_audcp_boot,audio_dsp_dump,audio_sipc, Live 0x0000000000000000 (OE)
agdsp_pd 32768 2 audio_dsp_dump,audio_sipc, Live 0x0000000000000000 (OE)
sprd_djtag 20480 0 - Live 0x0000000000000000 (E)
sprd_busmonitor 24576 0 - Live 0x0000000000000000 (E)
djtag_core 24576 2 sprd_djtag,sprd_busmonitor,[permanent], Live 0x0000000000000000 (E)
sprd_apb_busmonitor 28672 0 - Live 0x0000000000000000 (E)
sprd_cp_dvfs 32768 0 - Live 0x0000000000000000 (E)
dmc_mpu 36864 0 - Live 0x0000000000000000 (E)
musb_sprd 69632 0 - Live 0x0000000000000000 (E)
musb_hdrc 307200 2 sprd_audio_usb_offload,musb_sprd, Live 0x0000000000000000 (E)
sprd_pmic_wdt 20480 0 - Live 0x0000000000000000 (E)
ims_bridge 147456 0 - Live 0x0000000000000000 (E)
sprd_map 24576 0 - Live 0x0000000000000000 (E)
bq2597x_charger 40960 0 - Live 0x0000000000000000 (E)
sc27xx_fast_charger 20480 0 - Live 0x0000000000000000 (E)
sc27xx_pd 57344 0 - Live 0x0000000000000000 (E)
sprd_typec_displayport 24576 0 - Live 0x0000000000000000 (E)
sy65153_wireless_charger 36864 0 - Live 0x0000000000000000 (E)
sgm41516_charger 45056 0 - Live 0x0000000000000000 (E)
bq2560x_charger 53248 1 - Live 0x0000000000000000 (E)
sprd_uid 16384 0 - Live 0x0000000000000000 (E)
sc27xx_fuel_gauge 126976 0 - Live 0x0000000000000000 (E)
sprd_charger_manager 196608 3 bq2597x_charger,sy65153_wireless_charger,sc27xx_fuel_gauge, Live 0x0000000000000000 (E)
sprd_battery_info 20480 4 sgm41516_charger,bq2560x_charger,sc27xx_fuel_gauge,sprd_charger_manager, Live 0x0000000000000000 (E)
sc27xx_typec 40960 1 musb_sprd, Live 0x0000000000000000 (E)
phy_sprd_qogirl6 36864 3 - Live 0x0000000000000000 (E)
phy_sprd_commonphy 16384 2 musb_sprd,phy_sprd_qogirl6, Live 0x0000000000000000 (E)
sprd_tcpm 118784 3 sc27xx_pd,sprd_charger_manager,sc27xx_typec, Live 0x0000000000000000 (E)
sc27xx_vibra 16384 0 - Live 0x0000000000000000 (E)
sprd_virt_thm 16384 0 - Live 0x0000000000000000 (E)
thermal_generic_adc 16384 0 - Live 0x0000000000000000 (E)
sprd_wlan_combo 741376 0 - Live 0x0000000000000000 (OE)
unisoc_wcn_bsp 679936 4 unisoc_gnss_common_ctl_all,sprd_fm,sprdbt_tty,sprd_wlan_combo, Live 0x0000000000000000 (OE)
sprd_bcl 24576 0 - Live 0x0000000000000000 (E)
sprd_freq_limit 24576 0 - Live 0x0000000000000000 (E)
cpufreq_userspace 20480 0 - Live 0x0000000000000000 (E)
dmc_drv 28672 0 - Live 0x0000000000000000 (E)
sprd_ddr_dvfs 77824 0 - Live 0x0000000000000000 (E)
sc27xx_tsensor_thermal 16384 0 - Live 0x0000000000000000 (E)
sc27xx_poweroff 16384 0 - Live 0x0000000000000000 (E)
sc27xx_adc 57344 0 - Live 0x0000000000000000 (E)
pwm_sprd 20480 1 - Live 0x0000000000000000 (E)
spi_sprd 40960 0 - Live 0x0000000000000000 (E)
pinctrl_sprd_qogirl6 40960 0 - Live 0x0000000000000000 (E)
pinctrl_sprd 28672 1 pinctrl_sprd_qogirl6, Live 0x0000000000000000 (E)
ledtrig_pattern 24576 0 - Live 0x0000000000000000 (E)
leds_sc27xx_bltc 16384 0 - Live 0x0000000000000000 (E)
jpg 53248 0 - Live 0x0000000000000000 (OE)
vpu 90112 0 - Live 0x0000000000000000 (OE)
sprd_vsp_pw_domain 20480 0 - Live 0x0000000000000000 (OE)
mali_kbase 1466368 0 - Live 0x0000000000000000 (OE)
pinctrl 20480 0 - Live 0x0000000000000000 (E)
gpio 24576 0 - Live 0x0000000000000000 (E)
core 16384 2 pinctrl,gpio, Live 0x0000000000000000 (E)
extcon_usb_gpio 16384 0 - Live 0x0000000000000000 (E)
sprd_cpu_cooling 49152 0 - Live 0x0000000000000000 (E)
sprd_drm 1064960 1 focaltech_touch_spi, Live 0x0000000000000000 (OE)
unisoc_mm_reclaim 40960 1 sprd_drm, Live 0x0000000000000000 (E)
sprd_gsp 327680 1 sprd_drm, Live 0x0000000000000000 (OE)
unisoc_multi_control 20480 0 - Live 0x0000000000000000 (E)
mmdvfs 237568 0 - Live 0x0000000000000000 (OE)
apsys_dvfs 241664 2 vpu,sprd_drm, Live 0x0000000000000000 (E)
sprd_cpp 135168 0 - Live 0x0000000000000000 (OE)
sprd_camera 2125824 0 - Live 0x0000000000000000 (OE)
sprd_sensor 102400 1 sprd_camera, Live 0x0000000000000000 (OE)
sprd_dmabuf 40960 2 sprd_cpp,sprd_camera, Live 0x0000000000000000 (OE)
cma_heap 32768 0 [permanent], Live 0x0000000000000000 (E)
ion_ipc_trusty 20480 1 cma_heap, Live 0x0000000000000000 (E)
system_heap 28672 0 [permanent], Live 0x0000000000000000 (E)
unisoc_iommu 114688 7 jpg,vpu,sprd_drm,sprd_gsp,sprd_cpp,sprd_camera,system_heap, Live 0x0000000000000000 (E)
sprd_dma 36864 0 - Live 0x0000000000000000 (E)
unisoc_mm_emem 28672 3 zram,mali_kbase,system_heap, Live 0x0000000000000000 (E)
sprd_camsys_pw_domain 73728 5 jpg,mmdvfs,sprd_cpp,sprd_camera,sprd_sensor, Live 0x0000000000000000 (OE)
flash_ic_aw36515 28672 0 - Live 0x0000000000000000 (OE)
flash_ic_ocp81375 28672 0 - Live 0x0000000000000000 (OE)
flash_ic_ocp8137 24576 0 - Live 0x0000000000000000 (OE)
sprd_flash_drv 24576 4 sprd_camera,flash_ic_aw36515,flash_ic_ocp81375,flash_ic_ocp8137, Live 0x0000000000000000 (OE)
sprd_aphang 61440 0 - Live 0x0000000000000000 (E)
unisoc_last_kmsg 57344 1 sprd_aphang, Live 0x0000000000000000 (E)
sprd_usbpinmux 20480 2 musb_sprd,phy_sprd_qogirl6, Live 0x0000000000000000 (E)
sprd_bc1p2 28672 1 phy_sprd_qogirl6, Live 0x0000000000000000 (E)
cfg80211 1519616 1 sprd_wlan_combo, Live 0x0000000000000000 (E)
sprd_ptm 61440 0 - Live 0x0000000000000000 (E)
sprd_shm 28672 0 - Live 0x0000000000000000 (E)
trusty_virtio 28672 0 - Live 0x0000000000000000 (E)
trusty_ipc 57344 8 unisoc_wcn_bsp,sprd_drm,sprd_camera,ion_ipc_trusty, Live 0x0000000000000000 (E)
trusty_irq 28672 0 - Live 0x0000000000000000 (E)
trusty_log 36864 1 - Live 0x0000000000000000 (E)
trusty_pm 16384 0 - Live 0x0000000000000000 (E)
trusty 45056 8 mali_kbase,sprd_drm,sprd_gsp,sprd_shm,trusty_virtio,trusty_irq,trusty_log,trusty_pm, Live 0x0000000000000000 (E)
unisoc_userlog 20480 0 - Live 0x0000000000000000 (E)
tms_device_modules 61440 0 - Live 0x0000000000000000 (E)
sprd_trng 20480 0 - Live 0x0000000000000000 (E)
sprd_cache_print 20480 0 - Live 0x0000000000000000 (E)
sprd_usb_f_rndis 65536 2 - Live 0x0000000000000000 (E)
sprd_u_ether 49152 1 sprd_usb_f_rndis, Live 0x0000000000000000 (E)
sprd_usb_f_serial 24576 16 - Live 0x0000000000000000 (E)
sprd_u_serial 36864 1 sprd_usb_f_serial, Live 0x0000000000000000 (E)
sblock_bridge 20480 0 - Live 0x0000000000000000 (E)
sbuf_bridge 20480 0 - Live 0x0000000000000000 (E)
slog_bridge 28672 0 - Live 0x0000000000000000 (E)
sprd_iq 28672 0 - Live 0x0000000000000000 (E)
usb_f_vser 57344 4 slog_bridge,sprd_iq, Live 0x0000000000000000 (E)
sprd_cp_dump 28672 0 - Live 0x0000000000000000 (E)
sprd_modem_loader 45056 0 - Live 0x0000000000000000 (E)
seth 40960 0 - Live 0x0000000000000000 (E)
sipx 45056 1 seth, Live 0x0000000000000000 (E)
spool 28672 0 - Live 0x0000000000000000 (E)
spipe 24576 0 - Live 0x0000000000000000 (E)
sprd_sipc_virt_bus 16384 0 - Live 0x0000000000000000 (E)
sensorhub 61440 3 - Live 0x0000000000000000 (E)
sr_hwinfo 20480 5 focaltech_touch_spi,focaltech_side_fp,sprd_drm,tms_device_modules,sensorhub,[permanent], Live 0x0000000000000000 (E)
sipc_core 208896 19 sprd_fm,sprdbt_tty,agdsp_pd,sprd_cp_dvfs,sprd_pmic_wdt,ims_bridge,sprd_wlan_combo,unisoc_wcn_bsp,sprd_ddr_dvfs,sblock_bridge,sbuf_bridge,slog_bridge,sprd_iq,sprd_modem_loader,seth,sipx,spool,spipe,sensorhub, Live 0x0000000000000000 (E)
sprd_pdbg 57344 2 unisoc_wcn_bsp,sipc_core, Live 0x0000000000000000 (E)
unisoc_mailbox 40960 7 sipc_core,[permanent], Live 0x0000000000000000 (E)
sprd_power_manager 36864 3 sbuf_bridge,sprd_modem_loader,sipc_core, Live 0x0000000000000000 (E)
sprd_pmic_syscon 16384 0 - Live 0x0000000000000000 (E)
shutdown_detect 24576 0 [permanent], Live 0x0000000000000000 (E)
sdhci_sprd 180224 0 - Live 0x0000000000000000 (E)
mmc_hsq 20480 1 sdhci_sprd, Live 0x0000000000000000 (E)
mmc_swcq 81920 1 sdhci_sprd, Live 0x0000000000000000 (E)
gpio_pmic_eic_sprd 20480 10 - Live 0x0000000000000000 (E)
gpio_sprd 28672 22 - Live 0x0000000000000000 (E)
gpio_eic_sprd 32768 4 - Live 0x0000000000000000 (E)
sprd_cpufreq_v2 40960 0 [permanent], Live 0x0000000000000000 (E)
sprd_thermal_ctl 32768 1 mali_kbase, Live 0x0000000000000000 (E)
sprd_soc_thm 16384 0 - Live 0x0000000000000000 (E)
sprd_thermal 24576 0 - Live 0x0000000000000000 (E)
trusty_tui 24576 0 - Live 0x0000000000000000 (E)
sprd_7sreset 20480 0 - Live 0x0000000000000000 (E)
i2c_sprd_hw_v2 24576 0 - Live 0x0000000000000000 (E)
i2c_sprd 36864 0 - Live 0x0000000000000000 (E)
ufs_sprd 245760 0 - Live 0x0000000000000000 (E)
rpmb 45056 1 ufs_sprd, Live 0x0000000000000000 (E)
nvmem_sprd_efuse 20480 0 - Live 0x0000000000000000 (E)
nvmem_sprd_cache_efuse 16384 0 - Live 0x0000000000000000 (E)
sprd_lookat 20480 0 [permanent], Live 0x0000000000000000 (E)
nvmem_sc27xx_efuse 16384 0 - Live 0x0000000000000000 (E)
sprd_hwspinlock 20480 4 - Live 0x0000000000000000 (E)
sprd_soc_id 20480 3 mmdvfs,apsys_dvfs,ufs_sprd, Live 0x0000000000000000 (E)
sc2730_regulator 28672 7 - Live 0x0000000000000000 (E)
rtc_sc27xx 24576 1 - Live 0x0000000000000000 (E)
sprd_pmic_spi 24576 0 - Live 0x0000000000000000 (E)
spi_sprd_adi 32768 0 - Live 0x0000000000000000 (E)
unisoc_sched 270336 4 sprd_cpu_cooling,unisoc_mm_reclaim,[permanent], Live 0x0000000000000000 (E)
iolimit 28672 0 [permanent], Live 0x0000000000000000 (E)
ums9230_clk 159744 143 - Live 0x0000000000000000 (E)
clk_sprd 36864 1 ums9230_clk, Live 0x0000000000000000 (E)
sprd_time_sync_cp 16384 0 [permanent], Live 0x0000000000000000 (E)
sprd_time_sync 24576 1 sprd_time_sync_cp, Live 0x0000000000000000 (E)
sprd_systimer 20480 3 sensorhub,sprd_pdbg,sprd_time_sync,[permanent], Live 0x0000000000000000 (E)
sprd_sip_svc 28672 6 unisoc_cachedump,sprd_pmic_wdt,sprd_aphang,sprd_pdbg,sprd_cpufreq_v2,ufs_sprd,[permanent], Live 0x0000000000000000 (E)
sprd_wdt_fiq 28672 1 sprd_aphang, Live 0x0000000000000000 (E)
regmap_hook 20480 0 - Live 0x0000000000000000 (E)
timer_sprd 24576 0 [permanent], Live 0x0000000000000000 (E)
native_hang_monitor 45056 0 - Live 0x0000000000000000 (E)
sysdump 118784 15 unisoc_cachedump,dmc_drv,sprd_ddr_dvfs,sprd_aphang,unisoc_last_kmsg,sprd_cp_dump,sipc_core,shutdown_detect,ufs_sprd,sprd_pmic_spi,spi_sprd_adi,sprd_wdt_fiq,native_hang_monitor,[permanent], Live 0x0000000000000000 (E)
printk_cpuid 16384 0 - Live 0x0000000000000000 (E)


cat /proc/cpuinfo
Thu Aug  7 11:35:35 CST 2025
processor	: 0
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 1
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 2
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 3
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 4
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 5
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x2
CPU part	: 0xd05
CPU revision	: 0

processor	: 6
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x3
CPU part	: 0xd0a
CPU revision	: 1

processor	: 7
BogoMIPS	: 52.00
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp
CPU implementer	: 0x41
CPU architecture: 8
CPU variant	: 0x3
CPU part	: 0xd0a
CPU revision	: 1



getprop
Thu Aug  7 11:35:36 CST 2025
[aaudio.mmap_exclusive_policy]: [2]
[aaudio.mmap_policy]: [2]
[apex.all.ready]: [true]
[apr.flag.bootmode]: [1]
[bluetooth.device.class_of_device]: [90,2,12]
[bluetooth.device.default_name]: [SPRD_V_Common]
[bluetooth.profile.a2dp.source.enabled]: [true]
[bluetooth.profile.asha.central.enabled]: [true]
[bluetooth.profile.avrcp.target.enabled]: [true]
[bluetooth.profile.bas.client.enabled]: [true]
[bluetooth.profile.gatt.enabled]: [true]
[bluetooth.profile.hfp.ag.enabled]: [true]
[bluetooth.profile.hid.device.enabled]: [true]
[bluetooth.profile.hid.host.enabled]: [true]
[bluetooth.profile.map.server.enabled]: [true]
[bluetooth.profile.opp.enabled]: [true]
[bluetooth.profile.pan.nap.enabled]: [true]
[bluetooth.profile.pan.panu.enabled]: [true]
[bluetooth.profile.pbap.server.enabled]: [true]
[bootreceiver.enable]: [1]
[build.version.extensions.ad_services]: [16]
[build.version.extensions.r]: [16]
[build.version.extensions.s]: [16]
[build.version.extensions.t]: [16]
[build.version.extensions.u]: [16]
[build.version.extensions.v]: [16]
[camera.disable_zsl_mode]: [1]
[dalvik.vm.appimageformat]: [lz4]
[dalvik.vm.dex2oat-Xms]: [64m]
[dalvik.vm.dex2oat-Xmx]: [512m]
[dalvik.vm.dex2oat-cpu-set]: [0,1,2,3]
[dalvik.vm.dex2oat-max-image-block-size]: [524288]
[dalvik.vm.dex2oat-minidebuginfo]: [true]
[dalvik.vm.dex2oat-resolve-startup-strings]: [true]
[dalvik.vm.dex2oat-threads]: [4]
[dalvik.vm.dex2oat64.enabled]: [true]
[dalvik.vm.dexopt.secondary]: [true]
[dalvik.vm.dexopt.thermal-cutoff]: [2]
[dalvik.vm.enable_pr_dexopt]: [true]
[dalvik.vm.finalizer-timeout-ms]: [30000]
[dalvik.vm.heapgrowthlimit]: [256m]
[dalvik.vm.heapmaxfree]: [16m]
[dalvik.vm.heapminfree]: [2m]
[dalvik.vm.heapsize]: [512m]
[dalvik.vm.heapstartsize]: [8m]
[dalvik.vm.heaptargetutilization]: [0.6]
[dalvik.vm.image-dex2oat-Xms]: [64m]
[dalvik.vm.image-dex2oat-Xmx]: [64m]
[dalvik.vm.isa.arm.features]: [default]
[dalvik.vm.isa.arm.variant]: [cortex-a55]
[dalvik.vm.isa.arm64.features]: [default]
[dalvik.vm.isa.arm64.variant]: [cortex-a75]
[dalvik.vm.lockprof.threshold]: [500]
[dalvik.vm.madvise.artfile.size]: [4294967295]
[dalvik.vm.madvise.odexfile.size]: [104857600]
[dalvik.vm.madvise.vdexfile.size]: [104857600]
[dalvik.vm.minidebuginfo]: [true]
[dalvik.vm.thread-suspend-timeout-ms]: [40000]
[dalvik.vm.usap_pool_enabled]: [true]
[dalvik.vm.usap_pool_refill_delay_ms]: [3000]
[dalvik.vm.usap_pool_size_max]: [4]
[dalvik.vm.usap_pool_size_min]: [1]
[dalvik.vm.usap_refill_threshold]: [1]
[dalvik.vm.useartservice]: [true]
[dalvik.vm.usejit]: [true]
[debug.atrace.tags.enableflags]: [0]
[debug.fwk.enable_adpf_cpu_hint]: [false]
[debug.hwui.skia_tracing_enabled]: [false]
[debug.hwui.skia_use_perfetto_track_events]: [false]
[debug.hwui.use_hint_manager]: [true]
[debug.perfetto.sdk_sysprop_guard_generation]: [0]
[debug.renderengine.backend]: [skiaglthreaded]
[debug.renderengine.skia_tracing_enabled]: [false]
[debug.renderengine.skia_use_perfetto_track_events]: [false]
[debug.sf.auto_latch_unsignaled]: [false]
[debug.sf.enable_adpf_cpu_hint]: [true]
[debug.sf.enable_changezorder_flag]: [1]
[debug.sf.enable_gl_backpressure]: [false]
[debug.sf.enable_gpu_security]: [1]
[debug.sf.enable_skip_rotation]: [1]
[debug.sf.high_fps_late_app_phase_offset_ns]: [-4000000]
[debug.sf.high_fps_late_sf_phase_offset_ns]: [-3000000]
[debug.sf.hwc.min.duration]: [3000000]
[debug.sf.treat_170m_as_sRGB]: [1]
[debug.stagefright.c2inputsurface]: [-1]
[dev.mnt.blk.blackbox]: [mmcblk0p48]
[dev.mnt.blk.cache]: [mmcblk0p47]
[dev.mnt.blk.data]: [mmcblk0p74]
[dev.mnt.blk.data.user.0]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.cur_profiles]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_ce.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_ce.null.0]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.data_de.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.misc_ce.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.misc_de.null]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.ref_profiles]: [mmcblk0p74]
[dev.mnt.blk.data_mirror.storage_area]: [mmcblk0p74]
[dev.mnt.blk.metadata]: [mmcblk0p51]
[dev.mnt.blk.mnt.vendor]: [mmcblk0p1]
[dev.mnt.blk.odm]: [mmcblk0p46]
[dev.mnt.blk.product]: [mmcblk0p46]
[dev.mnt.blk.root]: [mmcblk0p46]
[dev.mnt.blk.system_dlkm]: [mmcblk0p46]
[dev.mnt.blk.system_ext]: [mmcblk0p46]
[dev.mnt.blk.vendor]: [mmcblk0p46]
[dev.mnt.blk.vendor_dlkm]: [mmcblk0p46]
[dev.mnt.dev.blackbox]: [mmcblk0p48]
[dev.mnt.dev.cache]: [mmcblk0p47]
[dev.mnt.dev.data]: [dm-50]
[dev.mnt.dev.data.user.0]: [dm-50]
[dev.mnt.dev.data_mirror.cur_profiles]: [dm-50]
[dev.mnt.dev.data_mirror.data_ce.null]: [dm-50]
[dev.mnt.dev.data_mirror.data_ce.null.0]: [dm-50]
[dev.mnt.dev.data_mirror.data_de.null]: [dm-50]
[dev.mnt.dev.data_mirror.misc_ce.null]: [dm-50]
[dev.mnt.dev.data_mirror.misc_de.null]: [dm-50]
[dev.mnt.dev.data_mirror.ref_profiles]: [dm-50]
[dev.mnt.dev.data_mirror.storage_area]: [dm-50]
[dev.mnt.dev.metadata]: [mmcblk0p51]
[dev.mnt.dev.mnt.vendor]: [mmcblk0p1]
[dev.mnt.dev.odm]: [dm-10]
[dev.mnt.dev.product]: [dm-11]
[dev.mnt.dev.root]: [dm-7]
[dev.mnt.dev.system_dlkm]: [dm-13]
[dev.mnt.dev.system_ext]: [dm-8]
[dev.mnt.dev.vendor]: [dm-9]
[dev.mnt.dev.vendor_dlkm]: [dm-12]
[dev.mnt.rootdisk.blackbox]: [mmcblk0]
[dev.mnt.rootdisk.cache]: [mmcblk0]
[dev.mnt.rootdisk.data]: [mmcblk0]
[dev.mnt.rootdisk.data.user.0]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.cur_profiles]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_ce.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_ce.null.0]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.data_de.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.misc_ce.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.misc_de.null]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.ref_profiles]: [mmcblk0]
[dev.mnt.rootdisk.data_mirror.storage_area]: [mmcblk0]
[dev.mnt.rootdisk.metadata]: [mmcblk0]
[dev.mnt.rootdisk.mnt.vendor]: [mmcblk0]
[dev.mnt.rootdisk.odm]: [mmcblk0]
[dev.mnt.rootdisk.product]: [mmcblk0]
[dev.mnt.rootdisk.root]: [mmcblk0]
[dev.mnt.rootdisk.system_dlkm]: [mmcblk0]
[dev.mnt.rootdisk.system_ext]: [mmcblk0]
[dev.mnt.rootdisk.vendor]: [mmcblk0]
[dev.mnt.rootdisk.vendor_dlkm]: [mmcblk0]
[graphics.gpu.profiler.support]: [true]
[gsm.client.base]: [android-tcl]
[init.svc.aconfigd]: [stopped]
[init.svc.aconfigd-mainline-init]: [stopped]
[init.svc.aconfigd-platform-init]: [stopped]
[init.svc.apexd]: [running]
[init.svc.apexd-bootstrap]: [stopped]
[init.svc.apexd-snapshotde]: [stopped]
[init.svc.art_boot]: [stopped]
[init.svc.boringssl_self_test32]: [stopped]
[init.svc.boringssl_self_test32_vendor]: [stopped]
[init.svc.boringssl_self_test64]: [stopped]
[init.svc.boringssl_self_test64_vendor]: [stopped]
[init.svc.bpfloader]: [stopped]
[init.svc.console]: [running]
[init.svc.create_splloader_dual_slot_byname_path]: [stopped]
[init.svc.derive_classpath]: [stopped]
[init.svc.derive_sdk]: [stopped]
[init.svc.hidl_memory]: [running]
[init.svc.hwservicemanager]: [running]
[init.svc.insmod-sh]: [stopped]
[init.svc.keystore2]: [running]
[init.svc.lmkd]: [running]
[init.svc.logd]: [running]
[init.svc.logd-reinit]: [stopped]
[init.svc.media.unisoc.codec2]: [running]
[init.svc.miscdata_hal_service]: [running]
[init.svc.netd]: [running]
[init.svc.odsign]: [stopped]
[init.svc.prng_seeder]: [running]
[init.svc.servicemanager]: [running]
[init.svc.srmi_proxyd]: [restarting]
[init.svc.statsd]: [running]
[init.svc.system_suspend]: [running]
[init.svc.tombstoned]: [running]
[init.svc.ueventd]: [running]
[init.svc.update_verifier]: [stopped]
[init.svc.vndservicemanager]: [running]
[init.svc.vold]: [running]
[init.svc.watchdogd]: [stopped]
[init.svc.ylog]: [running]
[init.svc.zygote]: [running]
[init.svc.zygote_secondary]: [running]
[log.tag.bluetooth]: [debug]
[log.tag.stats_log]: [I]
[logd.ready]: [true]
[net.bt.name]: [Android]
[oem_trusted_authority]: [com.sprd.android.USCPhotosProvider.providers.SpecialTypesProvider]
[oem_trusted_certificate]: [27196E386B875E76ADF700E7EA84E4C6EEE33DFA]
[partition.odm.verified.check_at_most_once]: [0]
[partition.product.verified.check_at_most_once]: [0]
[partition.system.verified.check_at_most_once]: [0]
[partition.system_dlkm.verified]: [2]
[partition.system_dlkm.verified.check_at_most_once]: [0]
[partition.system_dlkm.verified.hash_alg]: [sha256]
[partition.system_dlkm.verified.root_digest]: [c9da1069a9e966d816a2b8269f3b614d4140fdda92b0dd49221e1cb00e89b662]
[partition.system_ext.verified.check_at_most_once]: [0]
[partition.vendor.verified.check_at_most_once]: [0]
[partition.vendor_dlkm.verified]: [2]
[partition.vendor_dlkm.verified.check_at_most_once]: [0]
[partition.vendor_dlkm.verified.hash_alg]: [sha256]
[partition.vendor_dlkm.verified.root_digest]: [a420d369fb4b7b19ac2bd4e3a82fc1771291fc40a0b9f81199eb23d1a181d0d2]
[persist.audio.bigvolume.enabled]: [false]
[persist.audio.bigvolume.switch]: [false]
[persist.dbg.keep_debugfs_mounted]: [true]
[persist.debug.collection.sum]: [2]
[persist.debug.dalvik.vm.core_platform_api_policy]: [just-warn]
[persist.debug.dump_perfetto]: [true]
[persist.debug.half_anr_dump]: [true]
[persist.debug.leakdetector.enabled]: [true]
[persist.device_config.aconfig_flags.accessibility.enable_magnifier_thumbnail]: [false]
[persist.device_config.aconfig_flags.activity_manager_native_boot.modern_queue_enabled]: [true]
[persist.device_config.aconfig_flags.bluetooth.INIT_gd_hal_snoop_logger_filtering]: [true]
[persist.device_config.aconfig_flags.bluetooth.INIT_gd_hal_snoop_logger_socket]: [true]
[persist.device_config.aconfig_flags.bluetooth.audio_policy_ag_enabled]: [true]
[persist.device_config.aconfig_flags.bluetooth.audio_policy_hf_enabled]: [true]
[persist.device_config.aconfig_flags.bluetooth.le_audio_enabled_by_default]: [false]
[persist.device_config.aconfig_flags.bluetooth.location_denylist_advertising_data]: [⊈0016AAFE40/00FFFFFFF0,⊆0016AAFE/00FFFFFF,⊆00FF4C0002/00FFFFFFFF]
[persist.device_config.aconfig_flags.bluetooth.location_denylist_mac]: []
[persist.device_config.aconfig_flags.bluetooth.location_denylist_name]: []
[persist.device_config.aconfig_flags.bluetooth.scan_timeout_millis]: [300000]
[persist.device_config.aconfig_flags.codec_fwk.com.android.media.codec.flags.aidl_hal]: [true]
[persist.device_config.aconfig_flags.companion.enable_context_sync_telecom]: [false]
[persist.device_config.aconfig_flags.configuration.beta_launch]: [false]
[persist.device_config.aconfig_flags.configuration.beta_public_launch]: [false]
[persist.device_config.aconfig_flags.configuration.demo_flag]: [false]
[persist.device_config.aconfig_flags.configuration.droidfood_launch]: [false]
[persist.device_config.aconfig_flags.configuration.flag]: [true]
[persist.device_config.aconfig_flags.configuration.flag_five]: [false]
[persist.device_config.aconfig_flags.configuration.flag_four]: [false]
[persist.device_config.aconfig_flags.configuration.flag_six]: [false]
[persist.device_config.aconfig_flags.configuration.flag_three]: [false]
[persist.device_config.aconfig_flags.configuration.flag_two]: [false]
[persist.device_config.aconfig_flags.configuration.public_launch]: [false]
[persist.device_config.aconfig_flags.configuration.rescue_party_throttle_duration_min]: [10]
[persist.device_config.aconfig_flags.connectivity.data_stall_consecutive_dns_timeout_threshold]: [5]
[persist.device_config.aconfig_flags.connectivity.dhcp_init_reboot_enabled]: [false]
[persist.device_config.aconfig_flags.connectivity.dhcp_init_reboot_version]: [0]
[persist.device_config.aconfig_flags.connectivity.dhcp_ip_conflict_detect_version]: [0]
[persist.device_config.aconfig_flags.connectivity.dhcp_rapid_commit_enabled]: [false]
[persist.device_config.aconfig_flags.connectivity.dhcp_rapid_commit_version]: [1]
[persist.device_config.aconfig_flags.connectivity.dhcp_restart_configuration_delay]: [1000]
[persist.device_config.aconfig_flags.connectivity.dhcp_server_decline_version]: [0]
[persist.device_config.aconfig_flags.connectivity.ipclient_accept_ipv6_link_local_dns_version]: [0]
[persist.device_config.aconfig_flags.connectivity.ipclient_multicast_ns_version]: [0]
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app11]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app12]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app4]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app5]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app6]: []
[persist.device_config.aconfig_flags.edgetpu_native.allowlist_app7]: []
[persist.device_config.aconfig_flags.lmkd_native.thrashing_limit_critical]: [300]
[persist.device_config.aconfig_flags.nearby.nearby_enable_presence_broadcast_legacy]: [false]
[persist.device_config.aconfig_flags.nearby.nearby_nano_app_min_version]: [1]
[persist.device_config.aconfig_flags.nearby.nearby_support_test_app]: [false]
[persist.device_config.aconfig_flags.netd_native.dns_event_subsample_map]: [default:6 0:400 2:110 4:110 7:110]
[persist.device_config.aconfig_flags.netd_native.doh]: [1]
[persist.device_config.aconfig_flags.netd_native.dot_connect_timeout_ms]: [30000]
[persist.device_config.aconfig_flags.netd_native.dot_query_timeout_ms]: [-1]
[persist.device_config.aconfig_flags.netd_native.dot_revalidation_threshold]: [-1]
[persist.device_config.aconfig_flags.netd_native.dot_validation_latency_factor]: [3]
[persist.device_config.aconfig_flags.netd_native.dot_validation_latency_offset_ms]: [100]
[persist.device_config.aconfig_flags.netd_native.dot_xport_unusable_threshold]: [-1]
[persist.device_config.aconfig_flags.netd_native.max_cache_entries]: [640]
[persist.device_config.aconfig_flags.netd_native.max_queries_global]: [2500]
[persist.device_config.aconfig_flags.netd_native.no_retry_after_cancel]: [0]
[persist.device_config.aconfig_flags.netd_native.parallel_lookup]: [0]
[persist.device_config.aconfig_flags.netd_native.sort_nameservers]: [0]
[persist.device_config.aconfig_flags.nnapi_native.current_feature_level]: [7]
[persist.device_config.aconfig_flags.nnapi_native.telemetry_enable]: [false]
[persist.device_config.aconfig_flags.remote_key_provisioning_native.enable_rkpd]: [false]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-mods]: [2]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-mods-server]: [2]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-num-mods]: [100]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-num-mods-server]: [100]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-spec]: [1,5,30,60,600]
[persist.device_config.aconfig_flags.runtime_native.metrics.reporting-spec-server]: [1,10,60,3600,*]
[persist.device_config.aconfig_flags.runtime_native.metrics.write-to-statsd]: [true]
[persist.device_config.aconfig_flags.runtime_native.use_app_image_startup_cache]: [true]
[persist.device_config.aconfig_flags.runtime_native_boot.disable_lock_profiling]: [false]
[persist.device_config.aconfig_flags.runtime_native_boot.iorap_blacklisted_packages]: []
[persist.device_config.aconfig_flags.runtime_native_boot.iorap_perfetto_enable]: [false]
[persist.device_config.aconfig_flags.runtime_native_boot.iorap_readahead_enable]: [false]
[persist.device_config.aconfig_flags.runtime_native_boot.iorapd_options]: []
[persist.device_config.aconfig_flags.runtime_native_boot.use_generational_gc]: [true]
[persist.device_config.aconfig_flags.storage_native_boot.charging_required]: [false]
[persist.device_config.aconfig_flags.storage_native_boot.dirty_reclaim_rate]: [0.5]
[persist.device_config.aconfig_flags.storage_native_boot.fuse_enabled]: [1]
[persist.device_config.aconfig_flags.storage_native_boot.lifetime_threshold]: [70]
[persist.device_config.aconfig_flags.storage_native_boot.low_battery_level]: [20.0]
[persist.device_config.aconfig_flags.storage_native_boot.min_gc_sleeptime]: [5000]
[persist.device_config.aconfig_flags.storage_native_boot.min_segments_threshold]: [512]
[persist.device_config.aconfig_flags.storage_native_boot.segment_reclaim_weight]: [2.0]
[persist.device_config.aconfig_flags.storage_native_boot.smart_idle_maint_enabled]: [true]
[persist.device_config.aconfig_flags.storage_native_boot.smart_idle_maint_period]: [60]
[persist.device_config.aconfig_flags.storage_native_boot.take_over_get_content]: [false]
[persist.device_config.aconfig_flags.storage_native_boot.target_dirty_ratio]: [80]
[persist.device_config.aconfig_flags.storage_native_boot.transcode_compat_stale]: []
[persist.device_config.aconfig_flags.surface_flinger_native_boot.SkiaTracingFeature__use_skia_tracing]: [false]
[persist.device_config.runtime_native.metrics.reporting-mods]: [2]
[persist.device_config.runtime_native.metrics.reporting-mods-server]: [2]
[persist.device_config.runtime_native.metrics.reporting-num-mods]: [100]
[persist.device_config.runtime_native.metrics.reporting-num-mods-server]: [100]
[persist.device_config.runtime_native.metrics.reporting-spec]: [1,5,30,60,600]
[persist.device_config.runtime_native.metrics.reporting-spec-server]: [1,10,60,3600,*]
[persist.device_config.runtime_native.metrics.write-to-statsd]: [true]
[persist.device_config.runtime_native.use_app_image_startup_cache]: [true]
[persist.device_config.runtime_native_boot.disable_lock_profiling]: [false]
[persist.device_config.runtime_native_boot.iorap_blacklisted_packages]: []
[persist.device_config.runtime_native_boot.iorap_perfetto_enable]: [false]
[persist.device_config.runtime_native_boot.iorap_readahead_enable]: [false]
[persist.device_config.runtime_native_boot.iorapd_options]: []
[persist.device_config.runtime_native_boot.use_generational_gc]: [true]
[persist.log.tag.GnssConfiguration]: [V]
[persist.log.tag.GnssLocationProvider]: [V]
[persist.log.tag.GnssManager]: [V]
[persist.log.tag.GnssNetworkConnectivityHandler]: [V]
[persist.log.tag.GnssVisibilityControl]: [V]
[persist.log.tag.LocationManagerService]: [V]
[persist.log.tag.NtpTimeHelper]: [V]
[persist.log.tag.bluetooth]: [debug]
[persist.netmon.linger]: [20000]
[persist.nhmonitor.enable]: [on]
[persist.radio.multisim.config]: [dsds]
[persist.radio.psregstate]: [0,0]
[persist.storage.type]: [2]
[persist.sys.3d.calibraion]: [1]
[persist.sys.anti_aging.aging_state]: [1]
[persist.sys.apr.autoupload]: [1]
[persist.sys.apr.cp2version]: [WCN_TRUNK_22A_W24.38.3|uww2631_qogirL6|....|09-18-2024 16:26:32@sync]
[persist.sys.apr.cpversion]: [4G_MODEM_22B_W24.36.3|qogirl6_modem|09-04-2024 16:33:58@sync]
[persist.sys.apr.enabled]: [1]
[persist.sys.apr.exceptionnode]: [0]
[persist.sys.apr.gpsversion]: [GPS_GLO GE2_W17.40.5@sync]
[persist.sys.apr.intervaltime]: [1]
[persist.sys.apr.lifetime]: [7445]
[persist.sys.apr.radiomode]: [TL_LF_W_G,TL_LF_W_G@sync]
[persist.sys.apr.reload]: [7425]
[persist.sys.apr.reportlevel]: [0]
[persist.sys.apr.rlchanged]: [800]
[persist.sys.apr.testgroup]: [CSSLAB]
[persist.sys.apr.timechanged]: [180]
[persist.sys.audio.source]: [true]
[persist.sys.bl.clearuserdata]: [true]
[persist.sys.cam3.multi.cam.id]: [2]
[persist.sys.cam3.type]: [back_blur]
[persist.sys.choreographer.activity_cold_start_insert_frame]: [true]
[persist.sys.choreographer.fling_insert_frame]: [true]
[persist.sys.choreographer.pre_animation_load]: [false]
[persist.sys.dalvik.vm.lib.2]: [libart.so]
[persist.sys.displayinset.top]: [0]
[persist.sys.engineer.enabled]: [false]
[persist.sys.extrainfo]: []
[persist.sys.firstboot]: [DONE]
[persist.sys.firstboot_complete]: [1]
[persist.sys.fuse]: [true]
[persist.sys.fuse.passthrough.enable]: [true]
[persist.sys.gms]: [1]
[persist.sys.heartbeat.enable]: [1]
[persist.sys.lmk.reportkills]: [true]
[persist.sys.locale]: [en-US]
[persist.sys.log.yloglite]: [0]
[persist.sys.navbar.overlay]: [false]
[persist.sys.power.touch]: [1]
[persist.sys.pq.cabc.enabled]: [1]
[persist.sys.pq.dci.enabled]: [1]
[persist.sys.pq.enabled]: [1]
[persist.sys.private_features.enable]: [1]
[persist.sys.pwctl.appidle]: [1]
[persist.sys.pwctl.appidle.force]: [1]
[persist.sys.pwctl.appstats]: [0]
[persist.sys.pwctl.bgclean]: [1]
[persist.sys.pwctl.enable]: [1]
[persist.sys.pwctl.gps]: [1]
[persist.sys.pwctl.gps.onlysave]: [0]
[persist.sys.pwctl.guru]: [1]
[persist.sys.pwctl.onlysave]: [1]
[persist.sys.pwctl.wl]: [1]
[persist.sys.sdcardfs]: [force_on]
[persist.sys.sf.boostpolicy]: [6]
[persist.sys.sf.color_saturation]: [1.0]
[persist.sys.special_datestr]: [W25.60.1]
[persist.sys.ss.enable]: [true]
[persist.sys.ss.habit]: [true]
[persist.sys.ss.hmm]: [true]
[persist.sys.ss.predict]: [false]
[persist.sys.ss.scene]: [true]
[persist.sys.ss.scroll]: [false]
[persist.sys.ss.sr.enable]: [true]
[persist.sys.ss.track]: [true]
[persist.sys.ss.uhc.enable]: [true]
[persist.sys.support.antenna]: [false]
[persist.sys.support.typeC]: [true]
[persist.sys.support.vram]: [true]
[persist.sys.support.vramselect]: [false]
[persist.sys.support.vt]: [true]
[persist.sys.thermal.hightempkiller]: [1]
[persist.sys.time.offset]: [28800000]
[persist.sys.timezone]: [Asia/Shanghai]
[persist.sys.unievent.enabled]: [1]
[persist.sys.unisoc_delayanimationfinish]: [true]
[persist.sys.unisoc_dyn_insert_frame]: [true]
[persist.sys.unisoc_game_boost]: [true]
[persist.sys.unisoc_smart_animation]: [true]
[persist.sys.usb.config]: [adb]
[persist.sys.vilte.socket]: [ap]
[persist.sys.vram_alter_enable]: [false]
[persist.sys.vramenable]: [true]
[persist.sys.vramsize]: [4096M]
[persist.sys.vramstoragelifetime]: [0]
[persist.sys.vramversion]: [3.0]
[persist.sys.wfc.supp_dual_sim]: [true]
[persist.sys.wifi.reset.devpath]: [devices/platform/87000000.cpwcn-btwf/87000000.cpwcn-btwf:sprd-wlan]
[persist.sys.ylog.hcidump]: [1]
[persist.sys.ylog.tcpdump]: [3]
[persist.vendor.sys.core.enabled]: [1]
[persist.vendor.sys.isfirstboot]: [0]
[persist.vendor.sys.modem.diag]: [none]
[persist.vendor.sys.modem.reboot]: [0xff]
[persist.vendor.sys.modem.save_dump]: [1]
[persist.vendor.sys.modemreset]: [0]
[persist.vendor.sys.single.imsstack]: [true]
[persist.vendor.sys.sp.save_dump]: [1]
[persist.vendor.sys.volte.enable]: [true]
[persist.vendor.sys.wcnreset]: [0]
[persist.vendor.sys.wcnstate]: [1]
[persist.wm.extensions.enabled]: [true]
[persist.zygote.core_dump]: [1]
[pm.dexopt.ab-ota]: [speed-profile]
[pm.dexopt.bg-dexopt]: [speed-profile]
[pm.dexopt.boot-after-mainline-update]: [verify]
[pm.dexopt.boot-after-ota]: [verify]
[pm.dexopt.cmdline]: [verify]
[pm.dexopt.first-boot]: [verify]
[pm.dexopt.inactive]: [verify]
[pm.dexopt.install]: [speed-profile]
[pm.dexopt.install-bulk]: [speed-profile]
[pm.dexopt.install-bulk-downgraded]: [verify]
[pm.dexopt.install-bulk-secondary]: [verify]
[pm.dexopt.install-bulk-secondary-downgraded]: [verify]
[pm.dexopt.install-fast]: [skip]
[pm.dexopt.post-boot]: [verify]
[pm.dexopt.shared]: [speed]
[remote_provisioning.enable_rkpd]: [true]
[ro.actionable_compatible_property.enabled]: [true]
[ro.allow.mock.location]: [0]
[ro.apex.updatable]: [true]
[ro.appsflyer.preinstall.path]: [/system/etc/pre_install_tiktok.appsflyer]
[ro.audio.bigvolume.music_speaker]: [2]
[ro.audio.bigvolume.voice_earpiece]: [2]
[ro.audio.bigvolume.voice_speaker]: [2]
[ro.baseband]: [unknown]
[ro.bionic.2nd_arch]: [arm]
[ro.bionic.2nd_cpu_variant]: [cortex-a55]
[ro.bionic.arch]: [arm64]
[ro.bionic.cpu_variant]: [cortex-a75]
[ro.board.api_level]: [33]
[ro.board.first_api_level]: [33]
[ro.board.platform]: [ums9230]
[ro.boot.auto.chipid]: [UMS9230-AC]
[ro.boot.auto.efuse]: [UMS9230]
[ro.boot.avb_version]: [1.3]
[ro.boot.boot_devices]: [soc/soc:ap-apb/201d0000.sdio]
[ro.boot.carrier_group]: [OM]
[ro.boot.code]: [A601N]
[ro.boot.ddr_size]: [4096M]
[ro.boot.ddrsize]: [4096M]
[ro.boot.ddrsize.range]: [[4096,5120)]
[ro.boot.dpc]: [false]
[ro.boot.dswdten]: [enabled]
[ro.boot.dtbo_idx]: [11]
[ro.boot.dvfs_set]: [0x0,0,0]
[ro.boot.dynamic_partitions]: [true]
[ro.boot.ecid]: [25000000]
[ro.boot.fingerprint_support]: [0]
[ro.boot.flash.locked]: [1]
[ro.boot.force.user_adb]: [1]
[ro.boot.force_normal_boot]: [1]
[ro.boot.hardware]: [ums9230_6h10]
[ro.boot.init_fatal_panic]: [true]
[ro.boot.lwfq.type]: [1]
[ro.boot.mode]: [normal]
[ro.boot.nfc_support]: [1]
[ro.boot.odm_customized_name]: [A601N]
[ro.boot.payjoy]: [false]
[ro.boot.pcb_state]: [2]
[ro.boot.pmic.chipid]: [2730]
[ro.boot.product.hardware.sku]: [NFC_dualsim]
[ro.boot.project_name]: [A01]
[ro.boot.segment_efuse_sta]: [0]
[ro.boot.sim_count]: [2]
[ro.boot.slot_suffix]: [_a]
[ro.boot.tclsn]: [95382744705949]
[ro.boot.tclsn2]: [007439C20401D409]
[ro.boot.tct.platform]: [SPRD]
[ro.boot.vbmeta.avb_version]: [1.1]
[ro.boot.vbmeta.device]: [PARTUUID=1.0]
[ro.boot.vbmeta.device_state]: [locked]
[ro.boot.vbmeta.digest]: [3f3e4eb949abdeb27e0f7e2fb699f00278315cda19a4f00ab19b80453cde979e]
[ro.boot.vbmeta.hash_alg]: [sha256]
[ro.boot.vbmeta.size]: [51008]
[ro.boot.vendor.skip.init]: [0]
[ro.boot.verifiedbootstate]: [green]
[ro.boot.veritymode]: [enforcing]
[ro.boot.veritymode.managed]: [yes]
[ro.boot.wdten]: [e551]
[ro.bootimage.build.description]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.bootimage.build.display.id]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.bootloader]: [unknown]
[ro.bootmode]: [normal]
[ro.build.ab_update]: [true]
[ro.build.backported_fixes.alias_bitset.long_list]: [2]
[ro.build.characteristics]: [default]
[ro.build.date]: [Mon Aug  4 19:17:55 CST 2025]
[ro.build.date.utc]: [1754306275]
[ro.build.description]: [ums9230_6h10_Natv-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.build.display.id]: [ums9230_6h10_Natv-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0111:userdebug/test-keys]
[ro.build.flavor]: [ussi_arm64_full-userdebug]
[ro.build.host]: [pc2fdhqh]
[ro.build.id]: [AP3A.240905.015.A2]
[ro.build.product]: [ussi_arm64]
[ro.build.tags]: [test-keys]
[ro.build.type]: [userdebug]
[ro.build.user]: [hmt]
[ro.build.version.all_codenames]: [REL]
[ro.build.version.base_os]: []
[ro.build.version.codename]: [REL]
[ro.build.version.incremental]: [A0111]
[ro.build.version.known_codenames]: [Base,Base11,Cupcake,Donut,Eclair,Eclair01,EclairMr1,Froyo,Gingerbread,GingerbreadMr1,Honeycomb,HoneycombMr1,HoneycombMr2,IceCreamSandwich,IceCreamSandwichMr1,JellyBean,JellyBeanMr1,JellyBeanMr2,Kitkat,KitkatWatch,Lollipop,LollipopMr1,M,N,NMr1,O,OMr1,P,Q,R,S,Sv2,Tiramisu,UpsideDownCake,VanillaIceCream]
[ro.build.version.min_supported_target_sdk]: [28]
[ro.build.version.preview_sdk]: [0]
[ro.build.version.preview_sdk_fingerprint]: [REL]
[ro.build.version.release]: [15]
[ro.build.version.release_or_codename]: [15]
[ro.build.version.release_or_preview_display]: [15]
[ro.build.version.sdk]: [35]
[ro.build.version.security_patch]: [2025-06-05]
[ro.carrier]: [oversea]
[ro.com.android.dataroaming]: [false]
[ro.com.google.clientidbase.ms]: [android-tcl-gep1]
[ro.com.google.clientidbase.vs]: [android-tcl-gep1]
[ro.com.google.gmsversion]: [15_202503]
[ro.com.google.lens.oem_camera_package]: [com.android.camera2]
[ro.com.google.lens.oem_image_package]: [com.google.android.apps.photos]
[ro.config.alarm_alert]: [Atmospheric_Forest-default.mp3]
[ro.config.alarm_vol_default]: [13]
[ro.config.alarm_vol_steps]: [15]
[ro.config.isolated_compilation_enabled]: [true]
[ro.config.media_vol_default]: [13]
[ro.config.media_vol_steps]: [15]
[ro.config.notification_sound]: [Paint.mp3]
[ro.config.ringtone]: [Bloom.mp3,Bloom.mp3]
[ro.config.system_vol_default]: [13]
[ro.config.system_vol_steps]: [15]
[ro.config.vc_call_vol_default]: [5]
[ro.config.vc_call_vol_steps]: [7]
[ro.crypto.metadata.enabled]: [true]
[ro.crypto.state]: [encrypted]
[ro.crypto.type]: [file]
[ro.dalvik.vm.enable_uffd_gc]: [true]
[ro.dalvik.vm.native.bridge]: [0]
[ro.debuggable]: [1]
[ro.ecid]: [25000000]
[ro.force.debuggable]: [0]
[ro.frp.pst]: [/dev/block/by-name/persist]
[ro.hardware]: [ums9230_6h10]
[ro.hardware.audio.primary]: [ums9230]
[ro.hardware.camera]: [unisoc]
[ro.hardware.egl]: [mali]
[ro.hardware.hwcomposer]: [unisoc]
[ro.hardware.sensors]: [unisoc]
[ro.hw_timeout_multiplier]: [2]
[ro.hwui.use_vulkan]: [true]
[ro.kernel.version]: [5.15]
[ro.launcher.desktopgrid]: [true]
[ro.launcher.dynamic]: [false]
[ro.launcher.multimode]: [true]
[ro.launcher.notifbadge.count]: [true]
[ro.llndk.api_level]: [202404]
[ro.lmk.filecache_min_kb]: [153600]
[ro.lmk.kill_timeout_ms]: [200]
[ro.lmk.psi_complete_stall_ms]: [500]
[ro.lmk.stall_limit_critical]: [40]
[ro.lmk.swap_compression_ratio]: [0]
[ro.lmk.swap_free_low_percentage]: [20]
[ro.logd.kernel]: [true]
[ro.logd.size.stats]: [64K]
[ro.media.recoderEIS.enabled]: [true]
[ro.media.wfd.rgb.enabled]: [true]
[ro.odm.build.date]: [Mon Aug  4 19:17:50 CST 2025]
[ro.odm.build.date.utc]: [1754306270]
[ro.odm.build.description]: [ums9230_6h10_Natv-userdebug 13 TP1A.220624.014 601NA0111 release-keys]
[ro.odm.build.display.id]: [ums9230_6h10_Natv-userdebug 13 TP1A.220624.014 601NA0111 release-keys]
[ro.odm.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0111:userdebug/release-keys]
[ro.odm.build.version.incremental]: [A0111]
[ro.odm.def.ota.ver]: [601NA0111]
[ro.oem.key1]: [25000000]
[ro.opa.eligible_device]: [true]
[ro.opengles.version]: [196610]
[ro.postinstall.fstab.prefix]: [/product]
[ro.product.assistanttouch]: [false]
[ro.product.board]: [A601N]
[ro.product.brand]: [Alcatel]
[ro.product.build.date]: [Mon Aug  4 19:17:53 CST 2025]
[ro.product.build.date.utc]: [1754306273]
[ro.product.build.description]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.product.build.display.id]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.product.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0111:userdebug/release-keys]
[ro.product.build.id]: [AP3A.240905.015.A2]
[ro.product.build.tags]: [release-keys]
[ro.product.build.type]: [userdebug]
[ro.product.build.version.incremental]: [A0111]
[ro.product.build.version.release]: [15]
[ro.product.build.version.release_or_codename]: [15]
[ro.product.build.version.sdk]: [35]
[ro.product.cpu.abi]: [arm64-v8a]
[ro.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.product.cpu.abilist64]: [arm64-v8a]
[ro.product.cpu.pagesize.max]: [16384]
[ro.product.device]: [A01]
[ro.product.first_api_level]: [35]
[ro.product.locale]: [en-US]
[ro.product.manufacturer]: [TCL]
[ro.product.model]: [A601N]
[ro.product.name]: [A601N]
[ro.product.odm.brand]: [Alcatel]
[ro.product.odm.device]: [A01]
[ro.product.odm.manufacturer]: [TCL]
[ro.product.odm.model]: [A601N]
[ro.product.odm.name]: [A601N]
[ro.product.product.brand]: [Alcatel]
[ro.product.product.device]: [A01]
[ro.product.product.manufacturer]: [TCL]
[ro.product.product.model]: [A601N]
[ro.product.product.name]: [A601N]
[ro.product.publicname]: [Alcatel A62]
[ro.product.system.brand]: [Alcatel]
[ro.product.system.device]: [A01]
[ro.product.system.manufacturer]: [TCL]
[ro.product.system.model]: [A601N]
[ro.product.system.name]: [A601N]
[ro.product.system_dlkm.brand]: [Alcatel]
[ro.product.system_dlkm.device]: [A01]
[ro.product.system_dlkm.manufacturer]: [TCL]
[ro.product.system_dlkm.model]: [A601N]
[ro.product.system_dlkm.name]: [A601N]
[ro.product.system_ext.brand]: [Alcatel]
[ro.product.system_ext.device]: [A01]
[ro.product.system_ext.manufacturer]: [TCL]
[ro.product.system_ext.model]: [A601N]
[ro.product.system_ext.name]: [A601N]
[ro.product.tcl.dumysuffix]: [DMY]
[ro.product.vendor.brand]: [Alcatel]
[ro.product.vendor.device]: [A01]
[ro.product.vendor.manufacturer]: [TCL]
[ro.product.vendor.model]: [A601N]
[ro.product.vendor.name]: [A601N]
[ro.product.vendor.odm]: [true]
[ro.product.vendor_dlkm.brand]: [Alcatel]
[ro.product.vendor_dlkm.device]: [A01]
[ro.product.vendor_dlkm.manufacturer]: [TCL]
[ro.product.vendor_dlkm.model]: [A601N]
[ro.product.vendor_dlkm.name]: [A601N]
[ro.property_service.version]: [2]
[ro.revision]: [0]
[ro.secure]: [1]
[ro.secure_boot.state]: [1]
[ro.setupwizard.rotation_locked]: [true]
[ro.sf.lcd_density]: [260]
[ro.simlock.onekey.lock]: [0]
[ro.simlock.unlock.autoshow]: [1]
[ro.simlock.unlock.bynv]: [0]
[ro.soc.manufacturer]: [Spreadtrum]
[ro.soc.model]: [T606]
[ro.sprd.pwctl.ultra.message]: [1]
[ro.sprd.superresolution]: [1]
[ro.sr.displaysize.defaultresolution]: [0]
[ro.sr.displaysize.lowresolution]: [1]
[ro.sr.tp_screen_off]: [true]
[ro.support_one_handed_mode]: [true]
[ro.surface_flinger.force_hwc_copy_for_virtual_displays]: [true]
[ro.surface_flinger.game_default_frame_rate_override]: [60]
[ro.surface_flinger.has_HDR_display]: [false]
[ro.surface_flinger.has_wide_color_display]: [false]
[ro.surface_flinger.max_frame_buffer_acquired_buffers]: [3]
[ro.surface_flinger.max_virtual_display_dimension]: [4096]
[ro.surface_flinger.present_time_offset_from_vsync_ns]: [0]
[ro.surface_flinger.primary_display_orientation]: [ORIENTATION_0]
[ro.surface_flinger.protected_contents]: [true]
[ro.surface_flinger.running_without_sync_framework]: [false]
[ro.surface_flinger.set_display_power_timer_ms]: [1000]
[ro.surface_flinger.set_idle_timer_ms]: [4000]
[ro.surface_flinger.set_touch_timer_ms]: [200]
[ro.surface_flinger.start_graphics_allocator_service]: [false]
[ro.surface_flinger.use_content_detection_for_refresh_rate]: [true]
[ro.surface_flinger.use_context_priority]: [true]
[ro.surface_flinger.use_vr_flinger]: [false]
[ro.surface_flinger.vsync_event_phase_offset_ns]: [1000000]
[ro.surface_flinger.vsync_sf_event_phase_offset_ns]: [1000000]
[ro.sys.pwctl.ultrasaving]: [1]
[ro.system.build.date]: [Mon Aug  4 19:17:55 CST 2025]
[ro.system.build.date.utc]: [1754306275]
[ro.system.build.description]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.system.build.display.id]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.system.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0111:userdebug/release-keys]
[ro.system.build.id]: [AP3A.240905.015.A2]
[ro.system.build.tags]: [release-keys]
[ro.system.build.type]: [userdebug]
[ro.system.build.version.incremental]: [A0111]
[ro.system.build.version.release]: [15]
[ro.system.build.version.release_or_codename]: [15]
[ro.system.build.version.sdk]: [35]
[ro.system.component.label]: [SYSTEM-Android15--U1.0-W25.31.3]
[ro.system.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.system.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.system.product.cpu.abilist64]: [arm64-v8a]
[ro.system_dlkm.build.date]: [Mon Aug  4 19:17:53 CST 2025]
[ro.system_dlkm.build.date.utc]: [1754306273]
[ro.system_dlkm.build.description]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.system_dlkm.build.display.id]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.system_dlkm.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0111:userdebug/release-keys]
[ro.system_dlkm.build.id]: [AP3A.240905.015.A2]
[ro.system_dlkm.build.tags]: [release-keys]
[ro.system_dlkm.build.type]: [userdebug]
[ro.system_dlkm.build.version.incremental]: [A0111]
[ro.system_dlkm.build.version.release]: [15]
[ro.system_dlkm.build.version.release_or_codename]: [15]
[ro.system_dlkm.build.version.sdk]: [35]
[ro.system_ext.build.date]: [Wed Aug  6 16:51:42 CST 2025]
[ro.system_ext.build.date.utc]: [1754470302]
[ro.system_ext.build.description]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.system_ext.build.display.id]: [ussi_arm_go-userdebug 15 AP3A.240905.015.A2 601NA0111 release-keys]
[ro.system_ext.build.fingerprint]: [Alcatel/A601N/A01:15/AP3A.240905.015.A2/A0111:userdebug/release-keys]
[ro.system_ext.build.id]: [AP3A.240905.015.A2]
[ro.system_ext.build.tags]: [release-keys]
[ro.system_ext.build.type]: [userdebug]
[ro.system_ext.build.version.incremental]: [A0111]
[ro.system_ext.build.version.release]: [15]
[ro.system_ext.build.version.release_or_codename]: [15]
[ro.system_ext.build.version.sdk]: [35]
[ro.tct.trace.bsn]: [95382744705949]
[ro.telephony.default_network]: [9]
[ro.treble.enabled]: [true]
[ro.unipnp.switch]: [true]
[ro.vendor.api_level]: [33]
[ro.vendor.arm.egl.configs.nv12.hal_format]: [0x100]
[ro.vendor.arm.egl.configs.nv12.recordable]: [true]
[ro.vendor.arm.egl.configs.nv16.hal_format]: [0x10]
[ro.vendor.arm.egl.configs.nv16.recordable]: [true]
[ro.vendor.arm.egl.configs.nv21.hal_format]: [0x101]
[ro.vendor.arm.egl.configs.nv21.recordable]: [true]
[ro.vendor.arm.egl.configs.p010.hal_format]: [0x104]
[ro.vendor.arm.egl.configs.p010.recordable]: [true]
[ro.vendor.arm.egl.configs.p210.hal_format]: [0x105]
[ro.vendor.arm.egl.configs.p210.recordable]: [true]
[ro.vendor.arm.egl.configs.q410.hal_format]: [0x10a]
[ro.vendor.arm.egl.configs.q410.recordable]: [true]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.hal_format]: [0x2b]
[ro.vendor.arm.egl.configs.r10_g10_b10_a2_32bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.hal_format]: [0x16]
[ro.vendor.arm.egl.configs.r16_g16_b16_a16_64bit_float.recordable]: [false]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.framebuffer_target]: [true]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.hal_format]: [0x4]
[ro.vendor.arm.egl.configs.r5_g6_b5_a0_16bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.hal_format]: [0x0]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_yuv_special.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_24bit_yuv_special.hal_format]: [0x23]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.framebuffer_target]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.hal_format]: [0x2]
[ro.vendor.arm.egl.configs.r8_g8_b8_a0_32bit_fixed.recordable]: [false]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.framebuffer_target]: [true]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.hal_format]: [0x1]
[ro.vendor.arm.egl.configs.r8_g8_b8_a8_32bit_fixed.recordable]: [true]
[ro.vendor.arm.egl.configs.y0l2.hal_format]: [0x103]
[ro.vendor.arm.egl.configs.y0l2.recordable]: [true]
[ro.vendor.arm.egl.configs.y210.hal_format]: [0x106]
[ro.vendor.arm.egl.configs.y210.recordable]: [true]
[ro.vendor.arm.egl.configs.y410.hal_format]: [0x107]
[ro.vendor.arm.egl.configs.y410.recordable]: [true]
[ro.vendor.arm.egl.configs.yuv420.hal_format]: [0x108]
[ro.vendor.arm.egl.configs.yuv420.recordable]: [true]
[ro.vendor.arm.egl.configs.yuyv.hal_format]: [0x102]
[ro.vendor.arm.egl.configs.yuyv.recordable]: [true]
[ro.vendor.arm.egl.configs.yvu420.hal_format]: [0x10e]
[ro.vendor.arm.egl.configs.yvu420.recordable]: [true]
[ro.vendor.arm.gralloc.afrc_chroma_usage_flags]: [0x100000000000000,0x80000000000000,0x180000000000000]
[ro.vendor.arm.gralloc.afrc_chroma_usage_mask]: [0x180000000000000]
[ro.vendor.arm.gralloc.afrc_luma_usage_flags]: [0x50000000,0x60000000,0x40000000]
[ro.vendor.arm.gralloc.afrc_luma_usage_mask]: [0x70000000]
[ro.vendor.arm.gralloc.afrc_rgba_usage_flags]: [0x50000000,0x60000000,0x40000000]
[ro.vendor.arm.gralloc.afrc_rgba_usage_mask]: [0x70000000]
[ro.vendor.arm.gralloc.force_back_buffer_usage_flags]: [0x40000000000000]
[ro.vendor.arm.gralloc.force_back_buffer_usage_mask]: [0x40000000000000]
[ro.vendor.arm.gralloc.no_afbc_usage_flags]: [0x20000000]
[ro.vendor.arm.gralloc.no_afbc_usage_mask]: [0x60000000]
[ro.vendor.arm.gralloc.shared_access_usage_flags]: [0x10000000]
[ro.vendor.arm.gralloc.shared_access_usage_mask]: [0x50000000]
[ro.vendor.build.date]: [Mon Aug  4 19:17:49 CST 2025]
[ro.vendor.build.date.utc]: [1754306269]
[ro.vendor.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0111:userdebug/release-keys]
[ro.vendor.build.id]: [TP1A.220624.014]
[ro.vendor.build.tags]: [release-keys]
[ro.vendor.build.type]: [userdebug]
[ro.vendor.build.version.incremental]: [A0111]
[ro.vendor.build.version.release]: [13]
[ro.vendor.build.version.release_or_codename]: [13]
[ro.vendor.build.version.sdk]: [33]
[ro.vendor.product.cpu.abilist]: [arm64-v8a,armeabi-v7a,armeabi]
[ro.vendor.product.cpu.abilist32]: [armeabi-v7a,armeabi]
[ro.vendor.product.cpu.abilist64]: [arm64-v8a]
[ro.vendor.version.release]: [601NA0111]
[ro.vendor_dlkm.build.date]: [Mon Aug  4 19:17:50 CST 2025]
[ro.vendor_dlkm.build.date.utc]: [1754306270]
[ro.vendor_dlkm.build.fingerprint]: [Alcatel/A601N/A01:13/TP1A.220624.014/A0111:userdebug/release-keys]
[ro.vendor_dlkm.build.id]: [TP1A.220624.014]
[ro.vendor_dlkm.build.tags]: [release-keys]
[ro.vendor_dlkm.build.type]: [userdebug]
[ro.vendor_dlkm.build.version.incremental]: [A0111]
[ro.vendor_dlkm.build.version.release]: [13]
[ro.vendor_dlkm.build.version.release_or_codename]: [13]
[ro.vendor_dlkm.build.version.sdk]: [33]
[ro.vndk.version]: [33]
[ro.wifi.channels]: []
[ro.zygote]: [zygote64_32]
[security.perf_harden]: [1]
[service.wait_for_bootanim]: [1]
[servicemanager.ready]: [true]
[setupwizard.theme]: [glif_v3_light]
[sys.internal.emulated]: [1]
[sys.lmk.reportkills]: [1]
[sys.log.bootimes]: [1]
[sys.log.wbootimes]: [1]
[sys.usb.configfs]: [0]
[sys.usb.controller]: [musb-hdrc.1.auto]
[sys.usb.mode]: [normal]
[sys.use_memfd]: [false]
[sys.wifitracing.started]: [0]
[sys.ylog.bootimes]: [1]
[sys.ylog.file]: [/data/ylog/ap/003-0807_113535_poweron.ylog]
[sys.ylog.fwc]: []
[sys.ylog.path]: [/data/ylog/ap/]
[sys.ylog.version]: [5.0.0]
[sys.ylog.wbootimes]: [1]
[vold.has_adoptable]: [1]
[vold.has_compress]: [0]
[vold.has_quota]: [1]
[vold.has_reserved]: [1]


ylogctl q
Thu Aug  7 11:35:36 CST 2025
status = enable 
file = /data/ylog/ap/003-0807_113535_poweron.ylog 
size = 655737 
pid =  515  
[   530]  lastlog      -> Open    -> lastlog.log      [     29]->[      5] 
[   535]  uboot        -> Open    -> uboot.log        [    100]->[     40] 
[   536]  android      -> Open    -> android.log      [     14]->[      8] 
[   539]  kernel       -> Open    -> kernel.log       [     3B]->[      8] 
[   540]  trace        -> Open    -> trace.log        [      0]->[      0] 
[   541]  sgm          -> Open    -> sgm.csv          [     11]->[      2] 
[   544]  sysinfo      -> Open    -> sysinfo.log      [     17]->[      5] 
[     0]  thermal      -> Close   -> thermal.log      [      0]->[      0] 
[   549]  ylogdebug    -> Open    -> ylogdebug.log    [     20]->[      8] 
[   555]  phoneinfo    -> Open    -> phoneinfo.log    [     76]->[      8] 
[   560]  hcidump      -> Open    -> hcidump.cap      [      0]->[      0] 
[   564]  tcpdump      -> Open    -> tcpdump.cap      [      0]->[      0] 
[   572]  trustlog     -> Open    -> trusty.log       [      6]->[      2] 

phoneinfo end
poweron start
Thu Aug  7 11:36:36 CST 2025


uptime
Thu Aug  7 11:36:36 CST 2025
 11:36:36 up 1 min,  0 users,  load average: 27.79, 7.94, 2.73


the log file is 
Thu Aug  7 11:36:36 CST 2025


getprop>/data/ylog/phone.info
Thu Aug  7 11:36:37 CST 2025


chmod 0777 /data/ylog/phone.info
Thu Aug  7 11:36:37 CST 2025
